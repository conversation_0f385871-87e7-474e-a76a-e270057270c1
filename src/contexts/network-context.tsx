'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { WalletAdapterNetwork } from '@solana/wallet-adapter-base';
import { clusterApiUrl } from '@solana/web3.js';

// Network configuration
export const SOLANA_NETWORKS = {
  'mainnet-beta': {
    name: 'Mainnet Beta',
    url: process.env.NEXT_PUBLIC_SOLANA_RPC_URL || clusterApiUrl('mainnet-beta'),
    chainId: 101,
    displayName: 'Mainnet',
    color: 'green',
  },
  devnet: {
    name: 'Devnet',
    url: process.env.NEXT_PUBLIC_SOLANA_DEVNET_RPC_URL || clusterApiUrl('devnet'),
    chainId: 103,
    displayName: 'Devnet',
    color: 'orange',
  },
  testnet: {
    name: 'Testnet',
    url: process.env.NEXT_PUBLIC_SOLANA_TESTNET_RPC_URL || clusterApiUrl('testnet'),
    chainId: 102,
    displayName: 'Testnet',
    color: 'blue',
  },
} as const;

export type NetworkType = keyof typeof SOLANA_NETWORKS;

interface NetworkContextType {
  currentNetwork: NetworkType;
  setNetwork: (network: NetworkType) => void;
  networkConfig: typeof SOLANA_NETWORKS[NetworkType];
  availableNetworks: NetworkType[];
  isMainnet: boolean;
  isDevnet: boolean;
  isTestnet: boolean;
}

const NetworkContext = createContext<NetworkContextType | undefined>(undefined);

interface NetworkProviderProps {
  children: ReactNode;
  defaultNetwork?: NetworkType;
}

export function NetworkProvider({ children, defaultNetwork }: NetworkProviderProps) {
  // Get initial network from environment or localStorage
  const getInitialNetwork = (): NetworkType => {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('buddychip-solana-network') as NetworkType;
      if (saved && saved in SOLANA_NETWORKS) {
        return saved;
      }
    }
    
    // Fallback to environment variable or default
    const envNetwork = process.env.NEXT_PUBLIC_SOLANA_NETWORK as NetworkType;
    if (envNetwork && envNetwork in SOLANA_NETWORKS) {
      return envNetwork;
    }
    
    return defaultNetwork || 'devnet';
  };

  const [currentNetwork, setCurrentNetwork] = useState<NetworkType>(getInitialNetwork);

  // Save network preference to localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('buddychip-solana-network', currentNetwork);
    }
  }, [currentNetwork]);

  const setNetwork = (network: NetworkType) => {
    console.log('🔄 Switching Solana network:', {
      from: currentNetwork,
      to: network,
      config: SOLANA_NETWORKS[network]
    });
    setCurrentNetwork(network);
  };

  const networkConfig = SOLANA_NETWORKS[currentNetwork];
  const availableNetworks: NetworkType[] = Object.keys(SOLANA_NETWORKS) as NetworkType[];

  const contextValue: NetworkContextType = {
    currentNetwork,
    setNetwork,
    networkConfig,
    availableNetworks,
    isMainnet: currentNetwork === 'mainnet-beta',
    isDevnet: currentNetwork === 'devnet',
    isTestnet: currentNetwork === 'testnet',
  };

  return (
    <NetworkContext.Provider value={contextValue}>
      {children}
    </NetworkContext.Provider>
  );
}

export function useNetwork() {
  const context = useContext(NetworkContext);
  if (context === undefined) {
    throw new Error('useNetwork must be used within a NetworkProvider');
  }
  return context;
}

// Helper function to get WalletAdapterNetwork from our NetworkType
export function getWalletAdapterNetwork(network: NetworkType): WalletAdapterNetwork {
  switch (network) {
    case 'mainnet-beta':
      return WalletAdapterNetwork.Mainnet;
    case 'devnet':
      return WalletAdapterNetwork.Devnet;
    case 'testnet':
      return WalletAdapterNetwork.Testnet;
    default:
      return WalletAdapterNetwork.Devnet;
  }
}

// Helper function to get network display info
export function getNetworkDisplayInfo(network: NetworkType) {
  const config = SOLANA_NETWORKS[network];
  return {
    name: config.displayName,
    color: config.color,
    chainId: config.chainId,
    url: config.url,
  };
}

// Helper function to check if network is production
export function isProductionNetwork(network: NetworkType): boolean {
  return network === 'mainnet-beta';
}

// Helper function to get appropriate token addresses for network
export function getTokenAddressForNetwork(network: NetworkType) {
  // In production, you'd have different token addresses for different networks
  const addresses = {
    'mainnet-beta': {
      COPY_TOKEN: process.env.NEXT_PUBLIC_COPY_TOKEN_MINT_ADDRESS_MAINNET || 'CopyMainnetMint1111111111111111111111111111',
      USDC: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', // USDC on mainnet
    },
    devnet: {
      COPY_TOKEN: process.env.NEXT_PUBLIC_COPY_TOKEN_MINT_ADDRESS || 'CopyAiTokenMint1111111111111111111111111111',
      USDC: '4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU', // USDC on devnet
    },
    testnet: {
      COPY_TOKEN: process.env.NEXT_PUBLIC_COPY_TOKEN_MINT_ADDRESS_TESTNET || 'CopyTestnetMint1111111111111111111111111111',
      USDC: '4zMMC9srt5Ri5X14GAgXhaHii3GnPAEERYPJgZJDncDU', // USDC on testnet
    },
  };

  return addresses[network];
}
