/**
 * Rate Limiting Middleware
 * 
 * Provides comprehensive rate limiting for API routes with different strategies:
 * - IP-based rate limiting
 * - User-based rate limiting
 * - Endpoint-specific limits
 * - Sliding window algorithm
 */

import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/utils/logger';

const rateLimitLogger = logger.child({ component: 'rate-limit' });

// In-memory store for rate limiting (use Redis in production)
const rateLimitStore = new Map<string, { requests: number[]; lastReset: number }>();

// Rate limit configurations for different endpoints
export const RATE_LIMIT_CONFIGS = {
  // AI Generation endpoints (expensive operations)
  'copy-ai-generate': { limit: 5, windowMs: 60 * 1000 }, // 5 requests per minute
  'ai-generate-image': { limit: 3, windowMs: 60 * 1000 }, // 3 requests per minute
  'ai-search-agent': { limit: 10, windowMs: 60 * 1000 }, // 10 requests per minute
  'ai-analyze-tweet': { limit: 20, windowMs: 60 * 1000 }, // 20 requests per minute
  
  // Data fetching endpoints
  'twitter-ingest': { limit: 1, windowMs: 15 * 60 * 1000 }, // 1 request per 15 minutes
  'twitter-analyzed-tweets': { limit: 30, windowMs: 60 * 1000 }, // 30 requests per minute
  
  // Authentication endpoints
  'auth-login': { limit: 5, windowMs: 15 * 60 * 1000 }, // 5 attempts per 15 minutes
  'auth-signup': { limit: 3, windowMs: 60 * 60 * 1000 }, // 3 attempts per hour
  
  // General API endpoints
  'default': { limit: 100, windowMs: 15 * 60 * 1000 }, // 100 requests per 15 minutes
  
  // Chatbot endpoints
  'ai-search-chatbot': { limit: 20, windowMs: 60 * 1000 }, // 20 messages per minute
  
  // Personality analysis
  'personality-analyze': { limit: 2, windowMs: 60 * 60 * 1000 }, // 2 requests per hour
} as const;

export type RateLimitConfig = keyof typeof RATE_LIMIT_CONFIGS;

interface RateLimitOptions {
  limit?: number;
  windowMs?: number;
  keyGenerator?: (req: NextRequest) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  onLimitReached?: (req: NextRequest, key: string) => void;
}

/**
 * Creates a rate limiting function for API routes
 */
export function createRateLimit(
  configName: RateLimitConfig,
  options: RateLimitOptions = {}
) {
  const config = RATE_LIMIT_CONFIGS[configName];
  const limit = options.limit || config.limit;
  const windowMs = options.windowMs || config.windowMs;
  
  return function rateLimit(req: NextRequest): { allowed: boolean; remaining: number; resetTime: number } {
    const key = options.keyGenerator ? options.keyGenerator(req) : getDefaultKey(req);
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get or create rate limit data for this key
    let rateLimitData = rateLimitStore.get(key);
    if (!rateLimitData) {
      rateLimitData = { requests: [], lastReset: now };
      rateLimitStore.set(key, rateLimitData);
    }
    
    // Remove expired requests (sliding window)
    rateLimitData.requests = rateLimitData.requests.filter(time => time > windowStart);
    
    // Check if limit is exceeded
    const currentRequests = rateLimitData.requests.length;
    const allowed = currentRequests < limit;
    
    if (allowed) {
      // Add current request timestamp
      rateLimitData.requests.push(now);
      rateLimitStore.set(key, rateLimitData);
    } else {
      // Rate limit exceeded
      if (options.onLimitReached) {
        options.onLimitReached(req, key);
      }
      
      rateLimitLogger.warn('Rate limit exceeded', {
        key,
        configName,
        currentRequests,
        limit,
        windowMs,
        ip: getClientIP(req),
        userAgent: req.headers.get('user-agent'),
      });
    }
    
    // Calculate remaining requests and reset time
    const remaining = Math.max(0, limit - currentRequests - (allowed ? 1 : 0));
    const oldestRequest = rateLimitData.requests[0];
    const resetTime = oldestRequest ? oldestRequest + windowMs : now + windowMs;
    
    return {
      allowed,
      remaining,
      resetTime,
    };
  };
}

/**
 * Middleware wrapper for API routes
 */
export function withRateLimit(
  configName: RateLimitConfig,
  handler: (req: NextRequest) => Promise<NextResponse>,
  options: RateLimitOptions = {}
) {
  const rateLimit = createRateLimit(configName, options);
  
  return async function rateLimitedHandler(req: NextRequest): Promise<NextResponse> {
    const result = rateLimit(req);
    
    if (!result.allowed) {
      const retryAfter = Math.ceil((result.resetTime - Date.now()) / 1000);
      
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          message: `Too many requests. Try again in ${retryAfter} seconds.`,
          retryAfter,
        },
        {
          status: 429,
          headers: {
            'X-RateLimit-Limit': RATE_LIMIT_CONFIGS[configName].limit.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': result.resetTime.toString(),
            'Retry-After': retryAfter.toString(),
          },
        }
      );
    }
    
    // Add rate limit headers to successful responses
    const response = await handler(req);
    
    response.headers.set('X-RateLimit-Limit', RATE_LIMIT_CONFIGS[configName].limit.toString());
    response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
    response.headers.set('X-RateLimit-Reset', result.resetTime.toString());
    
    return response;
  };
}

/**
 * Get client IP address from request
 */
function getClientIP(req: NextRequest): string {
  const forwarded = req.headers.get('x-forwarded-for');
  const realIP = req.headers.get('x-real-ip');
  const cfConnectingIP = req.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  return req.ip || 'unknown';
}

/**
 * Default key generator (IP + User Agent hash)
 */
function getDefaultKey(req: NextRequest): string {
  const ip = getClientIP(req);
  const userAgent = req.headers.get('user-agent') || 'unknown';
  
  // Simple hash function for user agent
  let hash = 0;
  for (let i = 0; i < userAgent.length; i++) {
    const char = userAgent.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return `${ip}:${hash}`;
}

/**
 * User-based key generator (requires authentication)
 */
export function createUserKeyGenerator(fallbackToIP = true) {
  return function userKeyGenerator(req: NextRequest): string {
    // Try to get user ID from request (you'll need to implement this based on your auth)
    const userId = req.headers.get('x-user-id'); // Adjust based on your auth implementation
    
    if (userId) {
      return `user:${userId}`;
    }
    
    if (fallbackToIP) {
      return getDefaultKey(req);
    }
    
    return 'anonymous';
  };
}

/**
 * Cleanup expired entries (call periodically)
 */
export function cleanupRateLimitStore() {
  const now = Date.now();
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours
  
  for (const [key, data] of rateLimitStore.entries()) {
    if (now - data.lastReset > maxAge) {
      rateLimitStore.delete(key);
    }
  }
  
  rateLimitLogger.info('Rate limit store cleanup completed', {
    remainingEntries: rateLimitStore.size,
  });
}

// Cleanup every hour
if (typeof setInterval !== 'undefined') {
  setInterval(cleanupRateLimitStore, 60 * 60 * 1000);
}
