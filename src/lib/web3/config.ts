import { clusterApiUrl, Connection } from '@solana/web3.js';

// Solana network configuration
export const SOLANA_NETWORKS = {
  mainnet: {
    name: 'Mainnet Beta',
    url: process.env.NEXT_PUBLIC_SOLANA_RPC_URL || clusterApiUrl('mainnet-beta'),
    chainId: 101,
  },
  devnet: {
    name: 'Devnet',
    url: process.env.NEXT_PUBLIC_SOLANA_DEVNET_RPC_URL || clusterApiUrl('devnet'),
    chainId: 103,
  },
  testnet: {
    name: 'Testnet',
    url: process.env.NEXT_PUBLIC_SOLANA_TESTNET_RPC_URL || clusterApiUrl('testnet'),
    chainId: 102,
  },
} as const;

// Current network (default to devnet for development)
export const CURRENT_NETWORK = process.env.NEXT_PUBLIC_SOLANA_NETWORK || 'devnet';

// Solana connection
export const solanaConnection = new Connection(
  SOLANA_NETWORKS[CURRENT_NETWORK as keyof typeof SOLANA_NETWORKS].url,
  'confirmed'
);

console.log('🔗 Solana connection initialized:', {
  network: CURRENT_NETWORK,
  url: SOLANA_NETWORKS[CURRENT_NETWORK as keyof typeof SOLANA_NETWORKS].url,
});

// Contract addresses (Solana mint addresses)
export const CONTRACTS = {
  COPY_TOKEN: process.env.NEXT_PUBLIC_COPY_TOKEN_MINT_ADDRESS || 'CopyAiTokenMint1111111111111111111111111111',
  NFT_COLLECTION: process.env.NEXT_PUBLIC_NFT_COLLECTION_ADDRESS!,
  STAKING_PROGRAM: process.env.NEXT_PUBLIC_STAKING_PROGRAM_ADDRESS!,
} as const;

// Copy.AI pricing configuration
export const COPY_CONFIG = {
  GENERATION_COST: parseFloat(process.env.COPY_GENERATION_COST || '0.5'),
  STAKING_MINIMUM: parseFloat(process.env.COPY_STAKING_MINIMUM || '100'),
  THEMES: {
    MEME: 'meme',
    DEGEN: 'degen',
    VC_BAIT: 'vc_bait',
    GENERAL: 'general',
  },
  VIRAL_SCORE_WEIGHTS: {
    LIKES: 1,
    RETWEETS: 3,
    REPLIES: 2,
    QUOTES: 4,
  },
} as const;

// IPFS configuration
export const IPFS_CONFIG = {
  API_URL: process.env.IPFS_API_URL || 'https://ipfs.infura.io:5001',
  API_KEY: process.env.IPFS_API_KEY,
  API_SECRET: process.env.IPFS_API_SECRET,
  GATEWAY_URL: 'https://ipfs.io/ipfs/',
} as const;

// Chain configuration
export const CHAIN_CONFIG = {
  CHAIN_ID: parseInt(process.env.NEXT_PUBLIC_CHAIN_ID || '8453'),
  RPC_URL: process.env.NEXT_PUBLIC_RPC_URL || 'https://mainnet.base.org',
} as const;
