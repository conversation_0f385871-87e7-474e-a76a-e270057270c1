import { createSupabaseServerClient } from '@/lib/supabase/server';
import { logger } from '@/lib/utils/logger';

// Import and re-export types from the client-safe types file
import {
  TokenAction,
  TokenUsagePolicy,
  UsageTransaction,
  UsageAnalytics,
  DEFAULT_TOKEN_COSTS,
  ACTION_DESCRIPTIONS,
} from '@/lib/web3/token-usage-types';

export {
  TokenAction,
  TokenUsagePolicy,
  UsageTransaction,
  UsageAnalytics,
  DEFAULT_TOKEN_COSTS,
  ACTION_DESCRIPTIONS,
};

const usageLogger = logger.child({ component: 'TokenUsageService' });

/**
 * Token Usage Service - Handles all token spending logic
 */
export class TokenUsageService {
  /**
   * Get the cost for a specific action
   */
  static async getActionCost(actionType: TokenAction): Promise<number> {
    try {
      usageLogger.debug('Getting action cost', { actionType });

      const supabase = await createSupabaseServerClient();

      const { data, error } = await supabase
        .from('buddychip_token_usage_policies')
        .select('cost_amount')
        .eq('action_type', actionType)
        .eq('is_active', true)
        .single();

      if (error) {
        usageLogger.error('Error fetching action cost', { actionType, error });
        return 0; // Return 0 if action not found or error
      }

      const cost = parseFloat(data.cost_amount || '0');
      usageLogger.debug('Action cost retrieved', { actionType, cost });

      return cost;
    } catch (error) {
      usageLogger.error('Error in getActionCost', { actionType, error });
      return 0;
    }
  }

  /**
   * Check if user can afford a specific action
   */
  static async canAffordAction(userId: string, actionType: TokenAction): Promise<boolean> {
    try {
      usageLogger.debug('Checking if user can afford action', { userId, actionType });

      const supabase = await createSupabaseServerClient();

      // Use the database function for atomic check
      const { data, error } = await supabase
        .rpc('can_afford_action', {
          p_user_id: userId,
          p_action_type: actionType
        });

      if (error) {
        usageLogger.error('Error checking affordability', { userId, actionType, error });
        return false;
      }

      usageLogger.debug('Affordability check result', { userId, actionType, canAfford: data });
      return data === true;
    } catch (error) {
      usageLogger.error('Error in canAffordAction', { userId, actionType, error });
      return false;
    }
  }

  /**
   * Deduct tokens for an action (with transaction safety)
   */
  static async deductTokensForAction(transaction: UsageTransaction): Promise<void> {
    const supabase = await createSupabaseServerClient();

    usageLogger.info('Starting token deduction', {
      userId: transaction.userId,
      actionType: transaction.actionType,
      costAmount: transaction.costAmount
    });

    try {
      // Start a database transaction
      const { data: currentBalance, error: balanceError } = await supabase
        .from('buddychip_copy_tokens')
        .select('balance, total_spent')
        .eq('user_id', transaction.userId)
        .single();

      if (balanceError || !currentBalance) {
        throw new Error('Failed to fetch current balance');
      }

      const newBalance = parseFloat(currentBalance.balance) - transaction.costAmount;
      const newTotalSpent = parseFloat(currentBalance.total_spent || '0') + transaction.costAmount;

      if (newBalance < 0) {
        throw new Error('Insufficient balance');
      }

      // Update balance
      const { error: updateError } = await supabase
        .from('buddychip_copy_tokens')
        .update({
          balance: newBalance,
          total_spent: newTotalSpent,
        })
        .eq('user_id', transaction.userId);

      if (updateError) {
        throw new Error('Failed to update balance');
      }

      // Record transaction
      const { error: transactionError } = await supabase
        .from('buddychip_token_transactions')
        .insert({
          user_id: transaction.userId,
          transaction_type: 'action_cost',
          amount: -transaction.costAmount, // Negative for spending
          action_type: transaction.actionType,
          action_metadata: transaction.metadata || {},
          related_tweet_id: transaction.relatedEntityId,
          description: transaction.description || `${transaction.actionType} action cost`,
        });

      if (transactionError) {
        usageLogger.error('Failed to record transaction', { transaction, transactionError });
        // Note: Balance was already updated, so we log this but don't throw
      }

      usageLogger.info('Token deduction completed successfully', {
        userId: transaction.userId,
        actionType: transaction.actionType,
        costAmount: transaction.costAmount,
        newBalance,
        newTotalSpent
      });

    } catch (error) {
      usageLogger.error('Error in deductTokensForAction', { transaction, error });
      throw new Error(`Failed to deduct tokens: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get all active token usage policies
   */
  static async getAllPolicies(): Promise<TokenUsagePolicy[]> {
    try {
      const supabase = await createSupabaseServerClient();

      const { data, error } = await supabase
        .from('buddychip_token_usage_policies')
        .select('*')
        .eq('is_active', true)
        .order('action_type');

      if (error) {
        usageLogger.error('Error fetching policies', { error });
        return [];
      }

      return data.map(policy => ({
        id: policy.id,
        actionType: policy.action_type,
        costAmount: parseFloat(policy.cost_amount),
        description: policy.description,
        isActive: policy.is_active,
        createdAt: policy.created_at,
        updatedAt: policy.updated_at,
      }));
    } catch (error) {
      usageLogger.error('Error in getAllPolicies', { error });
      return [];
    }
  }

  /**
   * Get user's usage history
   */
  static async getUserUsageHistory(
    userId: string,
    limit: number = 50
  ): Promise<UsageTransaction[]> {
    try {
      const supabase = await createSupabaseServerClient();

      const { data, error } = await supabase
        .from('buddychip_token_transactions')
        .select('*')
        .eq('user_id', userId)
        .eq('transaction_type', 'action_cost')
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        usageLogger.error('Error fetching usage history', { userId, error });
        return [];
      }

      return data.map(tx => ({
        userId: tx.user_id,
        actionType: tx.action_type as TokenAction,
        costAmount: Math.abs(parseFloat(tx.amount)), // Convert back to positive
        metadata: tx.action_metadata || {},
        relatedEntityId: tx.related_tweet_id,
        description: tx.description,
      }));
    } catch (error) {
      usageLogger.error('Error in getUserUsageHistory', { userId, error });
      return [];
    }
  }
}
