/**
 * Token Usage Types - Client-Safe
 * 
 * This file contains only types and enums that can be safely imported
 * in both client and server components without causing build errors.
 */

/**
 * Enum for all possible token actions
 */
export enum TokenAction {
  TWEET_GENERATION = 'tweet_generation',
  CONTEXT_FETCH = 'context_fetch',
  TWEET_ANALYSIS = 'tweet_analysis',
  IMAGE_GENERATION = 'image_generation',
  BULK_TWEET_ANALYSIS = 'bulk_tweet_analysis',
  PREMIUM_THEME_ACCESS = 'premium_theme_access',
  VIRAL_SCORE_CALCULATION = 'viral_score_calculation',
  HASHTAG_SUGGESTIONS = 'hashtag_suggestions',
  ENGAGEMENT_PREDICTION = 'engagement_prediction',
  CONTENT_OPTIMIZATION = 'content_optimization',
}

/**
 * Interface for token usage policy
 */
export interface TokenUsagePolicy {
  id?: number;
  actionType: string;
  costAmount: number;
  description: string;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Interface for usage transaction
 */
export interface UsageTransaction {
  userId: string;
  actionType: TokenAction;
  costAmount: number;
  metadata?: Record<string, any>;
  relatedEntityId?: string;
  description?: string;
}

/**
 * Interface for usage analytics
 */
export interface UsageAnalytics {
  totalSpent: number;
  totalActions: number;
  actionBreakdown: Record<string, { count: number; totalCost: number }>;
  dailyUsage: Array<{ date: string; actions: number; cost: number }>;
  topActions: Array<{ actionType: string; count: number; percentage: number }>;
}

/**
 * Default token costs (fallback values)
 */
export const DEFAULT_TOKEN_COSTS: Record<TokenAction, number> = {
  [TokenAction.TWEET_GENERATION]: 0.5,
  [TokenAction.CONTEXT_FETCH]: 0.05,
  [TokenAction.TWEET_ANALYSIS]: 0.1,
  [TokenAction.IMAGE_GENERATION]: 1.0,
  [TokenAction.BULK_TWEET_ANALYSIS]: 0.5,
  [TokenAction.PREMIUM_THEME_ACCESS]: 2.0,
  [TokenAction.VIRAL_SCORE_CALCULATION]: 0.2,
  [TokenAction.HASHTAG_SUGGESTIONS]: 0.1,
  [TokenAction.ENGAGEMENT_PREDICTION]: 0.3,
  [TokenAction.CONTENT_OPTIMIZATION]: 0.4,
};

/**
 * Action descriptions for UI display
 */
export const ACTION_DESCRIPTIONS: Record<TokenAction, string> = {
  [TokenAction.TWEET_GENERATION]: 'Generate a viral tweet with AI',
  [TokenAction.CONTEXT_FETCH]: 'Fetch context from Twitter post or handle',
  [TokenAction.TWEET_ANALYSIS]: 'Analyze tweet relevance and engagement',
  [TokenAction.IMAGE_GENERATION]: 'Generate AI image for social media',
  [TokenAction.BULK_TWEET_ANALYSIS]: 'Analyze multiple tweets in batch',
  [TokenAction.PREMIUM_THEME_ACCESS]: 'Access to premium tweet themes',
  [TokenAction.VIRAL_SCORE_CALCULATION]: 'Calculate viral potential score',
  [TokenAction.HASHTAG_SUGGESTIONS]: 'Generate relevant hashtags',
  [TokenAction.ENGAGEMENT_PREDICTION]: 'Predict engagement metrics',
  [TokenAction.CONTENT_OPTIMIZATION]: 'Optimize content for virality',
};
