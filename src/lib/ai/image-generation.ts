/**
 * OpenAI Image Generation Service
 *
 * Implements image generation using OpenAI's new Responses API with gpt-4.1-mini
 * and the gpt-image-1 model for image generation. No DALL-E fallback.
 * Based on the latest OpenAI image generation documentation.
 */

import { OpenAI } from 'openai';
import { logger } from '@/lib/utils/logger';
import { enhanceImagePrompt, PromptEnhancementOptions, } from './prompt-enhancer';
import { Tool } from 'openai/resources/responses/responses.mjs';

const imageLogger = logger.child({ component: 'ImageGeneration' });

// Initialize OpenAI client with fallback
const openai = process.env.OPENAI_API_KEY ? new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
}) : null;

export interface ImageGenerationOptions {
  size?: '1024x1024' | '1024x1536' | '1536x1024' | 'auto';
  quality?: 'low' | 'medium' | 'high' | 'auto';
  format?: 'png' | 'jpeg' | 'webp';
  compression?: number; // 0-100% for JPEG and WebP
  background?: 'transparent' | 'opaque' | 'auto';
  previousResponseId?: string; // For multi-turn editing
  previousImageId?: string; // For multi-turn editing
  // Prompt enhancement options
  enhancePrompt?: boolean; // Whether to enhance the prompt with Gemini AI
  enhancementOptions?: PromptEnhancementOptions; // Options for prompt enhancement
}

export interface ImageGenerationResult {
  success: boolean;
  imageBase64?: string;
  revisedPrompt?: string;
  imageId?: string;
  responseId?: string;
  error?: string;
  metadata?: {
    originalPrompt: string;
    enhancedPrompt?: string; // Gemini-enhanced prompt
    revisedPrompt?: string; // OpenAI-revised prompt
    options: ImageGenerationOptions;
    generatedAt: string;
    model: string;
    enhancementInfo?: {
      wasEnhanced: boolean;
      improvements: string[];
      estimatedQuality: number;
      processingTime: number;
    };
  };
}

/**
 * Generate an image using OpenAI's image generation tool
 */
export async function generateImage(
  prompt: string,
  options: ImageGenerationOptions = {}
): Promise<ImageGenerationResult> {
  const requestId = `img-gen-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

  imageLogger.info('Starting image generation', {
    requestId,
    prompt: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : ''),
    options,
    willEnhancePrompt: options.enhancePrompt !== false // Default to true
  });

  try {
    if (!process.env.OPENAI_API_KEY || !openai) {
      throw new Error('OpenAI API key not configured');
    }

    // Step 1: Enhance the prompt with Gemini AI (default enabled)
    let finalPrompt = prompt;
    let enhancementInfo: {
      wasEnhanced: boolean;
      improvements: string[];
      estimatedQuality: number;
      processingTime: number;
    } | undefined = undefined;

    if (options.enhancePrompt !== false) { // Default to true
      try {
        imageLogger.info('Enhancing prompt with Gemini AI', { requestId, originalPrompt: prompt.substring(0, 100) });

        const enhancementResult = await enhanceImagePrompt(prompt, {
          contextType: options.enhancementOptions?.contextType || 'general',
          style: options.enhancementOptions?.style,
          mood: options.enhancementOptions?.mood,
          targetAudience: options.enhancementOptions?.targetAudience,
          contextContent: options.enhancementOptions?.contextContent
        });

        finalPrompt = enhancementResult.enhancedPrompt;
        enhancementInfo = {
          wasEnhanced: true,
          improvements: enhancementResult.improvements,
          estimatedQuality: enhancementResult.estimatedQuality,
          processingTime: enhancementResult.processingTime
        };

        imageLogger.info('Prompt enhanced successfully', {
          requestId,
          originalLength: prompt.length,
          enhancedLength: finalPrompt.length,
          estimatedQuality: enhancementResult.estimatedQuality
        });

        console.log('🎨 ImageGeneration: Prompt enhanced by Gemini', {
          original: prompt.substring(0, 50),
          enhanced: finalPrompt.substring(0, 100),
          improvements: enhancementResult.improvements.length,
          quality: enhancementResult.estimatedQuality
        });

      } catch (enhanceError) {
        imageLogger.warn('Prompt enhancement failed, using original prompt', {
          requestId,
          error: enhanceError instanceof Error ? enhanceError.message : 'Unknown error'
        });

        console.warn('⚠️ ImageGeneration: Prompt enhancement failed, using original', enhanceError);

        enhancementInfo = {
          wasEnhanced: false,
          improvements: ['Enhancement failed, used original prompt'],
          estimatedQuality: 5,
          processingTime: 0
        };
      }
    } else {
      enhancementInfo = {
        wasEnhanced: false,
        improvements: ['Enhancement disabled by user'],
        estimatedQuality: 5,
        processingTime: 0
      };
    }

    // Use ONLY the new OpenAI Responses API with gpt-image-1 model (no DALL-E fallback)
    let result: ImageGenerationResult;

    try {
      // Check if the responses API is available
      if (openai.responses && typeof openai.responses.create === 'function') {
        imageLogger.debug('Using OpenAI Responses API with gpt-4.1-mini', { requestId, prompt: prompt.substring(0, 100) });

        // Build the tool configuration with all available options
        const imageGenerationTool: Record<string, unknown> = {
          type: 'image_generation' as const,
        };

        // Add optional parameters - use 'auto' as default for best results
        if (options.size) {
          imageGenerationTool.size = options.size;
        }
        if (options.quality) {
          imageGenerationTool.quality = options.quality;
        }
        if (options.background) {
          imageGenerationTool.background = options.background;
        }
        if (options.format) {
          imageGenerationTool.format = options.format;
        }
        if (options.compression !== undefined) {
          imageGenerationTool.compression = options.compression;
        }

        const response = await openai.responses.create({
          model: 'gpt-4.1-mini',
          input: finalPrompt, // Use Gemini-enhanced prompt
          tools: [imageGenerationTool as unknown as Tool],
        });

        imageLogger.debug('OpenAI Responses API response received', {
          requestId,
          outputCount: response.output?.length || 0
        });

        // Extract image data from response
        if (!response.output || response.output.length === 0) {
          throw new Error('No output found in response');
        }

        // Find the image generation call in the output
        const imageGenerationCall = response.output.find(
          (output: { type: string }) => output.type === 'image_generation_call'
        ) as {
          type: string;
          status: string;
          result: string;
          revised_prompt: string;
          id: string;
        };

        if (!imageGenerationCall) {
          throw new Error('No image generation call found in response');
        }

        if (imageGenerationCall.status && imageGenerationCall.status !== 'completed') {
          throw new Error(`Image generation failed with status: ${imageGenerationCall.status}`);
        }

        if (!imageGenerationCall.result) {
          throw new Error('No image data found in generation call result');
        }

        result = {
          success: true,
          imageBase64: imageGenerationCall.result,
          revisedPrompt: imageGenerationCall.revised_prompt || undefined,
          imageId: imageGenerationCall.id || `img_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
          responseId: response.id || `req_${Date.now()}`,
          metadata: {
            originalPrompt: prompt,
            enhancedPrompt: finalPrompt !== prompt ? finalPrompt : undefined,
            revisedPrompt: imageGenerationCall.revised_prompt || undefined,
            options,
            generatedAt: new Date().toISOString(),
            model: 'gpt-image-1',
            enhancementInfo,
          },
        };
      } else {
        throw new Error('OpenAI Responses API not available - please ensure you have access to the latest OpenAI API');
      }
    } catch (responsesError) {
      // No fallback - only use the new OpenAI image generation model
      imageLogger.error('OpenAI Responses API failed - no fallback available', {
        requestId,
        error: responsesError instanceof Error ? responsesError.message : 'Unknown error'
      });

      throw new Error(`Image generation failed: ${responsesError instanceof Error ? responsesError.message : 'Unknown error'}`);
    }

    imageLogger.info('Image generation completed successfully', {
      requestId,
      imageId: result.imageId,
      revisedPrompt: result.revisedPrompt?.substring(0, 100) +
        (result.revisedPrompt && result.revisedPrompt.length > 100 ? '...' : ''),
    });

    return result;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;

    imageLogger.error('Image generation failed', {
      requestId,
      error: errorMessage,
      stack: errorStack,
      prompt: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : ''),
      options
    });

    return {
      success: false,
      error: errorMessage,
      metadata: {
        originalPrompt: prompt,
        options,
        generatedAt: new Date().toISOString(),
        model: 'gpt-image-1',
      },
    };
  }
}

/**
 * Generate an image with context from a conversation or tweet
 */
export async function generateImageWithContext(
  userQuery: string,
  context: {
    type: 'tweet' | 'conversation' | 'copy-ai' | 'reply';
    content: string;
    metadata?: Record<string, unknown>;
  },
  options: ImageGenerationOptions = {}
): Promise<ImageGenerationResult> {

  imageLogger.info('Generating image with context', {
    userQuery: userQuery.substring(0, 100) + (userQuery.length > 100 ? '...' : ''),
    contextType: context.type,
    contextLength: context.content.length
  });

  // Create an enhanced prompt that includes the context
  let enhancedPrompt = userQuery;

  switch (context.type) {
    case 'tweet':
      enhancedPrompt = `Create an image based on this request: "${userQuery}"

Context from tweet: "${context.content}"

Please create a visually appealing image that relates to both the user's request and the tweet content.`;
      break;

    case 'conversation':
      enhancedPrompt = `Create an image based on this request: "${userQuery}"

Context from conversation: "${context.content}"

Please create an image that captures the essence of the conversation and the user's specific request.`;
      break;

    case 'copy-ai':
      enhancedPrompt = `Create an image for a viral social media post based on: "${userQuery}"

Content context: "${context.content}"

Please create an engaging, shareable image that would work well with social media content.`;
      break;

    case 'reply':
      enhancedPrompt = `Create an image to accompany a reply based on: "${userQuery}"

Original content being replied to: "${context.content}"

Please create an image that would enhance the reply and be relevant to the conversation.`;
      break;
  }

  // Set up enhancement options based on context
  const enhancementOptions: ImageGenerationOptions = {
    ...options,
    enhancementOptions: {
      contextType: context.type,
      contextContent: context.content,
      targetAudience: context.type === 'copy-ai' ? 'social_media' : 'general',
      style: context.type === 'copy-ai' ? 'vibrant' : 'professional',
      ...options.enhancementOptions
    }
  };

  return generateImage(enhancedPrompt, enhancementOptions);
}

/**
 * Save generated image to file system (for development/testing)
 */
export async function saveImageToFile(
  imageBase64: string,
  filename: string
): Promise<boolean> {
  try {
    const fs = await import('fs');
    const buffer = Buffer.from(imageBase64, 'base64');
    fs.writeFileSync(filename, buffer);
    imageLogger.info('Image saved to file', { filename });
    return true;
  } catch (error) {
    imageLogger.error('Failed to save image to file', { filename, error });
    return false;
  }
}
