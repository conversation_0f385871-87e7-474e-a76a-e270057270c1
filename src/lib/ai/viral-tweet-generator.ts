import { callOpenRouter } from './openrouter-service';
import { logger } from '@/lib/utils/logger';

// Copy.AI themes configuration (moved here to avoid client-side imports in server code)
const COPY_CONFIG = {
  THEMES: {
    MEME: 'meme',
    DEGEN: 'degen',
    VC_BAIT: 'vc_bait',
    GENERAL: 'general',
  },
} as const;

const viralLogger = logger.child({ component: 'ViralTweetGenerator' });

export interface GeneratedTweet {
  content: string;
  theme: string;
  viralScore: number;
  hashtags: string[];
  estimatedEngagement: {
    likes: number;
    retweets: number;
    replies: number;
  };
}

interface ContextData {
  type: 'twitter_post' | 'twitter_handle';
  content: string;
  metadata?: {
    author?: string;
    engagement?: {
      likes: number;
      retweets: number;
      replies: number;
    };
    created_at?: string;
  };
}

/**
 * Generate a viral tweet based on prompt and theme
 */
export async function generateViralTweet(
  prompt: string,
  theme: string,
  requestId?: string,
  context?: ContextData
): Promise<GeneratedTweet> {
  viralLogger.info('Generating viral tweet', {
    prompt,
    theme,
    hasContext: !!context,
    contextType: context?.type,
    requestId
  });

  const systemPrompt = getSystemPromptForTheme(theme);
  const userPrompt = buildUserPrompt(prompt, theme, context);

  try {
    const response = await callOpenRouter(
      [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      'google/gemini-2.0-flash-exp', // Use latest Gemini model
      0.8, // Higher temperature for creativity
      280, // Twitter character limit
      requestId
    );

    const generatedTweet = parseGeneratedTweet(response, theme);

    viralLogger.info('Viral tweet generated successfully', {
      theme,
      contentLength: generatedTweet.content.length,
      requestId
    });

    return generatedTweet;
  } catch (error) {
    viralLogger.error('Error generating viral tweet', { prompt, theme, error, requestId });
    throw new Error('Failed to generate viral tweet');
  }
}

/**
 * Get system prompt based on theme
 */
function getSystemPromptForTheme(theme: string): string {
  const basePrompt = `You are a viral content creator specializing in generating highly engaging tweets. Your goal is to create content that maximizes engagement (likes, retweets, replies, quotes).

Key principles:
- Keep tweets under 280 characters
- Use compelling hooks in the first few words
- Include emotional triggers
- Make content shareable and quotable
- Add relevant hashtags (2-3 max)
- Include "Generated by Copium On-Chain" tag

`;

  switch (theme) {
    case COPY_CONFIG.THEMES.MEME:
      return basePrompt + `MEME MODE: Create humorous, relatable content that appeals to internet culture. Use popular meme formats, trending references, and casual language. Focus on humor and relatability.`;

    case COPY_CONFIG.THEMES.DEGEN:
      return basePrompt + `DEGEN MODE: Create content for crypto/DeFi enthusiasts. Use crypto slang, reference market movements, trading psychology, and DeFi protocols. Appeal to risk-taking mentality and FOMO.`;

    case COPY_CONFIG.THEMES.VC_BAIT:
      return basePrompt + `VC BAIT MODE: Create professional, thought-leadership content that appeals to investors and entrepreneurs. Focus on business insights, market trends, and strategic thinking. Use sophisticated language.`;

    case COPY_CONFIG.THEMES.GENERAL:
    default:
      return basePrompt + `GENERAL MODE: Create broadly appealing content that can go viral across different audiences. Focus on universal themes, motivational content, or trending topics.`;
  }
}

/**
 * Build user prompt with specific instructions
 */
function buildUserPrompt(prompt: string, theme: string, context?: ContextData): string {
  let basePrompt = `Create a viral tweet based on this prompt: "${prompt}"`;

  // Add context information if available
  if (context) {
    basePrompt += `\n\nContext Information:`;

    if (context.type === 'twitter_post') {
      basePrompt += `\n- Reference Post: "${context.content}"`;
      if (context.metadata?.author) {
        basePrompt += `\n- Original Author: @${context.metadata.author}`;
      }
      if (context.metadata?.engagement) {
        basePrompt += `\n- Engagement: ${context.metadata.engagement.likes} likes, ${context.metadata.engagement.retweets} retweets`;
      }
      basePrompt += `\n- Use this post as inspiration or reference, but create original content that builds upon or responds to it.`;
    } else if (context.type === 'twitter_handle') {
      basePrompt += `\n- Handle Analysis: ${context.content}`;
      if (context.metadata?.author) {
        basePrompt += `\n- Target Handle: @${context.metadata.author}`;
      }
      basePrompt += `\n- Use this analysis to understand the style and topics that resonate with this audience.`;
    }
  }

  basePrompt += `

Requirements:
- Theme: ${theme}
- Maximum 280 characters including hashtags
- Include 2-3 relevant hashtags
- End with "Generated by Copium On-Chain"
- Make it highly engaging and shareable
${context ? '- Incorporate insights from the provided context to make the tweet more relevant and engaging' : ''}

Return only the tweet content, nothing else.`;

  return basePrompt;
}

/**
 * Parse the generated tweet response
 */
function parseGeneratedTweet(response: string, theme: string): GeneratedTweet {
  const content = response.trim();

  // Extract hashtags
  const hashtagRegex = /#\w+/g;
  const hashtags = content.match(hashtagRegex) || [];

  // Calculate viral score based on content characteristics
  const viralScore = calculateViralScore(content, theme);

  // Estimate engagement based on viral score and theme
  const estimatedEngagement = estimateEngagement(viralScore, theme);

  return {
    content,
    theme,
    viralScore,
    hashtags,
    estimatedEngagement,
  };
}

/**
 * Calculate viral score based on content analysis
 */
function calculateViralScore(content: string, theme: string): number {
  let score = 50; // Base score

  // Length optimization (140-200 chars is optimal)
  const length = content.length;
  if (length >= 140 && length <= 200) {
    score += 10;
  } else if (length < 100 || length > 250) {
    score -= 10;
  }

  // Hashtag count (2-3 is optimal)
  const hashtagCount = (content.match(/#\w+/g) || []).length;
  if (hashtagCount >= 2 && hashtagCount <= 3) {
    score += 5;
  }

  // Emotional triggers
  const emotionalWords = ['amazing', 'incredible', 'shocking', 'unbelievable', 'mind-blowing', 'game-changer'];
  const hasEmotionalTrigger = emotionalWords.some(word =>
    content.toLowerCase().includes(word)
  );
  if (hasEmotionalTrigger) {
    score += 10;
  }

  // Question or call-to-action
  if (content.includes('?') || content.toLowerCase().includes('what do you think')) {
    score += 8;
  }

  // Theme-specific bonuses
  switch (theme) {
    case COPY_CONFIG.THEMES.MEME:
      if (content.includes('😂') || content.includes('💀') || content.includes('fr')) {
        score += 5;
      }
      break;
    case COPY_CONFIG.THEMES.DEGEN:
      if (content.toLowerCase().includes('moon') || content.toLowerCase().includes('diamond hands')) {
        score += 5;
      }
      break;
    case COPY_CONFIG.THEMES.VC_BAIT:
      if (content.toLowerCase().includes('insight') || content.toLowerCase().includes('strategy')) {
        score += 5;
      }
      break;
  }

  return Math.min(Math.max(score, 0), 100); // Clamp between 0-100
}

/**
 * Estimate engagement based on viral score and theme
 */
function estimateEngagement(viralScore: number, theme: string): GeneratedTweet['estimatedEngagement'] {
  const baseMultiplier = viralScore / 100;

  // Theme-specific engagement patterns
  let themeMultipliers = { likes: 1, retweets: 1, replies: 1 };

  switch (theme) {
    case COPY_CONFIG.THEMES.MEME:
      themeMultipliers = { likes: 1.5, retweets: 1.8, replies: 1.2 };
      break;
    case COPY_CONFIG.THEMES.DEGEN:
      themeMultipliers = { likes: 1.2, retweets: 2.0, replies: 1.5 };
      break;
    case COPY_CONFIG.THEMES.VC_BAIT:
      themeMultipliers = { likes: 1.0, retweets: 0.8, replies: 1.8 };
      break;
    case COPY_CONFIG.THEMES.GENERAL:
      themeMultipliers = { likes: 1.3, retweets: 1.1, replies: 1.0 };
      break;
  }

  return {
    likes: Math.round(100 * baseMultiplier * themeMultipliers.likes),
    retweets: Math.round(30 * baseMultiplier * themeMultipliers.retweets),
    replies: Math.round(20 * baseMultiplier * themeMultipliers.replies),
  };
}

/**
 * Get available themes for tweet generation
 */
export function getAvailableThemes() {
  return [
    {
      id: COPY_CONFIG.THEMES.GENERAL,
      name: 'General Viral',
      description: 'Broadly appealing content for maximum reach',
      icon: '🚀',
    },
    {
      id: COPY_CONFIG.THEMES.MEME,
      name: 'Meme Mode',
      description: 'Humorous, relatable internet culture content',
      icon: '😂',
    },
    {
      id: COPY_CONFIG.THEMES.DEGEN,
      name: 'Degen Mode',
      description: 'Crypto/DeFi focused content for traders',
      icon: '💎',
    },
    {
      id: COPY_CONFIG.THEMES.VC_BAIT,
      name: 'VC Bait',
      description: 'Professional thought-leadership content',
      icon: '💼',
    },
  ];
}
