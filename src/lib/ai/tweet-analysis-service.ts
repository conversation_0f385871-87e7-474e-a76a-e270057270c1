/**
 * Tweet Analysis Service
 *
 * This service analyzes tweets to determine their relevance and generates
 * appropriate reply suggestions based on user preferences.
 */

import { evaluateTweet, generateReplySuggestions } from './openrouter-service';
import { createSupabaseServerClient } from '@/lib/supabase/server';

// Interface for tweet analysis results
export interface TweetAnalysisResult {
  tweetId: number;
  isWorthReplying: boolean;
  relevanceScore: number;
  evaluationReason: string;
  replySuggestions: string[];
}

/**
 * Analyzes a tweet and generates reply suggestions
 *
 * @param tweetId The database ID of the tweet
 * @param userId The user ID
 * @returns Analysis results including relevance and reply suggestions
 */
export async function analyzeTweet(tweetId: number, userId: string): Promise<TweetAnalysisResult> {
  try {
    const supabase = await createSupabaseServerClient();

    // Fetch the tweet from the database
    const { data: tweet, error: tweetError } = await supabase
      .from('buddychip_tweets')
      .select('*')
      .eq('id', tweetId)
      .eq('user_id', userId)
      .single();

    if (tweetError || !tweet) {
      throw new Error(tweetError?.message || 'Tweet not found');
    }

    // Fetch user preferences
    const { data: userPreferences } = await supabase
      .from('buddychip_user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    // Default preferences if none are set
    const preferences = userPreferences || {
      reply_style: 'Professional and friendly',
      interests: 'General business and technology',
      tone: 'Helpful and informative'
    };

    // Format preferences for the AI
    const formattedPreferences = `
      Reply Style: ${preferences.reply_style || 'Professional and friendly'}
      Interests: ${preferences.interests || 'General business and technology'}
      Tone: ${preferences.tone || 'Helpful and informative'}
    `;

    // Extract metrics from the raw tweet data
    const metrics = tweet.raw_data?.public_metrics || {
      retweet_count: 0,
      reply_count: 0,
      like_count: 0,
      quote_count: 0
    };

    // Evaluate the tweet
    const evaluation = await evaluateTweet(
      tweet.content,
      tweet.author_handle,
      metrics,
      formattedPreferences,
      preferences.system_prompt
    );

    // Generate reply suggestions if the tweet is worth replying to
    let suggestions: string[] = [];
    if (evaluation.worthReplying) {
      suggestions = await generateReplySuggestions(
        tweet.content,
        tweet.author_handle,
        preferences.reply_style || 'Professional and friendly',
        preferences.system_prompt
      );
    }

    // Store the analysis results in the database
    const { error: updateError } = await supabase
      .from('buddychip_tweets')
      .update({
        ai_analysis: {
          relevance_score: evaluation.score,
          worth_replying: evaluation.worthReplying,
          evaluation_reason: evaluation.reason,
          reply_suggestions: suggestions,
          analyzed_at: new Date().toISOString()
        }
      })
      .eq('id', tweetId)
      .eq('user_id', userId);

    if (updateError) {
      console.error('Error updating tweet with analysis:', updateError);
    }

    return {
      tweetId,
      isWorthReplying: evaluation.worthReplying,
      relevanceScore: evaluation.score,
      evaluationReason: evaluation.reason,
      replySuggestions: suggestions
    };
  } catch (error) {
    console.error('Error analyzing tweet:', error);
    throw error;
  }
}

/**
 * Batch analyzes multiple tweets
 *
 * @param tweetIds Array of tweet IDs to analyze
 * @param userId The user ID
 * @returns Array of analysis results
 */
export async function batchAnalyzeTweets(tweetIds: number[], userId: string): Promise<TweetAnalysisResult[]> {
  const results: TweetAnalysisResult[] = [];

  for (const tweetId of tweetIds) {
    try {
      const result = await analyzeTweet(tweetId, userId);
      results.push(result);
    } catch (error) {
      console.error(`Error analyzing tweet ${tweetId}:`, error);
      // Continue with other tweets even if one fails
    }
  }

  return results;
}
