/**
 * OpenRouter AI Service
 *
 * This service provides an interface to OpenRouter's API for accessing
 * various AI models, including Google's Gemini 2.5 Flash Preview.
 *
 * It supports tweet evaluation, reply generation, and personality analysis.
 */

import { createLogger } from '@/lib/utils/logger';
import { ExternalServiceError } from '@/lib/utils/errors';

// Create a service-specific logger
const aiLogger = createLogger({ component: 'openrouter-service' });

// Default model to use
const DEFAULT_MODEL = 'google/gemini-2.5-flash-preview';

// OpenRouter API endpoint
const OPENROUTER_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// Types for OpenRouter API
interface Message {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface OpenRouterRequest {
  model: string;
  messages: Message[];
  temperature?: number;
  max_tokens?: number;
  top_p?: number;
  stream?: boolean;
  response_format?: { type: string };
}

interface OpenRouterResponse {
  id: string;
  choices: {
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }[];
  model: string;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

/**
 * Makes a request to the OpenRouter API
 *
 * @param messages Array of messages to send to the model
 * @param model Model to use (defaults to Gemini 2.5 Flash Preview)
 * @param temperature Temperature parameter for generation (0-1)
 * @param maxTokens Maximum tokens to generate
 * @param requestId Optional request ID for tracking
 * @returns The AI response
 */
export async function callOpenRouter(
  messages: Message[],
  model: string = DEFAULT_MODEL,
  temperature: number = 0.7,
  maxTokens: number = 1024,
  requestId?: string
): Promise<string> {
  // Create a request-specific logger
  const requestLogger = aiLogger.child({
    requestId: requestId || `ai-${Date.now()}`,
    model,
    temperature,
    maxTokens
  });

  // Check if API key is configured
  if (!process.env.OPENROUTER_API_KEY) {
    const error = new ExternalServiceError(
      'OpenRouter API key is not configured',
      'OPENROUTER_API_KEY_MISSING'
    );
    requestLogger.error('Missing API key for OpenRouter', error);
    throw error;
  }

  // Log the request (excluding sensitive content)
  requestLogger.info('Making OpenRouter API request', {
    messageCount: messages.length,
    firstMessageRole: messages[0]?.role,
    contentLength: messages.reduce((sum, msg) => sum + msg.content.length, 0)
  });

  try {
    const startTime = Date.now();
    const response = await fetch(OPENROUTER_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENROUTER_API_KEY}`,
        'HTTP-Referer': process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000', // Required for OpenRouter
        'X-Title': 'BuddyChip', // Optional, but good practice
        'X-Request-ID': requestId || `ai-${Date.now()}` // For tracking
      },
      body: JSON.stringify({
        model,
        messages,
        temperature,
        max_tokens: maxTokens,
        response_format: { type: "text" }
      } as OpenRouterRequest)
    });

    const responseTime = Date.now() - startTime;
    requestLogger.debug('OpenRouter API response received', { responseTime, status: response.status });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      const errorMessage = errorData.error?.message || response.statusText;
      const statusCode = response.status;

      // Determine error type based on status code
      let errorCode = 'OPENROUTER_API_ERROR';
      if (statusCode === 429) {
        errorCode = 'OPENROUTER_RATE_LIMIT';
      } else if (statusCode >= 500) {
        errorCode = 'OPENROUTER_SERVER_ERROR';
      } else if (statusCode === 401 || statusCode === 403) {
        errorCode = 'OPENROUTER_AUTH_ERROR';
      }

      const error = new ExternalServiceError(
        `OpenRouter API error: ${errorMessage}`,
        errorCode,
        { statusCode, errorData }
      );

      requestLogger.error('OpenRouter API request failed', error);
      throw error;
    }

    const data = await response.json() as OpenRouterResponse;

    // Log usage information
    if (data.usage) {
      requestLogger.info('OpenRouter API request completed', {
        promptTokens: data.usage.prompt_tokens,
        completionTokens: data.usage.completion_tokens,
        totalTokens: data.usage.total_tokens,
        responseTime
      });
    }

    return data.choices[0].message.content;
  } catch (error: unknown) {
    // If it's not already an ExternalServiceError, wrap it
    if (!(error instanceof ExternalServiceError)) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      const wrappedError = new ExternalServiceError(
        `Error calling OpenRouter: ${errorMessage}`,
        'OPENROUTER_REQUEST_FAILED',
        { originalError: error }
      );
      requestLogger.error('Unexpected error calling OpenRouter', wrappedError);
      throw wrappedError;
    }

    // Re-throw ExternalServiceError
    throw error;
  }
}

/**
 * Evaluates a tweet to determine if it's worth replying to
 *
 * @param tweetContent The content of the tweet
 * @param tweetAuthor The author of the tweet
 * @param tweetMetrics Metrics related to the tweet
 * @param userPreferences User's preferences and style
 * @returns Object containing evaluation results
 */
export async function evaluateTweet(
  tweetContent: string,
  tweetAuthor: string,
  tweetMetrics: Record<string, unknown>,
  userPreferences: string,
  customSystemPrompt?: string
): Promise<{ worthReplying: boolean; reason: string; score: number }> {
  // Use custom system prompt if provided, otherwise use default
  const systemPrompt = customSystemPrompt || `SYSTEM PROMPT — Tweet-Reply Gatekeeper
  You are TweetGatekeeper, an AI filter that decides whether a tweet deserves a reply from us.

📋  Output format
Return exactly **YES** or **NO** (uppercase, no punctuation, no extra text).

🔑  Evaluation steps (silent, internal)
1. **Author check (hard filter)**
2. **Content relevance — 40 % weight**
   • Tweet must discuss one of: building/launching a product, fundraising, GTM or growth strategy, hiring, technical hurdles, or lessons learned as a founder.
   • Exclude memes, politics, generic motivation, or personal-life updates.

3. **Engagement momentum — 20 % weight**
   • Prefer tweets < 24 h old that show either
     – ≥ 50 likes **or** ≥ 5 replies **or** engagement rate > 1 % of the author's followers.
   • Older or low-traction tweets lose points.

4. **Value-add opportunity — 30 % weight**
   • Does the tweet ask a question, seek feedback, share a challenge, or leave a clear gap we can fill with insight or resources?
   • Pure announcements, victory laps, or closed statements score low.

5. **Tone & sentiment — 10 % weight**
   • Favor constructive, neutral-to-positive tweets.
   • Reject hostile, political, discriminatory, or spammy content.

🏁  Decision rule
Compute a weighted score from the above criteria (0-100).
If **score ≥ 70 → YES**, else NO.
Never reveal the score or your reasoning; output only YES or NO.
`;

  const userPrompt = `Tweet by ${tweetAuthor}: "${tweetContent}"

Tweet metrics: ${JSON.stringify(tweetMetrics)}

My preferences: ${userPreferences}

Is this tweet relevant and worth replying to? Answer with ONLY "YES" or "NO".`;

  const messages: Message[] = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: userPrompt }
  ];

  try {
    const response = await callOpenRouter(messages, DEFAULT_MODEL, 0.3);
    // Process the simple YES/NO response
    const cleanResponse = response.trim().toUpperCase();

    if (cleanResponse.includes('YES')) {
      return {
        worthReplying: true,
        reason: 'This tweet is relevant to your interests and worth replying to.',
        score: 8
      };
    } else {
      return {
        worthReplying: false,
        reason: 'This tweet is not relevant enough to warrant a reply.',
        score: 3
      };
    }
  } catch (error: unknown) {
    console.error('Error evaluating tweet:', error);
    // Return a default response if evaluation fails
    return {
      worthReplying: false,
      reason: 'Failed to evaluate tweet',
      score: 0
    };
  }
}

/**
 * Generates reply suggestions for a tweet
 *
 * @param tweetContent The content of the tweet
 * @param tweetAuthor The author of the tweet
 * @param userStyle User's preferred reply style
 * @returns Array of suggested replies
 */
export async function generateReplySuggestions(
  tweetContent: string,
  tweetAuthor: string,
  userStyle: string,
  customSystemPrompt?: string
): Promise<string[]> {
  // Use custom system prompt if provided, otherwise use default
  const systemPrompt = customSystemPrompt || `SYSTEM PROMPT — TweetResponder
Your job: write a concise, high-value reply to the given tweet.

✍️  Output rules
• 1-3 sentences, maximum 280 characters total.
• Plain text only — **no emojis, no hashtags, no @-mentions unless the tweet explicitly requests one.**
• Do **NOT** use any word in BANNED_WORDS (case-insensitive).
• Avoid corporate buzzwords or filler; keep language clear and direct.

🎯  Content guidelines
1. Add concrete value: share an insight, a resource, a quick suggestion, or an insightful follow-up question.
2. Match the tweet's topic and tone (constructive, founder-focused).
3. If answering a question, address it first; if the tweet shares a challenge, offer a practical next step; if it's a milestone, briefly acknowledge and add a helpful thought.
4. Keep it respectful, professional, and free of marketing jargon.

🛑  Banned words (hard filter)
foster, synergy, synergies, leveraging, paradigm, holistic, disruptive, innovative, game-changing, empower, empowering, thought leadership, ecosystem, transformative, agile, impactful, scalable, cutting-edge, value-add`;

const userPrompt = `Tweet by ${tweetAuthor}: "${tweetContent}"

My preferred reply style: ${userStyle}

Generate 3 different reply suggestions that match my style. Format your response as a numbered list with each suggestion on a new line.`;

  const messages: Message[] = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: userPrompt }
  ];

  try {
    const response = await callOpenRouter(messages, DEFAULT_MODEL, 0.7);
    // Parse the numbered list response
    const suggestions = response
      .split(/\d+\.\s+/)
      .filter(line => line.trim().length > 0)
      .map(line => line.trim());

    return suggestions.length > 0 ? suggestions : ['Failed to generate suggestions'];
  } catch (error: unknown) {
    console.error('Error generating reply suggestions:', error);
    return ['Failed to generate reply suggestions'];
  }
}

/**
 * Analyzes a collection of tweets to determine the user's personality traits,
 * topics of interest, and writing style.
 *
 * @param tweetText Combined text from user's tweets
 * @returns Personality analysis results
 */
export async function analyzePersonality(tweetText: string): Promise<unknown> {
  console.log('Analyzing personality from tweets...');

  // Create a system prompt for personality analysis
  const systemPrompt = `
    You are an expert psychologist and data analyst specializing in personality analysis.
    Analyze the provided tweets to determine the user's personality traits, topics of interest, and writing style.

    Focus on the following aspects:

    1. Big Five Personality Traits:
       - Openness to experience (creativity, curiosity, openness to new ideas)
       - Conscientiousness (organization, responsibility, reliability)
       - Extraversion (sociability, assertiveness, energy)
       - Agreeableness (compassion, cooperation, warmth)
       - Neuroticism (anxiety, emotional instability, negative emotions)

    2. Topics of Interest:
       - Identify the main topics the user discusses
       - Determine the level of interest in each topic
       - Find niche interests that might not be immediately obvious

    3. Writing Style:
       - Formality level (formal vs. casual)
       - Emotionality (emotional vs. neutral)
       - Complexity (simple vs. sophisticated vocabulary)
       - Assertiveness (passive vs. assertive)

    Provide your analysis in a structured JSON format with scores between 0 and 1 for each trait.
    The JSON should have the following structure:
    {
      "traits": {
        "openness": 0.X,
        "conscientiousness": 0.X,
        "extraversion": 0.X,
        "agreeableness": 0.X,
        "neuroticism": 0.X
      },
      "topics": [
        {"name": "Topic1", "score": 0.X},
        {"name": "Topic2", "score": 0.X},
        ...
      ],
      "writing_style": {
        "formality": 0.X,
        "emotionality": 0.X,
        "complexity": 0.X,
        "assertiveness": 0.X
      }
    }
  `;

  const userPrompt = `
    Here are tweets from a user. Analyze their personality traits, topics of interest, and writing style:

    ${tweetText.substring(0, 8000)} // Limit to 8000 chars to avoid token limits
  `;

  const messages: Message[] = [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: userPrompt }
  ];

  try {
    const response = await callOpenRouter(messages, DEFAULT_MODEL, 0.2);
    console.log('AI response for personality analysis received');

    // Try to parse the JSON response
    try {
      // Extract JSON from the response (it might be wrapped in markdown code blocks)
      const jsonMatch = response.match(/```json\n([\s\S]*?)\n```/) ||
                      response.match(/```\n([\s\S]*?)\n```/) ||
                      response.match(/{[\s\S]*?}/);

      const jsonString = jsonMatch ? jsonMatch[0] : response;
      const cleanedJson = jsonString.replace(/```json\n|```\n|```/g, '');

      const analysisResult = JSON.parse(cleanedJson);

      // Ensure the result has the expected structure
      const result = {
        traits: {
          openness: analysisResult.traits?.openness || 0.5,
          conscientiousness: analysisResult.traits?.conscientiousness || 0.5,
          extraversion: analysisResult.traits?.extraversion || 0.5,
          agreeableness: analysisResult.traits?.agreeableness || 0.5,
          neuroticism: analysisResult.traits?.neuroticism || 0.5,
          ...analysisResult.traits
        },
        topics: Array.isArray(analysisResult.topics)
          ? analysisResult.topics
          : [{ name: 'General', score: 0.5 }],
        writing_style: {
          formality: analysisResult.writing_style?.formality || 0.5,
          emotionality: analysisResult.writing_style?.emotionality || 0.5,
          complexity: analysisResult.writing_style?.complexity || 0.5,
          assertiveness: analysisResult.writing_style?.assertiveness || 0.5,
          ...analysisResult.writing_style
        }
      };

      return result;
    } catch (parseError: unknown) {
      console.error('Error parsing AI response for personality analysis:', parseError);

      // Return a default analysis if parsing fails
      return {
        traits: {
          openness: 0.5,
          conscientiousness: 0.5,
          extraversion: 0.5,
          agreeableness: 0.5,
          neuroticism: 0.5
        },
        topics: [
          { name: 'General', score: 0.5 }
        ],
        writing_style: {
          formality: 0.5,
          emotionality: 0.5,
          complexity: 0.5,
          assertiveness: 0.5
        }
      };
    }
  } catch (error: unknown) {
    console.error('Error calling OpenRouter API for personality analysis:', error);
    throw error;
  }
}
