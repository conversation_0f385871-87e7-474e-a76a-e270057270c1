/**
 * Image Generation Tool for AI Agents
 *
 * This tool allows AI agents to generate images using OpenAI's image generation
 * capabilities as part of their conversation flow.
 */

import { tool } from 'ai';
import { z } from 'zod';
import { generateImage, generateImageWithContext, ImageGenerationOptions } from '@/lib/ai/image-generation';
import { logger } from '@/lib/utils/logger';

const toolLogger = logger.child({ component: 'ImageGenerationTool' });

/**
 * Image generation tool for AI agents
 */
export const imageGenerationTool = tool({
  description: `Generate images using AI based on text descriptions. This tool can create visual content to enhance conversations, illustrate concepts, or create engaging visuals for social media content. Use this when the user asks for images, visuals, or when an image would enhance the response.`,

  parameters: z.object({
    prompt: z.string().describe('Detailed description of the image to generate. Be specific about style, composition, colors, and mood.'),
    context: z.object({
      type: z.enum(['general', 'tweet', 'conversation', 'social_media', 'reply']).describe('Type of context for the image generation'),
      content: z.string().optional().describe('Relevant content that provides context for the image'),
      userQuery: z.string().optional().describe('Original user query or request'),
    }).optional().describe('Context information to enhance the image generation'),
    options: z.object({
      size: z.enum(['1024x1024', '1024x1536', '1536x1024', 'auto']).optional().describe('Image dimensions'),
      quality: z.enum(['low', 'medium', 'high', 'auto']).optional().describe('Image quality level'),
      style: z.enum(['photorealistic', 'artistic', 'cartoon', 'minimalist', 'vibrant']).optional().describe('Visual style preference'),
    }).optional().describe('Image generation options'),
    reasoning: z.string().describe('Why this image generation is helpful for the current conversation'),
  }),

  execute: async ({ prompt, context, options, reasoning }) => {
    const requestId = `tool-img-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    toolLogger.info('Image generation tool executed', {
      requestId,
      prompt: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : ''),
      contextType: context?.type,
      reasoning: reasoning.substring(0, 100) + (reasoning.length > 100 ? '...' : ''),
    });

    try {
      // Prepare generation options
      const generationOptions: ImageGenerationOptions = {
        size: options?.size || 'auto',
        quality: options?.quality || 'auto',
        background: 'auto',
      };

      let result;

      // Use context-aware generation if context is provided
      if (context && context.type !== 'general' && context.content) {
        result = await generateImageWithContext(
          context.userQuery || prompt,
          {
            type: context.type as 'tweet' | 'conversation' | 'copy-ai' | 'reply',
            content: context.content,
          },
          generationOptions
        );
      } else {
        // Use standard generation
        result = await generateImage(prompt, generationOptions);
      }

      if (!result.success) {
        toolLogger.error('Image generation failed in tool', {
          requestId,
          error: result.error,
        });

        return {
          success: false,
          error: result.error || 'Failed to generate image',
          reasoning,
        };
      }

      toolLogger.info('Image generation tool completed successfully', {
        requestId,
        imageId: result.imageId,
        revisedPrompt: result.revisedPrompt?.substring(0, 100) +
          (result.revisedPrompt && result.revisedPrompt.length > 100 ? '...' : ''),
      });

      return {
        success: true,
        imageGenerated: true,
        imageId: result.imageId,
        originalPrompt: prompt,
        revisedPrompt: result.revisedPrompt,
        imageBase64: result.imageBase64, // Note: This will be large
        metadata: result.metadata,
        reasoning,
        message: `Successfully generated an image based on: "${prompt}". ${result.revisedPrompt ? `The AI refined the prompt to: "${result.revisedPrompt}"` : ''}`
      };

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      toolLogger.error('Unexpected error in image generation tool', {
        requestId,
        error: errorMessage,
        prompt: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : ''),
      });

      return {
        success: false,
        error: `Image generation failed: ${errorMessage}`,
        reasoning,
      };
    }
  },
});

/**
 * Create a user-specific image generation tool with context
 */
export const createUserImageGenerationTool = (userId: string) => tool({
  description: `Generate images using AI for the current user. This tool can create visual content to enhance conversations, illustrate concepts, or create engaging visuals. The generated images are associated with the user's account.`,

  parameters: z.object({
    prompt: z.string().describe('Detailed description of the image to generate'),
    context: z.object({
      type: z.enum(['general', 'tweet', 'conversation', 'social_media', 'reply']).describe('Type of context'),
      content: z.string().optional().describe('Relevant content for context'),
      userQuery: z.string().optional().describe('Original user query'),
      sessionId: z.string().optional().describe('Current session ID'),
    }).optional(),
    options: z.object({
      size: z.enum(['1024x1024', '1024x1536', '1536x1024', 'auto']).optional(),
      quality: z.enum(['low', 'medium', 'high', 'auto']).optional(),
      saveToDatabase: z.boolean().default(true).describe('Whether to save the image metadata to database'),
    }).optional(),
    reasoning: z.string().describe('Why this image generation is helpful'),
  }),

  execute: async ({ prompt, context, options, reasoning }) => {
    const requestId = `user-img-${userId}-${Date.now()}`;

    toolLogger.info('User image generation tool executed', {
      requestId,
      userId,
      prompt: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : ''),
      contextType: context?.type,
    });

    try {
      const generationOptions: ImageGenerationOptions = {
        size: options?.size || 'auto',
        quality: options?.quality || 'auto',
      };

      let result;

      if (context && context.type !== 'general' && context.content) {
        result = await generateImageWithContext(
          context.userQuery || prompt,
          {
            type: context.type as 'tweet' | 'conversation' | 'copy-ai' | 'reply',
            content: context.content,
          },
          generationOptions
        );
      } else {
        result = await generateImage(prompt, generationOptions);
      }

      if (!result.success) {
        return {
          success: false,
          error: result.error || 'Failed to generate image',
          userId,
          reasoning,
        };
      }

      // TODO: Save to database if options.saveToDatabase is true
      // This would require implementing a database table for generated images

      return {
        success: true,
        imageGenerated: true,
        imageId: result.imageId,
        userId,
        originalPrompt: prompt,
        revisedPrompt: result.revisedPrompt,
        imageBase64: result.imageBase64,
        metadata: {
          ...result.metadata,
          userId,
          context,
          generatedViaAgent: true,
        },
        reasoning,
        message: `Generated image for user. ${result.revisedPrompt ? `Refined prompt: "${result.revisedPrompt}"` : ''}`
      };

    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      toolLogger.error('Error in user image generation tool', {
        requestId,
        userId,
        error: errorMessage,
      });

      return {
        success: false,
        error: `Image generation failed: ${errorMessage}`,
        userId,
        reasoning,
      };
    }
  },
});
