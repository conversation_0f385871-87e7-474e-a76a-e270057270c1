/**
 * AI Prompt Enhancer Service
 *
 * Uses Google Gemini 2.5 Flash Preview via OpenRouter to enhance image generation prompts
 * for better, more detailed, and more creative image descriptions.
 */

import { callOpenRouter } from './openrouter-service';
import { logger } from '@/lib/utils/logger';

const enhancerLogger = logger.child({ component: 'PromptEnhancer' });

export interface PromptEnhancementOptions {
  contextType?: 'general' | 'tweet' | 'conversation' | 'copy-ai' | 'reply';
  contextContent?: string;
  style?: 'photorealistic' | 'artistic' | 'cartoon' | 'minimalist' | 'vibrant' | 'professional' | 'creative';
  mood?: 'energetic' | 'calm' | 'dramatic' | 'playful' | 'serious' | 'inspiring';
  targetAudience?: 'general' | 'professional' | 'social_media' | 'marketing' | 'personal';
}

export interface EnhancedPromptResult {
  originalPrompt: string;
  enhancedPrompt: string;
  improvements: string[];
  estimatedQuality: number; // 1-10 scale
  suggestedStyle: string;
  processingTime: number;
}

/**
 * Enhance an image generation prompt using Gemini AI
 */
export async function enhanceImagePrompt(
  originalPrompt: string,
  options: PromptEnhancementOptions = {}
): Promise<EnhancedPromptResult> {
  const requestId = `prompt-enhance-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  const startTime = Date.now();

  enhancerLogger.info('Starting prompt enhancement', {
    requestId,
    originalPrompt: originalPrompt.substring(0, 100),
    options
  });

  try {
    const systemPrompt = buildSystemPrompt(options);
    const userPrompt = buildUserPrompt(originalPrompt, options);

    console.log('🎨 PromptEnhancer: Enhancing prompt with Gemini', {
      originalLength: originalPrompt.length,
      contextType: options.contextType,
      style: options.style
    });

    const response = await callOpenRouter(
      [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      'google/gemini-2.5-flash-preview', // Use latest Gemini model
      0.7, // Balanced creativity
      1000, // Allow for detailed enhancements
      requestId
    );

    const result = parseEnhancementResponse(response, originalPrompt, startTime);

    enhancerLogger.info('Prompt enhancement completed', {
      requestId,
      originalLength: originalPrompt.length,
      enhancedLength: result.enhancedPrompt.length,
      estimatedQuality: result.estimatedQuality,
      processingTime: result.processingTime
    });

    console.log('✅ PromptEnhancer: Enhancement completed', {
      originalPrompt: originalPrompt.substring(0, 50),
      enhancedPrompt: result.enhancedPrompt.substring(0, 100),
      improvements: result.improvements.length,
      quality: result.estimatedQuality
    });

    return result;

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const processingTime = Date.now() - startTime;

    enhancerLogger.error('Prompt enhancement failed', {
      requestId,
      error: errorMessage,
      originalPrompt: originalPrompt.substring(0, 100),
      processingTime
    });

    console.error('💥 PromptEnhancer: Enhancement failed, using original prompt', error);

    // Return original prompt with minimal enhancement if AI fails
    return {
      originalPrompt,
      enhancedPrompt: originalPrompt,
      improvements: ['AI enhancement failed, using original prompt'],
      estimatedQuality: 5,
      suggestedStyle: options.style || 'general',
      processingTime
    };
  }
}

/**
 * Build the system prompt for Gemini
 */
function buildSystemPrompt(options: PromptEnhancementOptions): string {
  return `You are an expert AI image prompt engineer specializing in creating detailed, high-quality prompts for image generation models like OpenAI's gpt-image-1.

Your task is to enhance user prompts to create more vivid, detailed, and effective image generation prompts.

ENHANCEMENT GUIDELINES:
1. **Visual Details**: Add specific visual elements, colors, lighting, composition
2. **Style & Technique**: Suggest appropriate artistic styles, camera angles, techniques
3. **Atmosphere**: Include mood, lighting conditions, environmental details
4. **Quality Modifiers**: Add terms that improve image quality and detail
5. **Context Awareness**: Consider the intended use case and audience

CONTEXT TYPE: ${options.contextType || 'general'}
TARGET STYLE: ${options.style || 'balanced'}
MOOD: ${options.mood || 'neutral'}
AUDIENCE: ${options.targetAudience || 'general'}

RESPONSE FORMAT:
Return a JSON object with:
{
  "enhancedPrompt": "The improved prompt with rich visual details",
  "improvements": ["List of specific improvements made"],
  "estimatedQuality": 8,
  "suggestedStyle": "recommended style",
  "reasoning": "Brief explanation of enhancements"
}

QUALITY STANDARDS:
- Enhanced prompts should be 2-4x longer than originals
- Include specific visual details (lighting, colors, composition)
- Add professional photography/art terms when appropriate
- Maintain the original intent while adding richness
- Optimize for ${options.contextType || 'general'} use case`;
}

/**
 * Build the user prompt with context
 */
function buildUserPrompt(originalPrompt: string, options: PromptEnhancementOptions): string {
  let prompt = `Original prompt to enhance: "${originalPrompt}"`;

  if (options.contextContent) {
    prompt += `\n\nContext: This image will be used in relation to: "${options.contextContent.substring(0, 200)}"`;
  }

  if (options.contextType) {
    switch (options.contextType) {
      case 'tweet':
        prompt += '\n\nThis image will accompany a tweet - make it engaging and shareable for social media.';
        break;
      case 'copy-ai':
        prompt += '\n\nThis image is for viral content creation - make it eye-catching and memorable.';
        break;
      case 'conversation':
        prompt += '\n\nThis image will be used in a conversation - make it relevant and expressive.';
        break;
      case 'reply':
        prompt += '\n\nThis image will accompany a reply - make it contextually appropriate.';
        break;
    }
  }

  prompt += '\n\nPlease enhance this prompt to create a much more detailed, vivid, and effective image generation prompt. Focus on visual richness while maintaining the original concept.';

  return prompt;
}

/**
 * Parse the Gemini response
 */
function parseEnhancementResponse(
  response: string,
  originalPrompt: string,
  startTime: number
): EnhancedPromptResult {
  const processingTime = Date.now() - startTime;

  try {
    // Try to parse as JSON first
    const parsed = JSON.parse(response);
    
    return {
      originalPrompt,
      enhancedPrompt: parsed.enhancedPrompt || originalPrompt,
      improvements: parsed.improvements || ['Enhanced with AI'],
      estimatedQuality: Math.min(Math.max(parsed.estimatedQuality || 7, 1), 10),
      suggestedStyle: parsed.suggestedStyle || 'enhanced',
      processingTime
    };
  } catch {
    // If JSON parsing fails, treat the entire response as the enhanced prompt
    const enhancedPrompt = response.trim();
    
    return {
      originalPrompt,
      enhancedPrompt: enhancedPrompt.length > originalPrompt.length ? enhancedPrompt : originalPrompt,
      improvements: ['Enhanced prompt structure and details'],
      estimatedQuality: 7,
      suggestedStyle: 'enhanced',
      processingTime
    };
  }
}

/**
 * Quick enhancement for simple use cases
 */
export async function quickEnhancePrompt(prompt: string): Promise<string> {
  try {
    const result = await enhanceImagePrompt(prompt, { contextType: 'general' });
    return result.enhancedPrompt;
  } catch {
    return prompt; // Return original if enhancement fails
  }
}
