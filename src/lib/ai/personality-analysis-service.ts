/**
 * Personality Analysis Service
 *
 * This service analyzes a user's tweets to determine their personality traits,
 * topics of interest, and writing style.
 */

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { SupabaseClient } from '@supabase/supabase-js';
import { analyzePersonality } from './openrouter-service';

// Interface for personality analysis results
export interface PersonalityAnalysis {
  traits: {
    openness: number;
    conscientiousness: number;
    extraversion: number;
    agreeableness: number;
    neuroticism: number;
    [key: string]: number;
  };
  topics: Array<{
    name: string;
    score: number;
  }>;
  writing_style: {
    formality: number;
    emotionality: number;
    complexity: number;
    assertiveness: number;
    [key: string]: number;
  };
  tweet_count: number;
}

interface AnalysisJobResult {
  jobId: string;
  status: string;
  estimatedTime: string;
}

/**
 * Initiates personality analysis for a user
 *
 * @param userId User ID
 * @returns Job information for tracking the analysis process
 */
export async function analyzeUserPersonality(userId: string): Promise<AnalysisJobResult> {
  console.log(`Starting personality analysis for user ${userId}`);

  const supabase = await createSupabaseServerClient();

  // Check if there are tweets to analyze
  const { count, error: countError } = await supabase
    .from('buddychip_personality_tweets')
    .select('*', { count: 'exact', head: true })
    .eq('user_id', userId);

  if (countError) {
    console.error('Error counting tweets:', countError);
    throw countError;
  }

  if (!count || count === 0) {
    throw new Error('No tweets available for analysis. Please fetch your tweets first.');
  }

  // Create an analysis job
  const { data: job, error } = await supabase
    .from('buddychip_analysis_jobs')
    .insert({
      user_id: userId,
      status: 'pending',
      progress: 0
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating analysis job:', error);
    throw error;
  }

  // Start a background process to analyze tweets
  // In a production environment, this would be handled by a serverless function or worker
  startPersonalityAnalysisJob(supabase, userId, job.id);

  // Calculate estimated time (rough estimate: 2 seconds per 10 tweets)
  const estimatedSeconds = Math.ceil((count / 10) * 2);
  const estimatedTime = estimatedSeconds < 60
    ? `${estimatedSeconds} seconds`
    : `${Math.ceil(estimatedSeconds / 60)} minutes`;

  return {
    jobId: job.id,
    status: 'pending',
    estimatedTime
  };
}

/**
 * Starts a background job to analyze personality
 *
 * @param supabase Supabase client
 * @param userId User ID
 * @param jobId Job ID for tracking
 */
async function startPersonalityAnalysisJob(
  supabase: SupabaseClient,
  userId: string,
  jobId: string
): Promise<void> {
  try {
    console.log(`Starting personality analysis job ${jobId} for user ${userId}`);

    // Update job status
    await supabase
      .from('buddychip_analysis_jobs')
      .update({
        status: 'in_progress',
        updated_at: new Date().toISOString()
      })
      .eq('id', jobId);

    // Fetch user's tweets
    const { data: tweets, error: tweetsError } = await supabase
      .from('buddychip_personality_tweets')
      .select('content, created_at, raw_data')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (tweetsError) {
      throw tweetsError;
    }

    if (!tweets || tweets.length === 0) {
      throw new Error('No tweets available for analysis');
    }

    // Update progress
    await updateJobProgress(supabase, jobId, 10);

    // Preprocess tweets
    const processedTweets = tweets.map(tweet => preprocessTweet(tweet.content));

    // Update progress
    await updateJobProgress(supabase, jobId, 20);

    // Analyze personality using AI
    const combinedText = processedTweets.join('\n\n');
    const analysis = await analyzePersonality(combinedText) as PersonalityAnalysis;

    // Update progress
    await updateJobProgress(supabase, jobId, 80);

    // Store analysis results
    const { error: upsertError } = await supabase
      .from('buddychip_personality_analysis')
      .upsert({
        user_id: userId,
        analysis_date: new Date().toISOString(),
        traits: analysis.traits,
        topics: analysis.topics,
        writing_style: analysis.writing_style,
        tweet_count: tweets.length
      }, {
        onConflict: 'user_id'
      });

    if (upsertError) {
      throw upsertError;
    }

    // Mark job as completed
    await supabase
      .from('buddychip_analysis_jobs')
      .update({
        status: 'completed',
        progress: 100,
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', jobId);

    console.log(`Personality analysis job ${jobId} completed.`);

  } catch (error: unknown) {
    // Handle errors
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`Error in personality analysis job ${jobId}:`, error);
    await supabase
      .from('buddychip_analysis_jobs')
      .update({
        status: 'failed',
        error: errorMessage,
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', jobId);
  }
}

/**
 * Updates the progress of a job
 *
 * @param supabase Supabase client
 * @param jobId Job ID
 * @param progress Progress percentage
 */
async function updateJobProgress(
  supabase: SupabaseClient,
  jobId: string,
  progress: number
): Promise<void> {
  await supabase
    .from('buddychip_analysis_jobs')
    .update({
      progress,
      updated_at: new Date().toISOString()
    })
    .eq('id', jobId);
}

/**
 * Preprocesses a tweet for analysis
 *
 * @param text Tweet text
 * @returns Preprocessed text
 */
function preprocessTweet(text: string): string {
  // Remove URLs
  let processed = text.replace(/https?:\/\/\S+/g, '');

  // Remove mentions
  processed = processed.replace(/@\w+/g, '');

  // Remove hashtags
  processed = processed.replace(/#\w+/g, '');

  // Remove RT prefix
  processed = processed.replace(/^RT\s+/g, '');

  // Remove extra whitespace
  processed = processed.replace(/\s+/g, ' ').trim();

  return processed;
}

/**
 * Gets the personality analysis for a user
 *
 * @param userId User ID
 * @returns Personality analysis results
 */
export async function getUserPersonalityAnalysis(userId: string): Promise<{
  analysis: PersonalityAnalysis | null;
  jobStatus: string | null;
}> {
  const supabase = await createSupabaseServerClient();

  // Check if there's an analysis result
  const { data: analysis, error: analysisError } = await supabase
    .from('buddychip_personality_analysis')
    .select('*')
    .eq('user_id', userId)
    .single();

  if (analysisError && analysisError.code !== 'PGRST116') { // PGRST116 is "no rows returned"
    console.error('Error fetching personality analysis:', analysisError);
    throw analysisError;
  }

  // Check if there's an ongoing job
  const { data: job, error: jobError } = await supabase
    .from('buddychip_analysis_jobs')
    .select('status')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .limit(1)
    .single();

  if (jobError && jobError.code !== 'PGRST116') {
    console.error('Error fetching analysis job:', jobError);
    throw jobError;
  }

  return {
    analysis: analysis as PersonalityAnalysis,
    jobStatus: job?.status || null
  };
}

/**
 * Gets the status of an analysis job
 *
 * @param jobId Job ID
 * @returns Job status information
 */
export async function getAnalysisJobStatus(jobId: string): Promise<{
  id: string;
  status: string;
  progress: number;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  error?: string;
}> {
  const supabase = await createSupabaseServerClient();

  const { data: job, error } = await supabase
    .from('buddychip_analysis_jobs')
    .select('*')
    .eq('id', jobId)
    .single();

  if (error) {
    console.error('Error fetching job status:', error);
    throw error;
  }

  return {
    id: job.id,
    status: job.status,
    progress: job.progress,
    createdAt: job.created_at,
    updatedAt: job.updated_at,
    completedAt: job.completed_at,
    error: job.error
  };
}
