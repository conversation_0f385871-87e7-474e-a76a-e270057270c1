/**
 * Search Agent with Perplexity API and xAI Live Search Tools
 *
 * This agent intelligently selects between Perplexity and xAI Live Search
 * based on the query type and requirements.
 * Uses OpenRouter with Google Gemini 2.5 Flash Preview for query analysis.
 */

import { generateText, tool } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { z } from 'zod';
import { logger } from '@/lib/utils/logger';

const searchAgentLogger = logger.child({ component: 'SearchAgent' });

// OpenRouter configuration for Gemini
const openrouter = createOpenAI({
  apiKey: process.env.OPENROUTER_API_KEY,
  baseURL: 'https://openrouter.ai/api/v1',
});

// Perplexity API configuration
const PERPLEXITY_API_URL = 'https://api.perplexity.ai/chat/completions';

// xAI API configuration
const XAI_API_URL = 'https://api.x.ai/v1/chat/completions';

// Types for API responses
interface PerplexityResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

interface XAIResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
  citations?: string[];
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// Perplexity Search Tool
const perplexitySearchTool = tool({
  description: 'Search the web using Perplexity AI for comprehensive research and analysis. Best for general knowledge, research, and detailed explanations.',
  parameters: z.object({
    query: z.string().describe('The search query to send to Perplexity'),
    model: z.enum(['sonar-pro',]).default('sonar-pro').describe('Perplexity model to use'),
    temperature: z.number().min(0).max(2).default(0.2).describe('Temperature for response generation'),
  }),
  execute: async ({ query, model, temperature }) => {
    searchAgentLogger.info('Executing Perplexity search', { query, model, temperature });

    if (!process.env.PERPLEXITY_API_KEY) {
      throw new Error('PERPLEXITY_API_KEY environment variable is not set');
    }

    try {
      const response = await fetch(PERPLEXITY_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.PERPLEXITY_API_KEY}`,
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: 'system',
              content: 'You are a helpful research assistant. Provide comprehensive, accurate, and well-sourced information.'
            },
            {
              role: 'user',
              content: query
            }
          ],
          temperature,
          max_tokens: 4000,
          return_citations: true,
          return_images: false,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Perplexity API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const data: PerplexityResponse = await response.json();
      const content = data.choices[0]?.message?.content || 'No response received';

      searchAgentLogger.info('Perplexity search completed', {
        query,
        responseLength: content.length,
        tokensUsed: data.usage?.total_tokens
      });

      return {
        content,
        source: 'perplexity',
        model,
        usage: data.usage,
        citations: [], // Perplexity includes citations in the content
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      searchAgentLogger.error('Perplexity search failed', { query, error: errorMessage });
      throw new Error(`Perplexity search failed: ${errorMessage}`);
    }
  },
});

// xAI Live Search Tool
const xaiLiveSearchTool = tool({
  description: 'Search real-time data using xAI Live Search. Best for current events, recent news, live social media content, and up-to-date information.',
  parameters: z.object({
    query: z.string().describe('The search query for real-time data'),
    mode: z.enum(['auto', 'on', 'off']).default('auto').describe('Search mode: auto (model decides), on (force search), off (no search)'),
    sources: z.array(z.enum(['web', 'x', 'news', 'rss'])).default(['web', 'x']).describe('Data sources to search'),
    maxResults: z.number().min(1).max(20).default(10).describe('Maximum number of search results'),
    fromDate: z.string().optional().describe('Start date for search in YYYY-MM-DD format'),
    toDate: z.string().optional().describe('End date for search in YYYY-MM-DD format'),
    xHandles: z.array(z.string()).optional().describe('Specific X handles to search (for X source)'),
    excludeWebsites: z.array(z.string()).optional().describe('Websites to exclude from search'),
    country: z.string().optional().describe('Country code for localized results (ISO alpha-2)'),
    safeSearch: z.boolean().default(true).describe('Enable safe search filtering'),
  }),
  execute: async ({
    query,
    mode,
    sources,
    maxResults,
    fromDate,
    toDate,
    xHandles,
    excludeWebsites,
    country,
    safeSearch
  }) => {
    searchAgentLogger.info('Executing xAI Live Search', {
      query,
      mode,
      sources,
      maxResults,
      fromDate,
      toDate
    });

    if (!process.env.XAI_API_KEY) {
      throw new Error('XAI_API_KEY environment variable is not set');
    }

    try {
      // Build search parameters
      const searchParameters: Record<string, unknown> = {
        mode,
        max_search_results: maxResults,
        return_citations: true,
        sources: sources.map(source => {
          const sourceConfig: Record<string, unknown> = { type: source };

          if (source === 'x' && xHandles && xHandles.length > 0) {
            sourceConfig.x_handles = xHandles;
          }

          if ((source === 'web' || source === 'news') && excludeWebsites && excludeWebsites.length > 0) {
            sourceConfig.excluded_websites = excludeWebsites.slice(0, 5); // Max 5 allowed
          }

          if ((source === 'web' || source === 'news') && country) {
            sourceConfig.country = country;
          }

          if ((source === 'web' || source === 'news')) {
            sourceConfig.safe_search = safeSearch;
          }

          return sourceConfig;
        }),
      };

      if (fromDate) {
        searchParameters.from_date = fromDate;
      }

      if (toDate) {
        searchParameters.to_date = toDate;
      }

      const response = await fetch(XAI_API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.XAI_API_KEY}`,
        },
        body: JSON.stringify({
          model: 'grok-3-latest',
          messages: [
            {
              role: 'user',
              content: query
            }
          ],
          search_parameters: searchParameters,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`xAI API error: ${response.status} ${response.statusText} - ${JSON.stringify(errorData)}`);
      }

      const data: XAIResponse = await response.json();
      const content = data.choices[0]?.message?.content || 'No response received';

      searchAgentLogger.info('xAI Live Search completed', {
        query,
        responseLength: content.length,
        citationsCount: data.citations?.length || 0,
        tokensUsed: data.usage?.total_tokens
      });

      return {
        content,
        source: 'xai_live_search',
        model: 'grok-3-latest',
        usage: data.usage,
        citations: data.citations || [],
        searchParameters,
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      searchAgentLogger.error('xAI Live Search failed', { query, error: errorMessage });
      throw new Error(`xAI Live Search failed: ${errorMessage}`);
    }
  },
});

// Query Analysis Tool
const queryAnalysisTool = tool({
  description: 'Analyze the user query to determine the best search strategy and tool selection.',
  parameters: z.object({
    query: z.string().describe('The user query to analyze'),
  }),
  execute: async ({ query }) => {
    searchAgentLogger.info('Analyzing query with Gemini', { query });

    if (!process.env.OPENROUTER_API_KEY) {
      searchAgentLogger.warn('OPENROUTER_API_KEY not set, using default analysis');
      return {
        queryType: 'mixed',
        recommendedTool: 'both',
        reasoning: 'OpenRouter API key not configured, using balanced approach',
        suggestedSources: ['web', 'x'],
        timeframe: 'any',
        priority: 'balanced'
      };
    }

    try {
      const { text } = await generateText({
        model: openrouter('google/gemini-2.5-flash-preview-05-20'),
        prompt: `
Analyze this search query and determine the best search strategy:

Query: "${query}"

Consider:
1. Is this asking for real-time/current information? (news, recent events, live data)
2. Is this asking for general knowledge or research?
3. Does it mention specific dates or time periods?
4. Does it involve social media or X/Twitter content?
5. What type of sources would be most valuable?

Respond with a JSON object containing:
{
  "queryType": "realtime" | "research" | "mixed",
  "recommendedTool": "perplexity" | "xai" | "both",
  "reasoning": "explanation of the recommendation",
  "suggestedSources": ["web", "x", "news", "rss"],
  "timeframe": "recent" | "historical" | "any",
  "priority": "speed" | "depth" | "balanced"
}
        `,
        temperature: 0.1,
        maxTokens: 500,
      });

      const analysis = JSON.parse(text);
      searchAgentLogger.info('Query analysis completed with Gemini', { query, analysis });
      return analysis;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      searchAgentLogger.warn('Failed to analyze query with Gemini, using defaults', { query, error: errorMessage });
      return {
        queryType: 'mixed',
        recommendedTool: 'both',
        reasoning: 'Could not analyze query with Gemini, using balanced approach',
        suggestedSources: ['web', 'x'],
        timeframe: 'any',
        priority: 'balanced'
      };
    }
  },
});

// Main search agent function
export async function executeSearchAgent(
  userQuery: string,
  requestId: string = `search-${Date.now()}`
) {
  const agentLogger = searchAgentLogger.child({ requestId });

  agentLogger.info('Starting search agent execution', { userQuery });

  try {
    // Step 1: Analyze the query using Gemini 2.5 Flash Preview via OpenRouter
    agentLogger.info('Starting query analysis with Gemini 2.5 Flash Preview', { userQuery });
    const analysis = await queryAnalysisTool.execute(
      { query: userQuery },
      { toolCallId: `analysis-${Date.now()}`, messages: [] }
    );

    // Step 2: Execute appropriate search(es) based on analysis
    const results: Array<{
      content: string;
      source: string;
      model?: string;
      usage?: { total_tokens?: number };
      citations?: string[];
      searchParameters?: Record<string, unknown>;
    }> = [];

    if (analysis.recommendedTool === 'perplexity' || analysis.recommendedTool === 'both') {
      try {
        const perplexityResult = await perplexitySearchTool.execute(
          {
            query: userQuery,
            model: 'sonar-pro',
            temperature: 0.2,
          },
          { toolCallId: `perplexity-${Date.now()}`, messages: [] }
        );
        results.push(perplexityResult);
      } catch (error) {
        agentLogger.error('Perplexity search failed', { error });
      }
    }

    if (analysis.recommendedTool === 'xai' || analysis.recommendedTool === 'both') {
      try {
        const xaiResult = await xaiLiveSearchTool.execute(
          {
            query: userQuery,
            mode: 'auto',
            sources: analysis.suggestedSources,
            maxResults: 10,
            safeSearch: true,
          },
          { toolCallId: `xai-${Date.now()}`, messages: [] }
        );
        results.push(xaiResult);
      } catch (error) {
        agentLogger.error('xAI Live Search failed', { error });
      }
    }

    if (results.length === 0) {
      throw new Error('All search tools failed');
    }

    // Step 3: Synthesize results if multiple sources were used
    let finalResponse: {
      content: string;
      source: string;
      model?: string;
      usage?: { total_tokens?: number };
      citations?: string[];
      searchParameters?: Record<string, unknown>;
      toolsUsed?: string[];
      analysis?: unknown;
    };
    if (results.length === 1) {
      finalResponse = { ...results[0], toolsUsed: [results[0].source] };
    } else {
      // Combine results from multiple sources
      const combinedContent = results.map((result, index) =>
        `## Source ${index + 1}: ${result.source}\n\n${result.content}`
      ).join('\n\n---\n\n');

      const allCitations = results.flatMap(result => result.citations || []);

      finalResponse = {
        content: combinedContent,
        source: 'combined',
        citations: allCitations,
        analysis,
        toolsUsed: results.map(r => r.source),
        usage: {
          total_tokens: results.reduce((sum, r) => sum + (r.usage?.total_tokens || 0), 0)
        }
      };
    }

    agentLogger.info('Search agent execution completed', {
      userQuery,
      toolsUsed: finalResponse.toolsUsed || [finalResponse.source],
      citationsCount: finalResponse.citations?.length || 0,
      responseLength: finalResponse.content.length
    });

    return {
      success: true,
      response: finalResponse,
      analysis,
      requestId,
    };

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    agentLogger.error('Search agent execution failed', { userQuery, error: errorMessage });

    return {
      success: false,
      error: errorMessage,
      requestId,
    };
  }
}
