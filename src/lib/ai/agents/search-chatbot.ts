/**
 * Search Chatbot Agent with Mem0 Memory Integration
 *
 * This chatbot combines the search agent capabilities with conversational AI
 * and persistent memory using Mem0 for context-aware interactions.
 */

import { generateText, tool } from 'ai';
import { createOpenAI } from '@ai-sdk/openai';
import { z } from 'zod';
import { logger } from '@/lib/utils/logger';
import { memoryService } from '@/lib/memory/mem0-service';
import { executeSearchAgent } from './search-agent';
import { createUserImageGenerationTool } from '@/lib/ai/tools/image-generation-tool';

const chatbotLogger = logger.child({ component: 'SearchChatbot' });

// OpenRouter configuration for Gemini
const openrouter = createOpenAI({
  apiKey: process.env.OPENROUTER_API_KEY,
  baseURL: 'https://openrouter.ai/api/v1',
});

// Memory retrieval tool - creates a function that returns a tool with userId context
const createMemoryRetrievalTool = (userId: string) => tool({
  description: 'Retrieve relevant memories from previous conversations to provide context-aware responses.',
  parameters: z.object({
    query: z.string().describe('Query to search for relevant memories'),
    limit: z.number().default(5).describe('Maximum number of memories to retrieve'),
  }),
  execute: async ({ query, limit }) => {
    chatbotLogger.info('Retrieving memories', { userId, query, limit });

    try {
      const memories = await memoryService.search(query, {
        userId,
        limit,
      });

      const relevantMemories = memories.map(memory => ({
        content: memory.content,
        type: memory.metadata?.type as string || 'conversation',
        timestamp: memory.createdAt,
      }));

      chatbotLogger.info('Memories retrieved', {
        userId,
        memoriesFound: relevantMemories.length
      });

      return {
        memories: relevantMemories,
        count: relevantMemories.length,
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      chatbotLogger.error('Error retrieving memories', { userId, error: errorMessage });
      return {
        memories: [],
        count: 0,
        error: 'Failed to retrieve memories',
      };
    }
  },
});

// Search execution tool
const searchExecutionTool = tool({
  description: 'Execute a search using the AI search agent when the user needs current information or research.',
  parameters: z.object({
    query: z.string().describe('Search query to execute'),
    reasoning: z.string().describe('Why this search is needed based on the conversation'),
  }),
  execute: async ({ query, reasoning }) => {
    chatbotLogger.info('Executing search from chatbot', { query, reasoning });

    try {
      const searchResult = await executeSearchAgent(query);

      if (searchResult.success) {
        chatbotLogger.info('Search executed successfully', {
          query,
          sources: searchResult.response?.toolsUsed || [searchResult.response?.source]
        });

        return {
          success: true,
          response: searchResult.response?.content,
          sources: searchResult.response?.toolsUsed || [searchResult.response?.source],
          citations: searchResult.response?.citations || [],
          analysis: searchResult.analysis,
        };
      } else {
        chatbotLogger.error('Search execution failed', { query, error: searchResult.error });
        return {
          success: false,
          error: searchResult.error,
        };
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      chatbotLogger.error('Error executing search', { query, error: errorMessage });
      return {
        success: false,
        error: 'Failed to execute search',
      };
    }
  },
});

// Memory storage tool - creates a function that returns a tool with userId context
const createMemoryStorageTool = (userId: string) => tool({
  description: 'Store important information from the conversation in memory for future reference.',
  parameters: z.object({
    messages: z.array(z.object({
      role: z.enum(['user', 'assistant']),
      content: z.string(),
    })).describe('Messages to store in memory'),
    sessionId: z.string().describe('Session ID for grouping related conversations'),
    importance: z.enum(['high', 'medium', 'low']).default('medium').describe('Importance level of the information'),
  }),
  execute: async ({ messages, sessionId, importance }) => {
    chatbotLogger.info('Storing conversation in memory', {
      userId,
      sessionId,
      messageCount: messages.length,
      importance
    });

    try {
      const memoryIds = await memoryService.add(messages, {
        userId,
        sessionId,
        metadata: {
          importance,
          source: 'search_chatbot',
          timestamp: new Date().toISOString(),
        },
      });

      chatbotLogger.info('Conversation stored in memory', {
        userId,
        sessionId,
        memoriesCreated: memoryIds.length
      });

      return {
        success: true,
        memoriesCreated: memoryIds.length,
        memoryIds,
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      chatbotLogger.error('Error storing conversation in memory', {
        userId,
        sessionId,
        error: errorMessage
      });
      return {
        success: false,
        error: 'Failed to store conversation in memory',
      };
    }
  },
});

// Main chatbot conversation function
export async function executeChatbotConversation(
  userMessage: string,
  options: {
    userId: string;
    sessionId: string;
    conversationHistory?: Array<{ role: 'user' | 'assistant'; content: string }>;
    includeSearch?: boolean;
    enabledTools?: string[];
  }
) {
  const { userId, sessionId, conversationHistory = [], includeSearch = true, enabledTools = [] } = options;
  const requestId = `chatbot-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  const chatLogger = chatbotLogger.child({ requestId, userId, sessionId });

  chatLogger.info('Starting chatbot conversation', {
    userMessage: userMessage.substring(0, 100) + (userMessage.length > 100 ? '...' : ''),
    historyLength: conversationHistory.length,
    includeSearch
  });

  if (!process.env.OPENROUTER_API_KEY) {
    throw new Error('OPENROUTER_API_KEY environment variable is not set');
  }

  try {
    // Step 1: Create tools with userId context
    const memoryRetrievalTool = createMemoryRetrievalTool(userId);
    const memoryStorageTool = createMemoryStorageTool(userId);

    // Step 2: Retrieve relevant memories for context
    let memoryContext: { memories: Array<{ content: string; type: string; timestamp: string }>; count: number } = { memories: [], count: 0 };
    try {
      memoryContext = await memoryRetrievalTool.execute(
        { query: userMessage, limit: 5 },
        {
          toolCallId: `memory-${Date.now()}`,
          messages: conversationHistory.map(msg => ({ role: msg.role, content: msg.content }))
        }
      );
    } catch (error) {
      chatLogger.warn('Failed to retrieve memory context', { error });
    }

    // Step 3: Build conversation context
    const systemPrompt = `You are BuddyChip's AI assistant with access to powerful search capabilities and persistent memory.

Your capabilities:
1. Access to real-time information via Perplexity and xAI Live Search
2. Persistent memory of user preferences, facts, and conversation history
3. Ability to provide both conversational responses and detailed research

Context from previous conversations:
${memoryContext.memories && memoryContext.memories.length > 0
  ? memoryContext.memories.map((m) => `- ${m.content} (${m.type}, ${m.timestamp})`).join('\n')
  : 'No relevant previous context found.'
}

CRITICAL INSTRUCTIONS:
- ALWAYS provide a conversational response to the user, even when using tools
- When you use searchExecutionTool, you MUST follow up with a comprehensive answer based on the search results
- When you use any tool, explain what you found and provide helpful information to the user
- Never just call a tool without providing a response - the user expects a conversational interaction
- Be conversational and helpful
- Use search tools when you need current information or detailed research
- Store important user information in memory for future conversations
- Reference previous context when relevant
- Be concise but thorough
- Always cite sources when providing factual information from searches

Current conversation session: ${sessionId}`;

    // Step 4: Generate response using Gemini with tools
    const imageGenerationTool = createUserImageGenerationTool(userId);

    const toolsMap = {
      memoryRetrievalTool,
      searchExecutionTool,
      memoryStorageTool,
      imageGenerationTool,
    };

    const tools = enabledTools.length > 0
      ? Object.fromEntries(
          enabledTools
            .filter(toolName => toolName in toolsMap)
            .map(toolName => [toolName, toolsMap[toolName as keyof typeof toolsMap]])
        )
      : toolsMap;

    chatLogger.info('Generating response with Gemini', {
      userMessage: userMessage.substring(0, 100) + (userMessage.length > 100 ? '...' : ''),
      historyLength: conversationHistory.length,
      toolsAvailable: Object.keys(tools),
    });

    const { text, toolResults } = await generateText({
      model: openrouter('google/gemini-2.5-flash-preview-05-20'),
      system: systemPrompt,
      messages: [
        ...conversationHistory.slice(-10), // Keep last 10 messages for context
        {
          role: 'user',
          content: `${userMessage}\n\nIMPORTANT: After using any tools, please provide a comprehensive conversational response based on the results. Don't just call tools - explain what you found and help the user understand the information.`
        },
      ],
      tools,
      toolChoice: 'auto',
      temperature: 0.7,
      maxTokens: 2000,
    });

    chatLogger.info('Response generated', {
      textLength: text.length,
      toolResultsCount: toolResults?.length || 0,
      toolsUsed: toolResults?.map((r) => r.toolName) || [],
      textPreview: text.substring(0, 200) + (text.length > 200 ? '...' : ''),
    });

    // Fallback: If no text response but tools were used, create a response based on tool results
    let finalResponse = text;
    if (!text && toolResults && toolResults.length > 0) {
      chatLogger.warn('No text response generated, creating fallback response from tool results');

      const searchResult = toolResults.find((r) => r.toolName === 'searchExecutionTool')?.result;
      if (searchResult && typeof searchResult === 'object' && 'success' in searchResult && searchResult.success) {
        const searchContent = (searchResult as { response: string }).response;
        const sources = (searchResult as { sources: string[] }).sources || [];
        const sourcesText = sources.length > 0 ? `\n\nSources: ${sources.join(', ')}` : '';

        if (searchContent && searchContent.length > 0) {
          finalResponse = `Based on my search, here's what I found:\n\n${searchContent}${sourcesText}`;
        } else {
          finalResponse = `I performed a search for your query, but I'm having trouble formatting the response. Please try asking your question in a different way.${sourcesText}`;
        }
      } else {
        finalResponse = "I've processed your request using my tools, but I'm having trouble generating a response. Please try rephrasing your question.";
      }
    } else if (!text) {
      finalResponse = "I'm here to help! Could you please rephrase your question or let me know what specific information you're looking for?";
    }

    // Step 4: Store the conversation in memory
    const conversationToStore = [
      { role: 'user' as const, content: userMessage },
      { role: 'assistant' as const, content: finalResponse },
    ];

    try {
      await memoryStorageTool.execute(
        {
          messages: conversationToStore,
          sessionId,
          importance: 'medium'
        },
        {
          toolCallId: `storage-${Date.now()}`,
          messages: [...conversationHistory, { role: 'user', content: userMessage }, { role: 'assistant', content: text }]
        }
      );
    } catch (error) {
      chatLogger.warn('Failed to store conversation in memory', { error });
    }

    // Step 5: Extract tool results for response metadata
    const searchResults = toolResults?.find((r) => r.toolName === 'searchExecutionTool')?.result;
    const memoryResults = toolResults?.filter((r) => r.toolName === 'memoryStorageTool' || r.toolName === 'memoryRetrievalTool');

    chatLogger.info('Chatbot conversation completed', {
      responseLength: finalResponse.length,
      originalTextLength: text.length,
      toolsUsed: toolResults?.map((r) => r.toolName) || [],
      searchExecuted: !!searchResults,
      memoriesUsed: memoryContext.count,
      usedFallback: finalResponse !== text,
    });

    return {
      success: true,
      response: finalResponse,
      metadata: {
        requestId,
        sessionId,
        toolsUsed: toolResults?.map((r) => r.toolName) || [],
        searchResults: searchResults || null,
        memoryContext: memoryResults,
        conversationStored: true,
      },
    };

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    chatLogger.error('Chatbot conversation failed', { error: errorMessage });

    return {
      success: false,
      error: errorMessage,
      metadata: {
        requestId,
        sessionId,
      },
    };
  }
}

// Helper function to generate session ID
export function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}

// Helper function to get conversation summary for memory
export async function summarizeConversation(
  messages: Array<{ role: 'user' | 'assistant'; content: string }>,
  userId: string
): Promise<string> {
  if (!process.env.OPENROUTER_API_KEY) {
    return 'Conversation summary unavailable - OpenRouter API key not configured';
  }

  try {
    const { text } = await generateText({
      model: openrouter('google/gemini-2.5-flash-preview-05-20'),
      prompt: `
Summarize this conversation in 2-3 sentences, focusing on:
1. Key topics discussed
2. Important user preferences or information revealed
3. Any decisions or conclusions reached

Conversation:
${messages.map(m => `${m.role}: ${m.content}`).join('\n')}

Summary:`,
      temperature: 0.3,
      maxTokens: 200,
    });

    return text.trim();
  } catch (error) {
    chatbotLogger.error('Error summarizing conversation', { userId, error });
    return 'Error generating conversation summary';
  }
}
