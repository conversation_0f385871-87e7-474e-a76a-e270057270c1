import { generateText, tool } from 'ai';
import { openai } from '@ai-sdk/openai';
import { z } from 'zod';
import { logger } from '@/lib/utils/logger';
import { createSupabaseServerClient } from '@/lib/supabase/server';

const agentLogger = logger.child({ component: 'ReplyAgent' });

// Tool for analyzing tweet sentiment and engagement potential
const analyzeTweetTool = tool({
  description: 'Analyze tweet sentiment, engagement potential, and relevance',
  parameters: z.object({
    tweetContent: z.string().describe('The tweet content to analyze'),
    authorHandle: z.string().describe('The tweet author handle'),
    metrics: z.object({
      likes: z.number(),
      retweets: z.number(),
      replies: z.number(),
    }).describe('Tweet engagement metrics'),
  }),
  execute: async ({ tweetContent, authorHandle, metrics }) => {
    agentLogger.info('Analyzing tweet', { authorHandle, contentLength: tweetContent.length });

    // Calculate engagement score
    const engagementScore = (metrics.likes * 1) + (metrics.retweets * 3) + (metrics.replies * 2);

    // Determine sentiment (simplified)
    const positiveWords = ['great', 'awesome', 'love', 'amazing', 'excellent', 'fantastic'];
    const negativeWords = ['bad', 'hate', 'terrible', 'awful', 'worst', 'horrible'];

    const lowerContent = tweetContent.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerContent.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerContent.includes(word)).length;

    let sentiment = 'neutral';
    if (positiveCount > negativeCount) sentiment = 'positive';
    else if (negativeCount > positiveCount) sentiment = 'negative';

    return {
      engagementScore,
      sentiment,
      viralPotential: engagementScore > 100 ? 'high' : engagementScore > 20 ? 'medium' : 'low',
      authorInfluence: authorHandle.includes('verified') ? 'high' : 'medium',
    };
  },
});

// Tool for fetching user context and preferences
const getUserContextTool = tool({
  description: 'Fetch user context, preferences, and past interactions',
  parameters: z.object({
    userId: z.string().describe('The user ID'),
    tweetAuthor: z.string().describe('The tweet author to check relationship with'),
  }),
  execute: async ({ userId, tweetAuthor }) => {
    agentLogger.info('Fetching user context', { userId, tweetAuthor });

    const supabase = await createSupabaseServerClient();

    // Get user preferences
    const { data: preferences } = await supabase
      .from('buddychip_user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    // Check past interactions with this author
    const { data: pastInteractions } = await supabase
      .from('buddychip_tweets')
      .select('ai_analysis')
      .eq('user_id', userId)
      .eq('author_handle', tweetAuthor)
      .limit(5);

    return {
      replyStyle: preferences?.reply_style || 'Professional and friendly',
      interests: preferences?.interests || [],
      systemPrompt: preferences?.system_prompt,
      pastInteractionCount: pastInteractions?.length || 0,
      hasRepliedBefore: (pastInteractions?.length || 0) > 0,
    };
  },
});

// Tool for generating contextual replies
const generateReplyTool = tool({
  description: 'Generate contextual reply suggestions based on analysis',
  parameters: z.object({
    tweetContent: z.string().describe('The original tweet content'),
    tweetAnalysis: z.object({
      sentiment: z.string(),
      viralPotential: z.string(),
      engagementScore: z.number(),
    }).describe('Tweet analysis results'),
    userContext: z.object({
      replyStyle: z.string(),
      interests: z.array(z.string()),
      hasRepliedBefore: z.boolean(),
    }).describe('User context and preferences'),
    replyType: z.enum(['supportive', 'questioning', 'informative', 'humorous']).describe('Type of reply to generate'),
  }),
  execute: async ({ tweetContent, tweetAnalysis, userContext, replyType }) => {
    agentLogger.info('Generating contextual reply', { replyType, sentiment: tweetAnalysis.sentiment });

    const replyPrompts = {
      supportive: `Generate a supportive and encouraging reply that builds on the original tweet's positive aspects.`,
      questioning: `Generate a thoughtful question that encourages further discussion and engagement.`,
      informative: `Generate an informative reply that adds value with relevant insights or facts.`,
      humorous: `Generate a light-hearted, appropriate humorous response that fits the context.`,
    };

    const styleGuide = {
      'Professional and friendly': 'Use professional language while maintaining warmth',
      'Casual and conversational': 'Use casual, everyday language like talking to a friend',
      'Technical and detailed': 'Use precise, technical language with specific details',
      'Witty and engaging': 'Use clever wordplay and engaging hooks',
    };

    const prompt = `
Original tweet: "${tweetContent}"
Tweet sentiment: ${tweetAnalysis.sentiment}
Viral potential: ${tweetAnalysis.viralPotential}
Reply style: ${userContext.replyStyle}
User interests: ${userContext.interests.join(', ')}
Has replied before: ${userContext.hasRepliedBefore}

${replyPrompts[replyType]}
${styleGuide[userContext.replyStyle as keyof typeof styleGuide] || styleGuide['Professional and friendly']}

Generate a reply that is:
- Under 280 characters
- Authentic and engaging
- Appropriate for the context
- Likely to generate positive engagement
`;

    const { text } = await generateText({
      model: openai('gpt-4o'),
      prompt,
      temperature: 0.7,
      maxTokens: 100,
    });

    return {
      reply: text.trim(),
      confidence: tweetAnalysis.viralPotential === 'high' ? 0.9 : 0.7,
      estimatedEngagement: Math.floor(tweetAnalysis.engagementScore * 0.1),
    };
  },
});

// Tool for checking reply appropriateness
const validateReplyTool = tool({
  description: 'Validate reply appropriateness and safety',
  parameters: z.object({
    originalTweet: z.string().describe('The original tweet'),
    proposedReply: z.string().describe('The proposed reply'),
    userBrand: z.string().describe('User brand/persona to maintain'),
  }),
  execute: async ({ originalTweet, proposedReply, userBrand }) => {
    agentLogger.info('Validating reply appropriateness');

    // Check for inappropriate content
    const inappropriatePatterns = [
      /\b(spam|scam|fake|bot)\b/i,
      /\b(hate|offensive|inappropriate)\b/i,
      /\b(politics|controversial)\b/i,
    ];

    const hasInappropriateContent = inappropriatePatterns.some(pattern =>
      pattern.test(proposedReply) || pattern.test(originalTweet)
    );

    // Check length
    const isValidLength = proposedReply.length <= 280 && proposedReply.length >= 10;

    // Check brand alignment (simplified)
    const brandKeywords = userBrand.toLowerCase().split(' ');
    const replyLower = proposedReply.toLowerCase();
    const brandAlignment = brandKeywords.some(keyword => replyLower.includes(keyword)) ? 0.8 : 0.6;

    return {
      isAppropriate: !hasInappropriateContent && isValidLength,
      safetyScore: hasInappropriateContent ? 0.2 : 0.9,
      brandAlignment,
      lengthValid: isValidLength,
      recommendations: hasInappropriateContent
        ? ['Remove inappropriate content', 'Use more professional language']
        : isValidLength
        ? ['Reply looks good']
        : ['Adjust reply length'],
    };
  },
});

/**
 * Advanced reply generation using AI SDK agents with multiple tools
 */
export async function generateAdvancedReply(
  tweetContent: string,
  tweetAuthor: string,
  tweetMetrics: { likes: number; retweets: number; replies: number },
  userId: string,
  requestId?: string
): Promise<{
  suggestions: Array<{
    reply: string;
    confidence: number;
    type: string;
    reasoning: string;
  }>;
  analysis: Record<string, unknown>;
}> {
  const requestLogger = agentLogger.child({
    requestId: requestId || `reply-${Date.now()}`,
    userId,
    tweetAuthor
  });

  requestLogger.info('Starting advanced reply generation');

  try {
    const { toolResults, steps } = await generateText({
      model: openai('gpt-4o'),
      system: `You are an expert social media strategist and reply generator. Your job is to:
1. Analyze the tweet thoroughly using available tools
2. Understand the user's context and preferences
3. Generate multiple high-quality reply suggestions
4. Validate each suggestion for appropriateness

Use the tools systematically to gather information before generating replies.
Always prioritize authenticity, engagement potential, and brand safety.`,

      prompt: `Generate reply suggestions for this tweet:
"${tweetContent}" by @${tweetAuthor}

Metrics: ${tweetMetrics.likes} likes, ${tweetMetrics.retweets} retweets, ${tweetMetrics.replies} replies
User ID: ${userId}

Please:
1. First analyze the tweet and its engagement potential
2. Get the user's context and preferences
3. Generate 3 different types of replies (supportive, questioning, informative)
4. Validate each reply for appropriateness
5. Provide your final recommendations`,

      tools: {
        analyzeTweet: analyzeTweetTool,
        getUserContext: getUserContextTool,
        generateReply: generateReplyTool,
        validateReply: validateReplyTool,
      },

      maxSteps: 10,
      temperature: 0.7,
    });

    // Extract results from tool executions
    const analysis = toolResults.find(r => r.toolName === 'analyzeTweet')?.result;
    const userContext = toolResults.find(r => r.toolName === 'getUserContext')?.result;
    const replyResults = toolResults.filter(r => r.toolName === 'generateReply');
    const validationResults = toolResults.filter(r => r.toolName === 'validateReply');

    // Format suggestions
    const suggestions = replyResults.map((result, index) => {
      const validation = validationResults[index]?.result;
      return {
        reply: result.result.reply,
        confidence: result.result.confidence * (validation?.safetyScore || 0.5),
        type: ['supportive', 'questioning', 'informative'][index] || 'general',
        reasoning: `Generated based on ${analysis?.sentiment} sentiment and ${analysis?.viralPotential} viral potential. Safety score: ${validation?.safetyScore || 'unknown'}`,
      };
    }).filter(s => s.confidence > 0.5); // Only return high-confidence suggestions

    requestLogger.info('Advanced reply generation completed', {
      suggestionCount: suggestions.length,
      stepsUsed: steps.length
    });

    return {
      suggestions,
      analysis: {
        tweetAnalysis: analysis,
        userContext,
        toolsUsed: toolResults.map(r => r.toolName),
        processingSteps: steps.length,
      },
    };

  } catch (error) {
    requestLogger.error('Error in advanced reply generation', { error });
    throw new Error('Failed to generate advanced reply');
  }
}
