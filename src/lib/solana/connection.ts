import { Connection, clusterApiUrl, PublicKey } from '@solana/web3.js';
import { SOLANA_NETWORKS, CURRENT_NETWORK } from '@/lib/web3/config';

/**
 * Solana connection management utilities
 */

// Create connection instance
export function createSolanaConnection(network?: string): Connection {
  const targetNetwork = network || CURRENT_NETWORK;
  const networkConfig = SOLANA_NETWORKS[targetNetwork as keyof typeof SOLANA_NETWORKS];
  
  if (!networkConfig) {
    console.warn(`⚠️ Unknown network: ${targetNetwork}, falling back to devnet`);
    return new Connection(clusterApiUrl('devnet'), 'confirmed');
  }

  console.log('🔗 Creating Solana connection:', {
    network: targetNetwork,
    url: networkConfig.url,
  });

  return new Connection(networkConfig.url, 'confirmed');
}

// Default connection instance
export const solanaConnection = createSolanaConnection();

/**
 * Get account balance in SOL
 */
export async function getAccountBalance(publicKey: PublicKey, connection?: Connection): Promise<number> {
  const conn = connection || solanaConnection;
  
  try {
    console.log('💰 Fetching balance for:', publicKey.toString());
    const balance = await conn.getBalance(publicKey);
    const solBalance = balance / 1e9; // Convert lamports to SOL
    
    console.log('💰 Account balance:', {
      publicKey: publicKey.toString(),
      lamports: balance,
      sol: solBalance,
    });
    
    return solBalance;
  } catch (error) {
    console.error('❌ Error fetching account balance:', error);
    throw new Error('Failed to fetch account balance');
  }
}

/**
 * Check if account exists
 */
export async function accountExists(publicKey: PublicKey, connection?: Connection): Promise<boolean> {
  const conn = connection || solanaConnection;
  
  try {
    const accountInfo = await conn.getAccountInfo(publicKey);
    return accountInfo !== null;
  } catch (error) {
    console.error('❌ Error checking account existence:', error);
    return false;
  }
}

/**
 * Get recent blockhash
 */
export async function getRecentBlockhash(connection?: Connection) {
  const conn = connection || solanaConnection;
  
  try {
    const { blockhash, lastValidBlockHeight } = await conn.getLatestBlockhash();
    
    console.log('🧱 Recent blockhash:', {
      blockhash,
      lastValidBlockHeight,
    });
    
    return { blockhash, lastValidBlockHeight };
  } catch (error) {
    console.error('❌ Error fetching recent blockhash:', error);
    throw new Error('Failed to fetch recent blockhash');
  }
}

/**
 * Confirm transaction
 */
export async function confirmTransaction(
  signature: string,
  connection?: Connection
): Promise<boolean> {
  const conn = connection || solanaConnection;
  
  try {
    console.log('⏳ Confirming transaction:', signature);
    
    const confirmation = await conn.confirmTransaction(signature, 'confirmed');
    
    if (confirmation.value.err) {
      console.error('❌ Transaction failed:', confirmation.value.err);
      return false;
    }
    
    console.log('✅ Transaction confirmed:', signature);
    return true;
  } catch (error) {
    console.error('❌ Error confirming transaction:', error);
    return false;
  }
}

/**
 * Get network info
 */
export async function getNetworkInfo(connection?: Connection) {
  const conn = connection || solanaConnection;
  
  try {
    const [version, epochInfo, supply] = await Promise.all([
      conn.getVersion(),
      conn.getEpochInfo(),
      conn.getSupply(),
    ]);
    
    const networkInfo = {
      version,
      epochInfo,
      supply: {
        total: supply.value.total / 1e9,
        circulating: supply.value.circulating / 1e9,
        nonCirculating: supply.value.nonCirculating / 1e9,
      },
      network: CURRENT_NETWORK,
    };
    
    console.log('🌐 Network info:', networkInfo);
    return networkInfo;
  } catch (error) {
    console.error('❌ Error fetching network info:', error);
    throw new Error('Failed to fetch network info');
  }
}
