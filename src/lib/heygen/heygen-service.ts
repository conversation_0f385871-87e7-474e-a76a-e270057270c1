import { logger } from '@/lib/utils/logger';

const heygenLogger = logger.child({ component: 'HeyGenService' });

interface HeyGenAvatar {
  avatar_id: string;
  avatar_name: string;
  preview_image_url: string;
  gender: string;
  avatar_type: string;
}

interface HeyGenVoice {
  voice_id: string;
  language: string;
  gender: string;
  name: string;
  preview_audio: string;
}

interface VideoGenerationRequest {
  script: string;
  avatarId: string;
  voiceId: string;
  title?: string;
  background?: string;
  ratio?: '16:9' | '9:16' | '1:1';
}

interface VideoGenerationResponse {
  video_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  video_url?: string;
  thumbnail_url?: string;
  duration?: number;
  error?: string;
}

/**
 * HeyGen API Service for generating avatar videos
 */
export class HeyGenService {
  private apiKey: string;
  private baseUrl: string = 'https://api.heygen.com/v2';

  constructor() {
    this.apiKey = process.env.HEYGEN_API_KEY || '';
    if (!this.apiKey) {
      heygenLogger.warn('HeyGen API key not configured');
    }
  }

  /**
   * Get available avatars
   */
  async getAvatars(): Promise<HeyGenAvatar[]> {
    heygenLogger.info('Fetching available avatars');

    try {
      const response = await fetch(`${this.baseUrl}/avatars`, {
        method: 'GET',
        headers: {
          'X-API-Key': this.apiKey,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HeyGen API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      heygenLogger.info('Avatars fetched successfully', { count: data.data?.avatars?.length || 0 });
      
      return data.data?.avatars || [];

    } catch (error) {
      heygenLogger.error('Error fetching avatars', { error });
      throw new Error('Failed to fetch avatars');
    }
  }

  /**
   * Get available voices
   */
  async getVoices(): Promise<HeyGenVoice[]> {
    heygenLogger.info('Fetching available voices');

    try {
      const response = await fetch(`${this.baseUrl}/voices`, {
        method: 'GET',
        headers: {
          'X-API-Key': this.apiKey,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HeyGen API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      heygenLogger.info('Voices fetched successfully', { count: data.data?.voices?.length || 0 });
      
      return data.data?.voices || [];

    } catch (error) {
      heygenLogger.error('Error fetching voices', { error });
      throw new Error('Failed to fetch voices');
    }
  }

  /**
   * Generate avatar video
   */
  async generateVideo(request: VideoGenerationRequest): Promise<VideoGenerationResponse> {
    heygenLogger.info('Generating avatar video', { 
      avatarId: request.avatarId,
      voiceId: request.voiceId,
      scriptLength: request.script.length 
    });

    try {
      const payload = {
        video_inputs: [
          {
            character: {
              type: 'avatar',
              avatar_id: request.avatarId,
              avatar_style: 'normal',
            },
            voice: {
              type: 'text',
              input_text: request.script,
              voice_id: request.voiceId,
            },
            background: {
              type: 'color',
              value: request.background || '#ffffff',
            },
          },
        ],
        dimension: {
          width: request.ratio === '9:16' ? 1080 : request.ratio === '1:1' ? 1080 : 1920,
          height: request.ratio === '9:16' ? 1920 : request.ratio === '1:1' ? 1080 : 1080,
        },
        aspect_ratio: request.ratio || '16:9',
        title: request.title || 'BuddyChip Generated Video',
      };

      const response = await fetch(`${this.baseUrl}/video/generate`, {
        method: 'POST',
        headers: {
          'X-API-Key': this.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        heygenLogger.error('HeyGen video generation failed', { 
          status: response.status,
          error: errorData 
        });
        throw new Error(`HeyGen API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      heygenLogger.info('Video generation initiated', { 
        videoId: data.data?.video_id,
        status: data.data?.status 
      });

      return {
        video_id: data.data?.video_id,
        status: data.data?.status || 'pending',
      };

    } catch (error) {
      heygenLogger.error('Error generating video', { error });
      throw new Error('Failed to generate video');
    }
  }

  /**
   * Check video generation status
   */
  async getVideoStatus(videoId: string): Promise<VideoGenerationResponse> {
    heygenLogger.info('Checking video status', { videoId });

    try {
      const response = await fetch(`${this.baseUrl}/video_status/${videoId}`, {
        method: 'GET',
        headers: {
          'X-API-Key': this.apiKey,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HeyGen API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      
      heygenLogger.info('Video status retrieved', { 
        videoId,
        status: data.data?.status 
      });

      return {
        video_id: videoId,
        status: data.data?.status || 'unknown',
        video_url: data.data?.video_url,
        thumbnail_url: data.data?.thumbnail_url,
        duration: data.data?.duration,
        error: data.data?.error,
      };

    } catch (error) {
      heygenLogger.error('Error checking video status', { videoId, error });
      throw new Error('Failed to check video status');
    }
  }

  /**
   * Generate viral video from tweet content
   */
  async generateViralVideo(
    tweetContent: string,
    options: {
      avatarId?: string;
      voiceId?: string;
      style?: 'professional' | 'casual' | 'energetic';
      background?: string;
      ratio?: '16:9' | '9:16' | '1:1';
    } = {}
  ): Promise<VideoGenerationResponse> {
    heygenLogger.info('Generating viral video from tweet', { 
      contentLength: tweetContent.length,
      style: options.style 
    });

    try {
      // Get default avatar and voice if not provided
      const avatarId = options.avatarId || await this.getDefaultAvatar();
      const voiceId = options.voiceId || await this.getDefaultVoice();

      // Enhance the script for video format
      const enhancedScript = this.enhanceScriptForVideo(tweetContent, options.style);

      return await this.generateVideo({
        script: enhancedScript,
        avatarId,
        voiceId,
        title: `Viral Tweet Video - ${new Date().toISOString()}`,
        background: options.background,
        ratio: options.ratio,
      });

    } catch (error) {
      heygenLogger.error('Error generating viral video', { error });
      throw new Error('Failed to generate viral video');
    }
  }

  /**
   * Get default avatar (first available)
   */
  private async getDefaultAvatar(): Promise<string> {
    try {
      const avatars = await this.getAvatars();
      if (avatars.length === 0) {
        throw new Error('No avatars available');
      }
      return avatars[0].avatar_id;
    } catch (error) {
      heygenLogger.error('Error getting default avatar', { error });
      // Fallback to a known avatar ID if available
      return 'default_avatar_id';
    }
  }

  /**
   * Get default voice (first available English voice)
   */
  private async getDefaultVoice(): Promise<string> {
    try {
      const voices = await this.getVoices();
      const englishVoice = voices.find(v => v.language.toLowerCase().includes('en'));
      if (!englishVoice) {
        throw new Error('No English voices available');
      }
      return englishVoice.voice_id;
    } catch (error) {
      heygenLogger.error('Error getting default voice', { error });
      // Fallback to a known voice ID if available
      return 'default_voice_id';
    }
  }

  /**
   * Enhance script for video format
   */
  private enhanceScriptForVideo(
    tweetContent: string, 
    style: 'professional' | 'casual' | 'energetic' = 'casual'
  ): string {
    const styleIntros = {
      professional: "Let me share an important insight with you.",
      casual: "Hey there! Here's something interesting I wanted to share.",
      energetic: "Wow! You need to hear this amazing insight!",
    };

    const styleOutros = {
      professional: "Thank you for your attention. What are your thoughts on this?",
      casual: "What do you think about this? Let me know in the comments!",
      energetic: "Isn't that incredible? Smash that like button if you agree!",
    };

    const intro = styleIntros[style];
    const outro = styleOutros[style];

    // Clean and format the tweet content
    const cleanContent = tweetContent
      .replace(/https?:\/\/[^\s]+/g, '') // Remove URLs
      .replace(/@\w+/g, '') // Remove mentions
      .replace(/#(\w+)/g, '$1') // Convert hashtags to regular words
      .trim();

    return `${intro} ${cleanContent} ${outro}`;
  }
}

// Export singleton instance
export const heygenService = new HeyGenService();
