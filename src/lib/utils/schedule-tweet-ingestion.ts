/**
 * Utility functions for scheduling tweet ingestion
 * This can be used to set up regular tweet fetching
 */

/**
 * Schedule tweet ingestion for all followed accounts
 * This function can be called from a cron job or serverless function
 * 
 * @param apiUrl The base URL of the API
 * @param authToken Authentication token for the API
 * @returns Promise that resolves when ingestion is complete
 */
export async function scheduleTweetIngestion(apiUrl: string, authToken: string) {
  try {
    // Call the tweet ingestion API
    const response = await fetch(`${apiUrl}/api/twitter/ingest`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to ingest tweets');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error scheduling tweet ingestion:', error);
    throw error;
  }
}

/**
 * Schedule tweet ingestion for a specific account
 * 
 * @param apiUrl The base URL of the API
 * @param authToken Authentication token for the API
 * @param accountId The ID of the account to ingest tweets for
 * @returns Promise that resolves when ingestion is complete
 */
export async function scheduleTweetIngestionForAccount(apiUrl: string, authToken: string, accountId: number) {
  try {
    // Call the tweet ingestion API for a specific account
    const response = await fetch(`${apiUrl}/api/twitter/ingest?account_id=${accountId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to ingest tweets for account');
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error scheduling tweet ingestion for account ${accountId}:`, error);
    throw error;
  }
}

/**
 * Example of how to set up a regular schedule for tweet ingestion
 * This can be used in a Node.js environment with a scheduler like node-cron
 * 
 * @example
 * // In a server.js file:
 * import cron from 'node-cron';
 * import { setupTweetIngestionSchedule } from './lib/utils/schedule-tweet-ingestion';
 * 
 * // Run tweet ingestion every hour
 * setupTweetIngestionSchedule('https://yourdomain.com', 'your-auth-token', '0 * * * *');
 */
export function setupTweetIngestionSchedule(apiUrl: string, authToken: string, cronSchedule: string) {
  // This is just an example - you would need to install node-cron in a real implementation
  // cron.schedule(cronSchedule, async () => {
  //   try {
  //     console.log('Running scheduled tweet ingestion...');
  //     await scheduleTweetIngestion(apiUrl, authToken);
  //     console.log('Scheduled tweet ingestion completed successfully');
  //   } catch (error) {
  //     console.error('Error in scheduled tweet ingestion:', error);
  //   }
  // });
  
  console.log(`Tweet ingestion scheduled with cron pattern: ${cronSchedule}`);
}
