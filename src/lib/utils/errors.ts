/**
 * Custom Error Classes
 * 
 * This file defines custom error classes for different types of errors
 * that can occur in the application. Using specific error types makes
 * error handling more consistent and provides better context for debugging.
 */

/**
 * Base application error class
 */
export class AppError extends Error {
  public statusCode: number;
  public code: string;
  public details?: unknown;
  public isOperational: boolean;

  constructor(
    message: string,
    statusCode = 500,
    code = 'INTERNAL_ERROR',
    details?: unknown,
    isOperational = true
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.code = code;
    this.details = details;
    this.isOperational = isOperational; // Indicates if this is an expected error

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * Error for authentication failures
 */
export class AuthenticationError extends AppError {
  constructor(
    message = 'Authentication failed',
    code = 'AUTHENTICATION_FAILED',
    details?: unknown
  ) {
    super(message, 401, code, details);
    this.name = 'AuthenticationError';
  }
}

/**
 * Error for authorization failures
 */
export class AuthorizationError extends AppError {
  constructor(
    message = 'You do not have permission to perform this action',
    code = 'AUTHORIZATION_FAILED',
    details?: unknown
  ) {
    super(message, 403, code, details);
    this.name = 'AuthorizationError';
  }
}

/**
 * Error for validation failures
 */
export class ValidationError extends AppError {
  constructor(
    message = 'Validation failed',
    code = 'VALIDATION_FAILED',
    details?: unknown
  ) {
    super(message, 400, code, details);
    this.name = 'ValidationError';
  }
}

/**
 * Error for not found resources
 */
export class NotFoundError extends AppError {
  constructor(
    message = 'Resource not found',
    code = 'NOT_FOUND',
    details?: unknown
  ) {
    super(message, 404, code, details);
    this.name = 'NotFoundError';
  }
}

/**
 * Error for database operations
 */
export class DatabaseError extends AppError {
  constructor(
    message = 'Database operation failed',
    code = 'DATABASE_ERROR',
    details?: unknown,
    isOperational = true
  ) {
    super(message, 500, code, details, isOperational);
    this.name = 'DatabaseError';
  }
}

/**
 * Error for external service failures (Twitter API, OpenRouter, etc.)
 */
export class ExternalServiceError extends AppError {
  constructor(
    message = 'External service error',
    code = 'EXTERNAL_SERVICE_ERROR',
    details?: unknown
  ) {
    super(message, 502, code, details);
    this.name = 'ExternalServiceError';
  }
}

/**
 * Error for rate limiting
 */
export class RateLimitError extends AppError {
  constructor(
    message = 'Rate limit exceeded',
    code = 'RATE_LIMIT_EXCEEDED',
    details?: unknown
  ) {
    super(message, 429, code, details);
    this.name = 'RateLimitError';
  }
}

/**
 * Error for subscription-related issues
 */
export class SubscriptionError extends AppError {
  constructor(
    message = 'Subscription error',
    code = 'SUBSCRIPTION_ERROR',
    details?: unknown
  ) {
    super(message, 402, code, details);
    this.name = 'SubscriptionError';
  }
}

/**
 * Helper function to determine if an error is an instance of AppError
 */
export function isAppError(error: unknown): error is AppError {
  return error instanceof AppError;
}

/**
 * Helper function to convert unknown errors to AppError
 */
export function toAppError(error: unknown): AppError {
  if (isAppError(error)) {
    return error;
  }

  // Handle specific error types
  if (error instanceof Error && error.message === 'PGRST116') {
    // Supabase "no rows returned" error
    return new NotFoundError('Resource not found', 'RESOURCE_NOT_FOUND', error);
  }

  // Default to internal server error
  return new AppError(
    error instanceof Error ? error.message : 'An unexpected error occurred',
    500,
    'INTERNAL_ERROR',
    error,
    false
  );
}
