/**
 * Validation Utilities
 *
 * This file provides utilities for validating request data using Zod.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { ValidationError } from './errors';
import { logger } from './logger';

/**
 * Validates request data against a Zod schema
 *
 * @param schema The Zod schema to validate against
 * @param data The data to validate
 * @param errorMessage Optional custom error message
 * @returns The validated data
 * @throws ValidationError if validation fails
 */
export function validateData<T extends z.ZodTypeAny>(
  schema: T,
  data: unknown,
  errorMessage = 'Validation failed'
): z.infer<T> {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const validationLogger = logger.child({
        component: 'validation',
        schema: schema._def.typeName
      });

      validationLogger.warn('Validation error', {
        errors: error.errors,
        data
      });

      throw new ValidationError(
        errorMessage,
        'VALIDATION_ERROR',
        { zodErrors: error.errors }
      );
    }
    throw error;
  }
}

/**
 * Creates a middleware that validates request data against a Zod schema
 *
 * @param schema The Zod schema to validate against
 * @param errorMessage Optional custom error message
 * @returns A middleware function that validates request data
 */
export function withValidation<T extends z.ZodTypeAny>(
  schema: T,
  errorMessage = 'Validation failed'
) {
  return async (request: NextRequest, next: () => Promise<NextResponse>) => {
    try {
      const data = await request.json();
      validateData(schema, data, errorMessage);
      return next();
    } catch (error) {
      if (error instanceof ValidationError) {
        return NextResponse.json(
          { error: error.message, details: error.details },
          { status: 400 }
        );
      }
      throw error;
    }
  };
}

/**
 * Common Zod schemas for reuse across the application
 */
export const schemas = {
  /**
   * Schema for tweet ID
   */
  tweetId: z.object({
    tweet_id: z.string().min(1, 'Tweet ID is required')
  }),

  /**
   * Schema for tweet IDs array - accepts both strings and numbers
   */
  tweetIds: z.object({
    tweet_ids: z.array(z.union([z.string(), z.number()])).min(1, 'At least one tweet ID is required'),
    analyze_all: z.boolean().optional()
  }),

  /**
   * Schema for tweet reply generation
   */
  replyGeneration: z.object({
    tweet_id: z.string().min(1, 'Tweet ID is required'),
    style: z.string().optional()
  }),

  /**
   * Schema for personality analysis
   */
  personalityAnalysis: z.object({
    user_id: z.string().uuid('Invalid user ID format').optional(),
    min_tweets: z.number().int().positive().optional()
  }),

  /**
   * Schema for account ID
   */
  accountId: z.object({
    account_id: z.string().min(1, 'Account ID is required')
  }),

  /**
   * Schema for user preferences
   */
  userPreferences: z.object({
    reply_style: z.string().optional(),
    system_prompt: z.string().optional(),
    auto_analyze: z.boolean().optional(),
    notification_email: z.string().email('Invalid email format').optional(),
    notification_frequency: z.enum(['daily', 'weekly', 'never']).optional()
  })
};

/**
 * Validates query parameters against a Zod schema
 *
 * @param request The Next.js request object
 * @param schema The Zod schema to validate against
 * @param errorMessage Optional custom error message
 * @returns The validated query parameters
 * @throws ValidationError if validation fails
 */
export function validateQueryParams<T extends z.ZodTypeAny>(
  request: NextRequest,
  schema: T,
  errorMessage = 'Invalid query parameters'
): z.infer<T> {
  const url = new URL(request.url);
  const params: Record<string, string> = {};

  // Convert URLSearchParams to a plain object
  url.searchParams.forEach((value, key) => {
    params[key] = value;
  });

  return validateData(schema, params, errorMessage);
}
