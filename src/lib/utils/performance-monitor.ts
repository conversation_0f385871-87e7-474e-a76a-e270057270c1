/**
 * Performance Monitoring Utility
 * 
 * Provides comprehensive performance monitoring for the BuddyChip application.
 * Tracks metrics, identifies bottlenecks, and provides optimization insights.
 */

import { logger } from './logger';
import { CLIENT_CONFIG } from '@/lib/config/client';

const performanceLogger = logger.child({ component: 'performance-monitor' });

// Performance metrics interface
interface PerformanceMetrics {
  timestamp: number;
  component: string;
  operation: string;
  duration: number;
  memory?: number;
  metadata?: Record<string, any>;
}

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  API_REQUEST: 2000, // 2 seconds
  DATABASE_QUERY: 1000, // 1 second
  COMPONENT_RENDER: 100, // 100ms
  BUNDLE_LOAD: 3000, // 3 seconds
  IMAGE_LOAD: 2000, // 2 seconds
  CACHE_OPERATION: 50, // 50ms
} as const;

// In-memory metrics store (use Redis in production)
class MetricsStore {
  private metrics: PerformanceMetrics[] = [];
  private readonly maxEntries = 1000;

  add(metric: PerformanceMetrics): void {
    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxEntries) {
      this.metrics = this.metrics.slice(-this.maxEntries);
    }
  }

  getMetrics(component?: string, operation?: string): PerformanceMetrics[] {
    return this.metrics.filter(metric => {
      if (component && metric.component !== component) return false;
      if (operation && metric.operation !== operation) return false;
      return true;
    });
  }

  getAverageTime(component: string, operation: string): number {
    const relevantMetrics = this.getMetrics(component, operation);
    if (relevantMetrics.length === 0) return 0;
    
    const total = relevantMetrics.reduce((sum, metric) => sum + metric.duration, 0);
    return total / relevantMetrics.length;
  }

  getSlowOperations(threshold: number): PerformanceMetrics[] {
    return this.metrics.filter(metric => metric.duration > threshold);
  }

  clear(): void {
    this.metrics = [];
  }
}

const metricsStore = new MetricsStore();

// Performance monitor class
export class PerformanceMonitor {
  private component: string;
  private activeTimers: Map<string, number> = new Map();

  constructor(component: string) {
    this.component = component;
  }

  /**
   * Start timing an operation
   */
  startTimer(operation: string): void {
    this.activeTimers.set(operation, performance.now());
  }

  /**
   * End timing and record metrics
   */
  endTimer(operation: string, metadata?: Record<string, any>): number {
    const startTime = this.activeTimers.get(operation);
    if (!startTime) {
      performanceLogger.warn('Timer not found', { component: this.component, operation });
      return 0;
    }

    const duration = performance.now() - startTime;
    this.activeTimers.delete(operation);

    this.recordMetric(operation, duration, metadata);
    return duration;
  }

  /**
   * Time a function execution
   */
  async timeAsync<T>(
    operation: string,
    fn: () => Promise<T>,
    metadata?: Record<string, any>
  ): Promise<T> {
    const startTime = performance.now();
    try {
      const result = await fn();
      const duration = performance.now() - startTime;
      this.recordMetric(operation, duration, { ...metadata, success: true });
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordMetric(operation, duration, { 
        ...metadata, 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Time a synchronous function
   */
  timeSync<T>(
    operation: string,
    fn: () => T,
    metadata?: Record<string, any>
  ): T {
    const startTime = performance.now();
    try {
      const result = fn();
      const duration = performance.now() - startTime;
      this.recordMetric(operation, duration, { ...metadata, success: true });
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      this.recordMetric(operation, duration, { 
        ...metadata, 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      throw error;
    }
  }

  /**
   * Record a performance metric
   */
  recordMetric(operation: string, duration: number, metadata?: Record<string, any>): void {
    const metric: PerformanceMetrics = {
      timestamp: Date.now(),
      component: this.component,
      operation,
      duration,
      memory: this.getMemoryUsage(),
      metadata,
    };

    metricsStore.add(metric);

    // Log slow operations
    const threshold = this.getThreshold(operation);
    if (duration > threshold) {
      performanceLogger.warn('Slow operation detected', {
        component: this.component,
        operation,
        duration,
        threshold,
        metadata,
      });
    }

    // Log in development
    if (CLIENT_CONFIG.ENABLE_DEBUG) {
      performanceLogger.debug('Performance metric recorded', metric);
    }
  }

  /**
   * Get performance summary for this component
   */
  getSummary(): {
    component: string;
    totalOperations: number;
    averageTime: number;
    slowOperations: number;
    operations: Array<{
      operation: string;
      count: number;
      averageTime: number;
      maxTime: number;
      minTime: number;
    }>;
  } {
    const metrics = metricsStore.getMetrics(this.component);
    
    if (metrics.length === 0) {
      return {
        component: this.component,
        totalOperations: 0,
        averageTime: 0,
        slowOperations: 0,
        operations: [],
      };
    }

    const operationStats = new Map<string, number[]>();
    
    metrics.forEach(metric => {
      if (!operationStats.has(metric.operation)) {
        operationStats.set(metric.operation, []);
      }
      operationStats.get(metric.operation)!.push(metric.duration);
    });

    const operations = Array.from(operationStats.entries()).map(([operation, durations]) => ({
      operation,
      count: durations.length,
      averageTime: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      maxTime: Math.max(...durations),
      minTime: Math.min(...durations),
    }));

    const totalTime = metrics.reduce((sum, metric) => sum + metric.duration, 0);
    const slowOperations = metrics.filter(metric => 
      metric.duration > this.getThreshold(metric.operation)
    ).length;

    return {
      component: this.component,
      totalOperations: metrics.length,
      averageTime: totalTime / metrics.length,
      slowOperations,
      operations,
    };
  }

  private getThreshold(operation: string): number {
    const operationLower = operation.toLowerCase();
    
    if (operationLower.includes('api') || operationLower.includes('request')) {
      return PERFORMANCE_THRESHOLDS.API_REQUEST;
    }
    if (operationLower.includes('database') || operationLower.includes('query')) {
      return PERFORMANCE_THRESHOLDS.DATABASE_QUERY;
    }
    if (operationLower.includes('render') || operationLower.includes('component')) {
      return PERFORMANCE_THRESHOLDS.COMPONENT_RENDER;
    }
    if (operationLower.includes('bundle') || operationLower.includes('load')) {
      return PERFORMANCE_THRESHOLDS.BUNDLE_LOAD;
    }
    if (operationLower.includes('image')) {
      return PERFORMANCE_THRESHOLDS.IMAGE_LOAD;
    }
    if (operationLower.includes('cache')) {
      return PERFORMANCE_THRESHOLDS.CACHE_OPERATION;
    }
    
    return PERFORMANCE_THRESHOLDS.API_REQUEST; // Default threshold
  }

  private getMemoryUsage(): number {
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed / 1024 / 1024; // MB
    }
    
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize / 1024 / 1024; // MB
    }
    
    return 0;
  }
}

// Global performance utilities
export const globalPerformanceMonitor = {
  /**
   * Get overall application performance summary
   */
  getGlobalSummary(): {
    totalMetrics: number;
    slowOperations: number;
    topSlowComponents: Array<{ component: string; averageTime: number }>;
    memoryTrend: number[];
  } {
    const allMetrics = metricsStore.getMetrics();
    const slowOperations = metricsStore.getSlowOperations(PERFORMANCE_THRESHOLDS.API_REQUEST);
    
    // Group by component
    const componentStats = new Map<string, number[]>();
    allMetrics.forEach(metric => {
      if (!componentStats.has(metric.component)) {
        componentStats.set(metric.component, []);
      }
      componentStats.get(metric.component)!.push(metric.duration);
    });

    const topSlowComponents = Array.from(componentStats.entries())
      .map(([component, durations]) => ({
        component,
        averageTime: durations.reduce((sum, d) => sum + d, 0) / durations.length,
      }))
      .sort((a, b) => b.averageTime - a.averageTime)
      .slice(0, 5);

    // Memory trend (last 10 metrics with memory data)
    const memoryTrend = allMetrics
      .filter(metric => metric.memory !== undefined)
      .slice(-10)
      .map(metric => metric.memory!);

    return {
      totalMetrics: allMetrics.length,
      slowOperations: slowOperations.length,
      topSlowComponents,
      memoryTrend,
    };
  },

  /**
   * Clear all metrics
   */
  clearMetrics(): void {
    metricsStore.clear();
    performanceLogger.info('Performance metrics cleared');
  },

  /**
   * Export metrics for analysis
   */
  exportMetrics(): PerformanceMetrics[] {
    return metricsStore.getMetrics();
  },
};

// Factory function to create performance monitors
export function createPerformanceMonitor(component: string): PerformanceMonitor {
  return new PerformanceMonitor(component);
}

// React hooks for performance monitoring
export function usePerformanceMonitor(component: string) {
  const monitor = new PerformanceMonitor(component);
  
  return {
    startTimer: (operation: string) => monitor.startTimer(operation),
    endTimer: (operation: string, metadata?: Record<string, any>) => monitor.endTimer(operation, metadata),
    recordMetric: (operation: string, duration: number, metadata?: Record<string, any>) => 
      monitor.recordMetric(operation, duration, metadata),
    getSummary: () => monitor.getSummary(),
  };
}

// Web Vitals monitoring (client-side only)
export function initWebVitalsMonitoring() {
  if (typeof window === 'undefined') return;

  // Monitor Core Web Vitals
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      const monitor = new PerformanceMonitor('web-vitals');
      monitor.recordMetric(entry.name, entry.value, {
        entryType: entry.entryType,
        startTime: entry.startTime,
      });
    });
  });

  try {
    observer.observe({ entryTypes: ['measure', 'navigation', 'paint'] });
  } catch (error) {
    performanceLogger.warn('Failed to initialize Web Vitals monitoring', { error });
  }
}
