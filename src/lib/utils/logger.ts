/**
 * Logger Service
 * 
 * A structured logging service that provides consistent logging across the application.
 * Supports different log levels, contextual information, and formatting.
 */

// Log levels in order of severity
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

// Configuration for the logger
interface LoggerConfig {
  minLevel: LogLevel;
  includeTimestamp: boolean;
  includeLevel: boolean;
  includeContext: boolean;
  enableConsole: boolean;
}

// Default configuration
const DEFAULT_CONFIG: LoggerConfig = {
  minLevel: process.env.NODE_ENV === 'production' ? LogLevel.INFO : LogLevel.DEBUG,
  includeTimestamp: true,
  includeLevel: true,
  includeContext: true,
  enableConsole: true,
};

// Context information for logs
interface LogContext {
  userId?: string;
  requestId?: string;
  component?: string;
  [key: string]: unknown;
}

/**
 * Logger class for structured logging
 */
export class Logger {
  private config: LoggerConfig;
  private context: LogContext;

  /**
   * Creates a new logger instance
   * 
   * @param context Optional context information for all logs from this instance
   * @param config Optional configuration overrides
   */
  constructor(context: LogContext = {}, config: Partial<LoggerConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.context = context;
  }

  /**
   * Creates a child logger with additional context
   * 
   * @param additionalContext Additional context to merge with the parent context
   * @returns A new logger instance with the combined context
   */
  child(additionalContext: LogContext): Logger {
    return new Logger(
      { ...this.context, ...additionalContext },
      this.config
    );
  }

  /**
   * Logs a debug message
   * 
   * @param message The message to log
   * @param data Optional data to include with the log
   * @param context Optional context specific to this log
   */
  debug(message: string, data?: unknown, context?: LogContext): void {
    this.log(LogLevel.DEBUG, message, data, context);
  }

  /**
   * Logs an info message
   * 
   * @param message The message to log
   * @param data Optional data to include with the log
   * @param context Optional context specific to this log
   */
  info(message: string, data?: unknown, context?: LogContext): void {
    this.log(LogLevel.INFO, message, data, context);
  }

  /**
   * Logs a warning message
   * 
   * @param message The message to log
   * @param data Optional data to include with the log
   * @param context Optional context specific to this log
   */
  warn(message: string, data?: unknown, context?: LogContext): void {
    this.log(LogLevel.WARN, message, data, context);
  }

  /**
   * Logs an error message
   * 
   * @param message The message to log
   * @param error Optional error object
   * @param context Optional context specific to this log
   */
  error(message: string, error?: Error | unknown, context?: LogContext): void {
    // Extract useful information from the error
    const errorData = error instanceof Error
      ? {
          message: error.message,
          name: error.name,
          stack: error.stack,
          ...(error as unknown as Record<string, unknown>), 
        }
      : error;

    this.log(LogLevel.ERROR, message, errorData, context);
  }

  /**
   * Internal method to handle logging
   */
  private log(level: LogLevel, message: string, data?: unknown, context?: LogContext): void {
    // Check if this log level should be processed
    if (level < this.config.minLevel) {
      return;
    }

    // Combine context
    const combinedContext = { ...this.context, ...context };

    // Create the log entry
    const logEntry: Record<string, unknown> = {
      message,
    };

    // Add timestamp if configured
    if (this.config.includeTimestamp) {
      logEntry.timestamp = new Date().toISOString();
    }

    // Add level if configured
    if (this.config.includeLevel) {
      logEntry.level = LogLevel[level];
    }

    // Add context if configured and exists
    if (this.config.includeContext && Object.keys(combinedContext).length > 0) {
      logEntry.context = combinedContext;
    }

    // Add data if provided
    if (data !== undefined) {
      logEntry.data = data;
    }

    // Output to console if enabled
    if (this.config.enableConsole) {
      this.consoleLog(level, logEntry);
    }

    // In a production environment, you might want to send logs to a service
    // like Sentry, LogRocket, or a custom logging endpoint
    // this.sendToLogService(logEntry);
  }

  /**
   * Outputs the log entry to the console
   */
  private consoleLog(level: LogLevel, logEntry: Record<string, unknown>): void {
    const logString = JSON.stringify(logEntry, null, 2);

    switch (level) {
      case LogLevel.DEBUG:
        console.debug(logString);
        break;
      case LogLevel.INFO:
        console.info(logString);
        break;
      case LogLevel.WARN:
        console.warn(logString);
        break;
      case LogLevel.ERROR:
        console.error(logString);
        break;
    }
  }
}

// Create a default logger instance
export const logger = new Logger();

// Export a function to create a logger with context
export function createLogger(context: LogContext): Logger {
  return logger.child(context);
}
