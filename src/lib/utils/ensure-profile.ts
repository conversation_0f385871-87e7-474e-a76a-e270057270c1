import { SupabaseClient } from '@supabase/supabase-js';

/**
 * Ensures that a profile exists for the current user.
 * If a profile doesn't exist, it creates one.
 * 
 * @param supabase The Supabase client
 * @param userId The user ID
 * @returns A promise that resolves when the profile is ensured
 */
export async function ensureProfile(supabase: SupabaseClient, userId: string) {
  // Check if profile exists
  const { data: profile, error: profileError } = await supabase
    .from('buddychip_profiles')
    .select('id')
    .eq('id', userId)
    .single();
  
  if (profileError && profileError.code !== 'PGRST116') {
    // PGRST116 is the error code for "no rows returned"
    console.error('Error checking profile:', profileError);
    throw new Error('Failed to check profile');
  }
  
  // If profile doesn't exist, create it
  if (!profile) {
    // Get user details
    const { data: userData, error: userError } = await supabase.auth.getUser();
    
    if (userError) {
      console.error('Error getting user:', userError);
      throw new Error('Failed to get user details');
    }
    
    const user = userData.user;
    
    // Create profile
    const { error: insertError } = await supabase
      .from('buddychip_profiles')
      .insert({
        id: userId,
        username: user?.user_metadata?.user_name || user?.email?.split('@')[0] || `user_${Date.now()}`,
        avatar_url: user?.user_metadata?.avatar_url || null,
      });
    
    if (insertError) {
      console.error('Error creating profile:', insertError);
      throw new Error('Failed to create profile');
    }
  }
  
  return true;
}
