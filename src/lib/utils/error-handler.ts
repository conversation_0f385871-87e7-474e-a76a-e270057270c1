/**
 * Error Handler Utilities
 * 
 * This file provides utilities for handling errors consistently across the application.
 */

import { NextRequest, NextResponse } from 'next/server';
import { isAppError, toAppError } from './errors';
import { logger } from './logger';

/**
 * Handles API route errors and returns appropriate responses
 * 
 * @param error The error to handle
 * @param request The Next.js request object
 * @returns A formatted NextResponse with appropriate status code and error details
 */
export function handleApiError(error: unknown, request: NextRequest): NextResponse {
  const appError = isAppError(error) ? error : toAppError(error);
  
  // Create a logger with request context
  const errorLogger = logger.child({
    requestId: request.headers.get('x-request-id') || undefined,
    url: request.url,
    method: request.method,
  });
  
  // Log the error with appropriate level
  if (appError.isOperational) {
    // Expected errors (like validation errors) are logged as warnings
    errorLogger.warn(`API Error: ${appError.message}`, appError);
  } else {
    // Unexpected errors are logged as errors
    errorLogger.error(`Unexpected API Error: ${appError.message}`, appError);
  }
  
  // Prepare the response payload
  const payload = {
    error: {
      code: appError.code,
      message: appError.message,
    },
  };
  
  // Include error details in development mode
  if (process.env.NODE_ENV !== 'production' && appError.details) {
    (payload.error as unknown as { details: unknown }).details = appError.details;
  }
  
  return NextResponse.json(payload, { status: appError.statusCode });
}

/**
 * Creates a try/catch wrapper for API route handlers
 * 
 * @param handler The API route handler function
 * @returns A wrapped handler that catches and processes errors
 */
export function withErrorHandling(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    try {
      return await handler(request);
    } catch (error: unknown) {
      return handleApiError(error, request);
    }
  };
}

/**
 * Handles client-side errors and provides consistent error messages
 * 
 * @param error The error to handle
 * @returns A formatted error object for client-side display
 */
export function handleClientError(error: unknown): {
  message: string;
  code?: string;
  details?: unknown;
} {
  // Convert to AppError if it's not already
  const appError = isAppError(error) ? error : toAppError(error);
  
  // Log the error
  logger.error(`Client Error: ${appError.message}`, appError);
  
  // Return a formatted error object
  return {
    message: appError.message,
    code: appError.code,
    details: process.env.NODE_ENV !== 'production' ? appError.details : undefined,
  };
}
