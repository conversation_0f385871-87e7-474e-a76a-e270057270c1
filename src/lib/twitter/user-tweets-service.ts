/**
 * User Tweets Service
 *
 * This service handles fetching and storing a user's own tweets for personality analysis.
 */

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { SupabaseClient } from '@supabase/supabase-js';

// Default number of tweets to fetch for free tier
const DEFAULT_MAX_TWEETS = 25;

// Twitter API rate limits
const RATE_LIMITS = {
  MAX_REQUESTS_PER_WINDOW: 900, // 900 requests per 15-minute window
  WINDOW_MS: 15 * 60 * 1000, // 15 minutes in milliseconds
  TWEETS_PER_REQUEST: 100, // Maximum tweets per request
  DELAY_BETWEEN_REQUESTS_MS: 1000 // Delay between requests to avoid rate limiting
};

interface UserTweet {
  id: string;
  text: string;
  created_at: string;
  public_metrics?: {
    retweet_count: number;
    reply_count: number;
    like_count: number;
    quote_count: number;
  };
}

interface FetchTweetsResult {
  jobId: string;
  status: string;
  maxTweets: number;
  estimatedTime: string;
}

/**
 * Fetches a user's tweets for personality analysis
 *
 * @param userId The user's ID
 * @param twitterHandle The user's Twitter handle
 * @param requestedTweetCount Optional number of tweets to fetch (limited by subscription tier)
 * @returns Job information for tracking the tweet fetching process
 */
export async function fetchUserTweets(
  userId: string,
  twitterHandle: string,
  requestedTweetCount?: number
): Promise<FetchTweetsResult> {
  console.log(`Fetching tweets for user ${userId}, handle: ${twitterHandle}`);

  const supabase = await createSupabaseServerClient();

  // Check user's subscription tier to determine max tweets allowed
  // For now, we'll use a default value until subscription system is implemented
  const maxAllowedTweets = await getMaxTweetsForUser();
  const tweetsToFetch = Math.min(requestedTweetCount || maxAllowedTweets, maxAllowedTweets);

  // Create an analysis job
  const { data: job, error } = await supabase
    .from('buddychip_analysis_jobs')
    .insert({
      user_id: userId,
      status: 'pending',
      progress: 0
    })
    .select()
    .single();

  if (error) {
    console.error('Error creating analysis job:', error);
    throw error;
  }

  // Start a background process to fetch tweets
  // In a production environment, this would be handled by a serverless function or worker
  startTweetFetchingJob(supabase, userId, twitterHandle, tweetsToFetch, job.id);

  // Calculate estimated time (rough estimate: 1 second per 10 tweets)
  const estimatedSeconds = Math.ceil(tweetsToFetch / 10);
  const estimatedTime = estimatedSeconds < 60
    ? `${estimatedSeconds} seconds`
    : `${Math.ceil(estimatedSeconds / 60)} minutes`;

  return {
    jobId: job.id,
    status: 'pending',
    maxTweets: tweetsToFetch,
    estimatedTime
  };
}

/**
 * Gets the maximum number of tweets allowed for a user based on their subscription
 *
 * @param supabase Supabase client
 * @param userId User ID
 * @returns Maximum number of tweets allowed
 */
async function getMaxTweetsForUser(): Promise<number> {
  // TODO: Implement subscription tier check
  // For now, return the default value
  return DEFAULT_MAX_TWEETS;
}

/**
 * Starts a background job to fetch tweets
 *
 * @param supabase Supabase client
 * @param userId User ID
 * @param twitterHandle Twitter handle
 * @param maxTweets Maximum number of tweets to fetch
 * @param jobId Job ID for tracking
 */
async function startTweetFetchingJob(
  supabase: SupabaseClient,
  userId: string,
  twitterHandle: string,
  maxTweets: number,
  jobId: string
): Promise<void> {
  try {
    console.log(`Starting tweet fetching job ${jobId} for user ${userId}`);

    // Update job status
    await supabase
      .from('buddychip_analysis_jobs')
      .update({
        status: 'in_progress',
        updated_at: new Date().toISOString()
      })
      .eq('id', jobId);

    // Fetch tweets in batches to respect Twitter API rate limits
    let fetchedCount = 0;

    while (fetchedCount < maxTweets) {
      // Update progress
      const progress = Math.round((fetchedCount / maxTweets) * 100);
      await updateJobProgress(supabase, jobId, progress);

      // Fetch a batch of tweets
      const batchSize = Math.min(RATE_LIMITS.TWEETS_PER_REQUEST, maxTweets - fetchedCount);
      let tweets: UserTweet[];

      try {
        tweets = await fetchTwitterUserTimeline();
      } catch (error: unknown) {
        console.warn(`Error fetching tweets from Twitter API: ${error instanceof Error ? error.message : 'Unknown error'}`);
        console.warn('Falling back to mock tweets for development');
        tweets = generateMockTweets(twitterHandle, batchSize, fetchedCount);
      }

      if (tweets.length === 0) {
        console.log(`No more tweets available for ${twitterHandle}`);
        break; // No more tweets available
      }

      // Store tweets in database
      const tweetsToInsert = tweets.map(tweet => ({
        user_id: userId,
        tweet_id: tweet.id,
        content: tweet.text,
        created_at: new Date(tweet.created_at).toISOString(),
        raw_data: tweet
      }));

      const { error: insertError } = await supabase
        .from('buddychip_personality_tweets')
        .upsert(tweetsToInsert, {
          onConflict: 'user_id,tweet_id'
        });

      if (insertError) {
        console.error('Error inserting tweets:', insertError);
      }

      // Update count
      fetchedCount += tweets.length;

      // Respect Twitter API rate limits
      await new Promise(resolve => setTimeout(resolve, RATE_LIMITS.DELAY_BETWEEN_REQUESTS_MS));
    }

    // Mark job as completed
    await supabase
      .from('buddychip_analysis_jobs')
      .update({
        status: 'completed',
        progress: 100,
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', jobId);

    console.log(`Tweet fetching job ${jobId} completed. Fetched ${fetchedCount} tweets.`);

    } catch (error: unknown) {
    // Handle errors
    console.error(`Error in tweet fetching job ${jobId}:`, error);
    await supabase
      .from('buddychip_analysis_jobs')
      .update({
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        completed_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .eq('id', jobId);
  }
}

/**
 * Updates the progress of a job
 *
 * @param supabase Supabase client
 * @param jobId Job ID
 * @param progress Progress percentage
 */
async function updateJobProgress(
  supabase: SupabaseClient,
  jobId: string,
  progress: number
): Promise<void> {
  await supabase
    .from('buddychip_analysis_jobs')
    .update({
      progress,
      updated_at: new Date().toISOString()
    })
    .eq('id', jobId);
}

/**
 * Fetches tweets from Twitter API
 *
 * @param twitterHandle Twitter handle
 * @param count Number of tweets to fetch
 * @param maxId Maximum tweet ID for pagination
 * @returns Array of tweets
 */
async function fetchTwitterUserTimeline(): Promise<UserTweet[]> {
  // Check if we have Twitter API credentials
  if (!process.env.TWITTER_BEARER_TOKEN) {
    throw new Error('Twitter API credentials not configured');
  }

  // TODO: Implement actual Twitter API call
  // For now, return mock data
  throw new Error('Twitter API integration not implemented yet');
}

/**
 * Generates mock tweets for development and testing
 *
 * @param twitterHandle Twitter handle
 * @param count Number of tweets to generate
 * @param offset Offset for tweet IDs
 * @returns Array of mock tweets
 */
function generateMockTweets(
  _twitterHandle: string,
  count: number,
  offset: number = 0
): UserTweet[] {
  const topics = [
    'technology', 'business', 'politics', 'sports', 'entertainment',
    'science', 'health', 'education', 'environment', 'travel'
  ];

  const sentiments = [
    'excited', 'happy', 'neutral', 'concerned', 'frustrated',
    'optimistic', 'thoughtful', 'curious', 'grateful', 'inspired'
  ];

  return Array.from({ length: count }, (_, i) => {
    const id = (1000000 + offset + i).toString();
    const topic = topics[Math.floor(Math.random() * topics.length)];
    const sentiment = sentiments[Math.floor(Math.random() * sentiments.length)];
    const daysAgo = Math.floor(Math.random() * 365);
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);

    return {
      id,
      text: `I'm feeling ${sentiment} about the latest developments in ${topic}. What do you think? #${topic} #thoughts`,
      created_at: date.toISOString(),
      public_metrics: {
        retweet_count: Math.floor(Math.random() * 50),
        reply_count: Math.floor(Math.random() * 20),
        like_count: Math.floor(Math.random() * 100),
        quote_count: Math.floor(Math.random() * 10)
      }
    };
  });
}
