/**
 * Server-Only Configuration
 * 
 * This file contains sensitive environment variables that should <PERSON>VE<PERSON> be exposed to the client.
 * These variables are only available on the server-side and in API routes.
 */

// Validate that we're on the server side
if (typeof window !== 'undefined') {
  throw new Error('Server configuration should not be imported on the client side');
}

export const SERVER_CONFIG = {
  // Database & Authentication
  SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  
  // AI Services
  OPENAI_API_KEY: process.env.OPENAI_API_KEY!,
  OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY!,
  PERPLEXITY_API_KEY: process.env.PERPLEXITY_API_KEY!,
  XAI_API_KEY: process.env.XAI_API_KEY!,
  
  // Social Media APIs
  TWITTER_BEARER_TOKEN: process.env.TWITTER_BEARER_TOKEN!,
  TWITTER_API_KEY: process.env.TWITTER_API_KEY!,
  TWITTER_API_SECRET: process.env.TWITTER_API_SECRET!,
  
  // Blockchain & Web3
  COPY_TOKEN_MINT_ADDRESS: process.env.COPY_TOKEN_MINT_ADDRESS!,
  RECIPIENT_ADDRESS: process.env.RECIPIENT_ADDRESS!,
  
  // IPFS & Storage
  IPFS_API_KEY: process.env.IPFS_API_KEY!,
  IPFS_API_SECRET: process.env.IPFS_API_SECRET!,
  
  // Pricing Configuration
  COPY_GENERATION_COST: parseFloat(process.env.COPY_GENERATION_COST || '0.5'),
  COPY_STAKING_MINIMUM: parseFloat(process.env.COPY_STAKING_MINIMUM || '100'),
  
  // Rate Limiting
  RATE_LIMIT_ENABLED: process.env.RATE_LIMIT_ENABLED === 'true',
  RATE_LIMIT_REDIS_URL: process.env.RATE_LIMIT_REDIS_URL,
  
  // Monitoring & Analytics
  SENTRY_DSN: process.env.SENTRY_DSN,
  ANALYTICS_API_KEY: process.env.ANALYTICS_API_KEY,
} as const;

// Validation function to ensure all required environment variables are set
export function validateServerConfig() {
  const requiredVars = [
    'SUPABASE_SERVICE_ROLE_KEY',
    'OPENAI_API_KEY',
  ] as const;

  const missingVars = requiredVars.filter(
    (varName) => !process.env[varName]
  );

  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}`
    );
  }
}

// Optional configuration with defaults
export const SERVER_DEFAULTS = {
  RATE_LIMIT_WINDOW_MS: 15 * 60 * 1000, // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: 100,
  CACHE_TTL_SECONDS: 300, // 5 minutes
  MAX_TWEET_FETCH_COUNT: 50,
  DEFAULT_PAGINATION_LIMIT: 20,
} as const;

// Environment-specific configuration
export const ENVIRONMENT = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  IS_PRODUCTION: process.env.NODE_ENV === 'production',
  IS_DEVELOPMENT: process.env.NODE_ENV === 'development',
  IS_TEST: process.env.NODE_ENV === 'test',
} as const;

// Logging configuration
export const LOGGING_CONFIG = {
  LEVEL: process.env.LOG_LEVEL || (ENVIRONMENT.IS_PRODUCTION ? 'info' : 'debug'),
  ENABLE_CONSOLE: process.env.ENABLE_CONSOLE_LOGGING !== 'false',
  ENABLE_FILE: process.env.ENABLE_FILE_LOGGING === 'true',
} as const;

// Security configuration
export const SECURITY_CONFIG = {
  ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  CSRF_SECRET: process.env.CSRF_SECRET || 'default-csrf-secret-change-in-production',
  SESSION_SECRET: process.env.SESSION_SECRET || 'default-session-secret-change-in-production',
  ENCRYPTION_KEY: process.env.ENCRYPTION_KEY,
} as const;

// Initialize validation on import
if (ENVIRONMENT.IS_PRODUCTION) {
  validateServerConfig();
}

console.log('🔒 Server configuration loaded:', {
  environment: ENVIRONMENT.NODE_ENV,
  hasOpenAI: !!SERVER_CONFIG.OPENAI_API_KEY,
  hasSupabase: !!SERVER_CONFIG.SUPABASE_SERVICE_ROLE_KEY,
  hasTwitter: !!SERVER_CONFIG.TWITTER_BEARER_TOKEN,
  rateLimitEnabled: SERVER_CONFIG.RATE_LIMIT_ENABLED,
});
