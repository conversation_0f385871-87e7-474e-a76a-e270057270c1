/**
 * Client-Side Configuration
 * 
 * This file contains ONLY public environment variables that are safe to expose to the client.
 * All variables here must be prefixed with NEXT_PUBLIC_ to be available in the browser.
 */

export const CLIENT_CONFIG = {
  // Supabase Public Configuration
  SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL!,
  SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  
  // Solana Network Configuration
  SOLANA_NETWORK: process.env.NEXT_PUBLIC_SOLANA_NETWORK || 'devnet',
  SOLANA_RPC_URL: process.env.NEXT_PUBLIC_SOLANA_RPC_URL,
  SOLANA_DEVNET_RPC_URL: process.env.NEXT_PUBLIC_SOLANA_DEVNET_RPC_URL,
  SOLANA_TESTNET_RPC_URL: process.env.NEXT_PUBLIC_SOLANA_TESTNET_RPC_URL,
  
  // Blockchain Contract Addresses (Public)
  COPY_TOKEN_MINT_ADDRESS: process.env.NEXT_PUBLIC_COPY_TOKEN_MINT_ADDRESS || 'CopyAiTokenMint1111111111111111111111111111',
  NFT_COLLECTION_ADDRESS: process.env.NEXT_PUBLIC_NFT_COLLECTION_ADDRESS,
  STAKING_PROGRAM_ADDRESS: process.env.NEXT_PUBLIC_STAKING_PROGRAM_ADDRESS,
  
  // WalletConnect Configuration
  WALLETCONNECT_PROJECT_ID: process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID,
  
  // Application Configuration
  APP_URL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || 'BuddyChip',
  APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
  
  // Feature Flags (Public)
  ENABLE_ANALYTICS: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
  ENABLE_DEBUG: process.env.NEXT_PUBLIC_ENABLE_DEBUG === 'true',
  ENABLE_PERFORMANCE_MONITORING: process.env.NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING === 'true',
  
  // Chain Configuration
  CHAIN_ID: parseInt(process.env.NEXT_PUBLIC_CHAIN_ID || '8453'),
  RPC_URL: process.env.NEXT_PUBLIC_RPC_URL || 'https://mainnet.base.org',
  
  // IPFS Gateway (Public)
  IPFS_GATEWAY_URL: process.env.NEXT_PUBLIC_IPFS_GATEWAY_URL || 'https://ipfs.io/ipfs/',
} as const;

// Solana network configuration
export const SOLANA_NETWORKS = {
  mainnet: {
    name: 'Mainnet Beta',
    url: CLIENT_CONFIG.SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
    chainId: 101,
  },
  devnet: {
    name: 'Devnet',
    url: CLIENT_CONFIG.SOLANA_DEVNET_RPC_URL || 'https://api.devnet.solana.com',
    chainId: 103,
  },
  testnet: {
    name: 'Testnet',
    url: CLIENT_CONFIG.SOLANA_TESTNET_RPC_URL || 'https://api.testnet.solana.com',
    chainId: 102,
  },
} as const;

// Get current network configuration
export function getCurrentNetworkConfig() {
  const network = CLIENT_CONFIG.SOLANA_NETWORK as keyof typeof SOLANA_NETWORKS;
  return SOLANA_NETWORKS[network] || SOLANA_NETWORKS.devnet;
}

// Copy.AI public configuration
export const COPY_CONFIG = {
  THEMES: {
    MEME: 'meme',
    DEGEN: 'degen',
    VC_BAIT: 'vc_bait',
    GENERAL: 'general',
  },
  VIRAL_SCORE_WEIGHTS: {
    LIKES: 1,
    RETWEETS: 3,
    REPLIES: 2,
    QUOTES: 4,
  },
} as const;

// Validation function for client configuration
export function validateClientConfig() {
  const requiredVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
  ] as const;

  const missingVars = requiredVars.filter(
    (varName) => !CLIENT_CONFIG[varName]
  );

  if (missingVars.length > 0) {
    console.error(
      `Missing required public environment variables: ${missingVars.join(', ')}`
    );
    return false;
  }

  return true;
}

// Performance configuration
export const PERFORMANCE_CONFIG = {
  ENABLE_BUNDLE_ANALYZER: process.env.ANALYZE === 'true',
  ENABLE_PERFORMANCE_MONITORING: CLIENT_CONFIG.ENABLE_PERFORMANCE_MONITORING,
  CACHE_STALE_TIME: 5 * 60 * 1000, // 5 minutes
  CACHE_GC_TIME: 10 * 60 * 1000, // 10 minutes
} as const;

// UI Configuration
export const UI_CONFIG = {
  DEFAULT_THEME: 'dark',
  ENABLE_ANIMATIONS: true,
  TWEETS_PER_PAGE: 20,
  MAX_TWEET_LENGTH: 280,
  DEBOUNCE_DELAY: 300,
} as const;

// Initialize validation
if (typeof window !== 'undefined') {
  // Only validate on client side
  validateClientConfig();
}

console.log('🌐 Client configuration loaded:', {
  appName: CLIENT_CONFIG.APP_NAME,
  network: CLIENT_CONFIG.SOLANA_NETWORK,
  hasSupabase: !!CLIENT_CONFIG.SUPABASE_URL,
  hasWalletConnect: !!CLIENT_CONFIG.WALLETCONNECT_PROJECT_ID,
  analyticsEnabled: CLIENT_CONFIG.ENABLE_ANALYTICS,
});
