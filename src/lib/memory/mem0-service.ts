import { createSupabaseServerClient } from '@/lib/supabase/server';
import { logger } from '@/lib/utils/logger';

const memoryLogger = logger.child({ component: 'Mem0Service' });

interface MemoryEntry {
  id: string;
  content: string;
  metadata: Record<string, unknown>;
  embedding?: number[];
  userId: string;
  createdAt: string;
  updatedAt: string;
}

interface ConversationMessage {
  role: 'user' | 'assistant';
  content: string;
}

/**
 * Mem0-compatible memory service using Supabase as vector storage
 * Implements core Mem0 functionality with Supabase backend
 */
export class BuddyChipMemoryService {
  private async getSupabase() {
    return await createSupabaseServerClient();
  }

  /**
   * Add conversation messages to memory
   */
  async add(
    messages: ConversationMessage[],
    options: {
      userId: string;
      metadata?: Record<string, unknown>;
      sessionId?: string;
    }
  ): Promise<string[]> {
    memoryLogger.info('Adding messages to memory', {
      userId: options.userId,
      messageCount: messages.length,
      sessionId: options.sessionId
    });

    try {

      const memoryIds: string[] = [];

      // Process each message and extract meaningful memories
      for (const message of messages) {
        const memories = await this.extractMemories(message, options);

        for (const memory of memories) {
          const memoryId = await this.storeMemory(memory, options);
          if (memoryId) memoryIds.push(memoryId);
        }
      }

      memoryLogger.info('Successfully added memories', {
        userId: options.userId,
        memoriesCreated: memoryIds.length
      });

      return memoryIds;

    } catch (error) {
      memoryLogger.error('Error adding memories', {
        userId: options.userId,
        error
      });
      throw new Error('Failed to add memories');
    }
  }

  /**
   * Search memories by query
   */
  async search(
    query: string,
    options: {
      userId: string;
      limit?: number;
      metadata?: Record<string, unknown>;
    }
  ): Promise<MemoryEntry[]> {
    memoryLogger.info('Searching memories', {
      userId: options.userId,
      query: query.substring(0, 50) + '...',
      limit: options.limit || 10
    });

    try {
      const supabase = await this.getSupabase();

      // Get query embedding using OpenAI
      const queryEmbedding = await this.getEmbedding(query);

      // Search using Supabase vector similarity
      const { data: memories, error } = await supabase
        .rpc('match_memories', {
          query_embedding: queryEmbedding,
          match_count: options.limit || 10,
          filter: {
            user_id: options.userId,
            ...options.metadata,
          }
        });

      if (error) {
        memoryLogger.error('Error searching memories', {
          userId: options.userId,
          error
        });
        throw error;
      }

      memoryLogger.info('Memory search completed', {
        userId: options.userId,
        resultsCount: memories?.length || 0
      });

      return memories || [];

    } catch (error) {
      memoryLogger.error('Error in memory search', {
        userId: options.userId,
        error
      });
      throw new Error('Failed to search memories');
    }
  }

  /**
   * Get all memories for a user
   */
  async getAll(userId: string, limit: number = 100): Promise<MemoryEntry[]> {
    memoryLogger.info('Getting all memories', { userId, limit });

    try {
      const supabase = await this.getSupabase();

      const { data: memories, error } = await supabase
        .from('buddychip_memories')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) {
        memoryLogger.error('Error getting all memories', { userId, error });
        throw error;
      }

      return memories || [];

    } catch (error) {
      memoryLogger.error('Error in getAll memories', { userId, error });
      throw new Error('Failed to get memories');
    }
  }

  /**
   * Delete a memory by ID
   */
  async delete(memoryId: string, userId: string): Promise<boolean> {
    memoryLogger.info('Deleting memory', { memoryId, userId });

    try {
      const supabase = await this.getSupabase();

      const { error } = await supabase
        .from('buddychip_memories')
        .delete()
        .eq('id', memoryId)
        .eq('user_id', userId);

      if (error) {
        memoryLogger.error('Error deleting memory', { memoryId, userId, error });
        return false;
      }

      memoryLogger.info('Memory deleted successfully', { memoryId, userId });
      return true;

    } catch (error) {
      memoryLogger.error('Error in delete memory', { memoryId, userId, error });
      return false;
    }
  }

  /**
   * Update memory metadata
   */
  async update(
    memoryId: string,
    userId: string,
    updates: { content?: string; metadata?: Record<string, unknown> }
  ): Promise<boolean> {
    memoryLogger.info('Updating memory', { memoryId, userId });

    try {
      const supabase = await this.getSupabase();

      const updateData: Record<string, unknown> = {
        updated_at: new Date().toISOString(),
      };

      if (updates.content) {
        updateData.content = updates.content;
        updateData.embedding = await this.getEmbedding(updates.content);
      }

      if (updates.metadata) {
        updateData.metadata = updates.metadata;
      }

      const { error } = await supabase
        .from('buddychip_memories')
        .update(updateData)
        .eq('id', memoryId)
        .eq('user_id', userId);

      if (error) {
        memoryLogger.error('Error updating memory', { memoryId, userId, error });
        return false;
      }

      memoryLogger.info('Memory updated successfully', { memoryId, userId });
      return true;

    } catch (error) {
      memoryLogger.error('Error in update memory', { memoryId, userId, error });
      return false;
    }
  }

  /**
   * Extract meaningful memories from a conversation message
   */
  private async extractMemories(
    message: ConversationMessage,
    options: { userId: string; metadata?: Record<string, unknown>; sessionId?: string }
  ): Promise<Array<{ content: string; metadata: Record<string, unknown> }>> {
    // Simple extraction logic - in production, use LLM for better extraction
    const memories: Array<{ content: string; metadata: Record<string, unknown> }> = [];

    // Extract preferences, facts, and important information
    const content = message.content.toLowerCase();

    // Preference patterns
    if (content.includes('i like') || content.includes('i love') || content.includes('i prefer')) {
      memories.push({
        content: message.content,
        metadata: {
          type: 'preference',
          role: message.role,
          sessionId: options.sessionId,
          extractedAt: new Date().toISOString(),
          ...options.metadata,
        },
      });
    }

    // Fact patterns
    if (content.includes('i am') || content.includes('i work') || content.includes('my name is')) {
      memories.push({
        content: message.content,
        metadata: {
          type: 'personal_fact',
          role: message.role,
          sessionId: options.sessionId,
          extractedAt: new Date().toISOString(),
          ...options.metadata,
        },
      });
    }

    // Goal patterns
    if (content.includes('i want to') || content.includes('i plan to') || content.includes('my goal')) {
      memories.push({
        content: message.content,
        metadata: {
          type: 'goal',
          role: message.role,
          sessionId: options.sessionId,
          extractedAt: new Date().toISOString(),
          ...options.metadata,
        },
      });
    }

    // If no specific patterns, store as general conversation
    if (memories.length === 0 && message.content.length > 20) {
      memories.push({
        content: message.content,
        metadata: {
          type: 'conversation',
          role: message.role,
          sessionId: options.sessionId,
          extractedAt: new Date().toISOString(),
          ...options.metadata,
        },
      });
    }

    return memories;
  }

  /**
   * Store a memory in Supabase
   */
  private async storeMemory(
    memory: { content: string; metadata: Record<string, unknown> },
    options: { userId: string }
  ): Promise<string | null> {
    try {
      const embedding = await this.getEmbedding(memory.content);
      const memoryId = `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      const supabase = await this.getSupabase();

      const { error } = await supabase
        .from('buddychip_memories')
        .insert({
          id: memoryId,
          user_id: options.userId,
          content: memory.content,
          embedding,
          metadata: memory.metadata,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (error) {
        memoryLogger.error('Error storing memory', { userId: options.userId, error });
        return null;
      }

      return memoryId;

    } catch (error) {
      memoryLogger.error('Error in storeMemory', { userId: options.userId, error });
      return null;
    }
  }

  /**
   * Get embedding for text using OpenAI
   */
  private async getEmbedding(text: string): Promise<number[]> {
    try {
      const response = await fetch('https://api.openai.com/v1/embeddings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        },
        body: JSON.stringify({
          model: 'text-embedding-3-small',
          input: text,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      return data.data[0].embedding;

    } catch (error) {
      memoryLogger.error('Error getting embedding', { error });
      throw new Error('Failed to get embedding');
    }
  }
}

// Export singleton instance
export const memoryService = new BuddyChipMemoryService();
