/**
 * Comprehensive Input Validation Schemas
 * 
 * Provides security-focused validation schemas for all API endpoints.
 * Includes XSS protection, SQL injection prevention, and data sanitization.
 */

import { z } from 'zod';

// Common validation patterns
const PATTERNS = {
  // Twitter handle: alphanumeric, underscores, 1-15 chars
  TWITTER_HANDLE: /^[a-zA-Z0-9_]{1,15}$/,
  // Tweet ID: numeric string, 1-20 chars
  TWEET_ID: /^\d{1,20}$/,
  // UUID v4 pattern
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  // Safe text: no script tags, no javascript:, no data: URLs
  SAFE_TEXT: /^(?!.*<script|.*javascript:|.*data:).*$/i,
  // URL validation
  URL: /^https?:\/\/[^\s/$.?#].[^\s]*$/i,
};

// XSS prevention function
function sanitizeText(text: string): string {
  return text
    .replace(/<script[^>]*>.*?<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/data:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .trim();
}

// Custom validation functions
const safeString = (maxLength = 1000) => z.string()
  .max(maxLength, `Text too long (max ${maxLength} characters)`)
  .refine(
    (val) => PATTERNS.SAFE_TEXT.test(val),
    'Invalid characters detected'
  )
  .transform(sanitizeText);

const twitterHandle = () => z.string()
  .min(1, 'Twitter handle is required')
  .max(15, 'Twitter handle too long')
  .refine(
    (val) => PATTERNS.TWITTER_HANDLE.test(val),
    'Invalid Twitter handle format'
  )
  .transform((val) => val.toLowerCase());

const tweetId = () => z.string()
  .min(1, 'Tweet ID is required')
  .refine(
    (val) => PATTERNS.TWEET_ID.test(val),
    'Invalid Tweet ID format'
  );

const uuid = () => z.string()
  .refine(
    (val) => PATTERNS.UUID.test(val),
    'Invalid UUID format'
  );

const safeUrl = () => z.string()
  .refine(
    (val) => PATTERNS.URL.test(val),
    'Invalid URL format'
  )
  .refine(
    (val) => !val.includes('javascript:') && !val.includes('data:'),
    'Unsafe URL detected'
  );

// Tweet analysis schemas
export const tweetAnalysisSchema = z.object({
  tweet_id: z.union([z.number().int().positive(), tweetId()]),
  content: safeString(280),
  user_id: uuid(),
  force_reanalysis: z.boolean().default(false),
});

export const bulkTweetAnalysisSchema = z.object({
  tweet_ids: z.array(z.union([z.number().int().positive(), tweetId()]))
    .min(1, 'At least one tweet ID required')
    .max(50, 'Too many tweets (max 50)'),
  user_id: uuid(),
  batch_size: z.number().int().min(1).max(10).default(10),
});

// Search and query schemas
export const searchQuerySchema = z.object({
  query: safeString(500),
  limit: z.number().int().min(1).max(50).default(10),
  offset: z.number().int().min(0).default(0),
  filters: z.object({
    account_id: z.string().optional(),
    date_from: z.string().datetime().optional(),
    date_to: z.string().datetime().optional(),
    min_engagement: z.number().min(0).optional(),
  }).optional(),
});

export const aiSearchSchema = z.object({
  query: safeString(1000),
  save_to_history: z.boolean().default(true),
  user_context: z.object({
    preferences: z.array(safeString(100)).max(10).optional(),
    previous_searches: z.array(safeString(200)).max(5).optional(),
  }).optional(),
  tools: z.array(z.enum(['perplexity', 'xai_search', 'web_search'])).optional(),
});

// Twitter account schemas
export const addTwitterAccountSchema = z.object({
  twitter_handle: twitterHandle(),
  display_name: safeString(50).optional(),
  description: safeString(200).optional(),
  user_id: uuid(),
});

export const updateTwitterAccountSchema = z.object({
  id: z.number().int().positive(),
  twitter_handle: twitterHandle().optional(),
  display_name: safeString(50).optional(),
  description: safeString(200).optional(),
  is_active: z.boolean().optional(),
});

// Copy.AI generation schemas
export const copyGenerationSchema = z.object({
  context: safeString(2000),
  theme: z.enum(['meme', 'degen', 'vc_bait', 'general']).default('general'),
  tone: z.enum(['casual', 'professional', 'humorous', 'aggressive']).default('casual'),
  length: z.enum(['short', 'medium', 'long']).default('medium'),
  include_hashtags: z.boolean().default(true),
  include_emojis: z.boolean().default(true),
  target_audience: safeString(100).optional(),
  custom_instructions: safeString(500).optional(),
});

export const replyGenerationSchema = z.object({
  tweet_id: z.union([z.number().int().positive(), tweetId()]),
  count: z.number().int().min(1).max(5).default(3),
  style: z.enum(['supportive', 'questioning', 'informative', 'humorous']).default('supportive'),
  max_length: z.number().int().min(50).max(280).default(280),
  include_mentions: z.boolean().default(false),
});

// Image generation schemas
export const imageGenerationSchema = z.object({
  prompt: safeString(1000),
  context_type: z.enum(['tweet', 'reply', 'general']).default('general'),
  context_content: safeString(500).optional(),
  style: z.enum(['realistic', 'cartoon', 'abstract', 'minimalist']).default('realistic'),
  size: z.enum(['square', 'landscape', 'portrait']).default('square'),
  quality: z.enum(['standard', 'hd']).default('standard'),
});

// User profile schemas
export const updateProfileSchema = z.object({
  username: z.string()
    .min(3, 'Username too short')
    .max(30, 'Username too long')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Invalid username format')
    .optional(),
  display_name: safeString(50).optional(),
  bio: safeString(200).optional(),
  avatar_url: safeUrl().optional(),
  preferences: z.object({
    theme: z.enum(['light', 'dark', 'auto']).default('dark'),
    notifications: z.boolean().default(true),
    auto_analyze: z.boolean().default(true),
    default_copy_theme: z.enum(['meme', 'degen', 'vc_bait', 'general']).default('general'),
  }).optional(),
});

// Pagination schemas
export const paginationSchema = z.object({
  limit: z.number().int().min(1).max(100).default(20),
  offset: z.number().int().min(0).default(0),
  cursor: z.string().optional(),
  sort_by: z.string().optional(),
  sort_order: z.enum(['asc', 'desc']).default('desc'),
});

// Token usage schemas
export const tokenUsageSchema = z.object({
  action: z.enum(['copy_generation', 'image_generation', 'tweet_analysis', 'search_query']),
  amount: z.number().positive(),
  user_id: uuid(),
  metadata: z.object({
    tweet_id: tweetId().optional(),
    query: safeString(200).optional(),
    tokens_used: z.number().int().positive().optional(),
  }).optional(),
});

// Webhook schemas
export const webhookSchema = z.object({
  event: z.string(),
  data: z.record(z.unknown()),
  timestamp: z.string().datetime(),
  signature: z.string(),
});

// File upload schemas
export const fileUploadSchema = z.object({
  filename: z.string()
    .min(1, 'Filename required')
    .max(255, 'Filename too long')
    .refine(
      (val) => !/[<>:"/\\|?*]/.test(val),
      'Invalid filename characters'
    ),
  content_type: z.string()
    .refine(
      (val) => ['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(val),
      'Invalid file type'
    ),
  size: z.number().int().min(1).max(10 * 1024 * 1024), // Max 10MB
});

// Rate limiting bypass schema (for admin use)
export const rateLimitBypassSchema = z.object({
  user_id: uuid(),
  endpoint: z.string(),
  duration_minutes: z.number().int().min(1).max(1440), // Max 24 hours
  reason: safeString(200),
});

// Export validation helpers
export const validateRequest = <T>(schema: z.ZodSchema<T>, data: unknown): T => {
  const result = schema.safeParse(data);
  if (!result.success) {
    throw new Error(`Validation failed: ${result.error.errors.map(e => e.message).join(', ')}`);
  }
  return result.data;
};

export const validateAndSanitize = <T>(schema: z.ZodSchema<T>, data: unknown): T => {
  try {
    return validateRequest(schema, data);
  } catch (error) {
    throw new Error(`Input validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Common validation middleware
export const createValidationMiddleware = <T>(schema: z.ZodSchema<T>) => {
  return (data: unknown): T => {
    return validateAndSanitize(schema, data);
  };
};
