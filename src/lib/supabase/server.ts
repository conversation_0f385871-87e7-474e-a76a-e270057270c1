import { createServerClient, type CookieOptions } from '@supabase/ssr';
import { cookies } from 'next/headers';

// NOTE: There are deprecation warnings for createServerClient.
// This is expected as Supabase is transitioning to a new API.
// The code will still work, but should be updated in the future when Supabase
// provides a stable replacement for createServerClient.

// This client is for use in Server Components, Route Handlers, and Server Actions.
// It relies on middleware to handle cookie updates (set/remove) for session management.
export async function createSupabaseServerClient() {
  const cookieStore = await cookies(); // Await the Promise from cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          // In Server Components, cookieStore.set() will throw an error
          // because it's a read-only store. Middleware handles actual cookie setting.
          // This try-catch is to prevent runtime errors if Supabase client calls set.
          try {
            cookieStore.set(name, value, options);
          } catch {
            // console.log('Attempted to set cookie in a Server Component/read-only context', name);
          }
        },
        remove(name: string) {
          // Similar to set, this will throw in Server Components.
          try {
            cookieStore.delete(name);
          } catch {
            // console.log('Attempted to delete cookie in a Server Component/read-only context', name);
          }
        },
      },
    }
  );
}

// Server client for Route Handlers (can pass a modifiable cookie store)
export async function createSupabaseRouteHandlerClient() {
  const cookieStore = await cookies();
  // In Route Handlers, cookies() from next/headers can be used to set/delete cookies
  // as it operates on the outgoing response.
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        get(name: string) {
          return cookieStore.get(name)?.value;
        },
        set(name: string, value: string, options: CookieOptions) {
          cookieStore.set(name, value, options);
        },
        remove(name: string) {
          // Fix the delete method to match the expected signature
          cookieStore.delete(name);
        },
      },
    }
  );
}

// Admin client - uses service_role key, does not interact with user session cookies.
export async function createSupabaseAdminClient() {
  if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is not set for admin client');
  }

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        // These are effectively no-ops for auth state when using service_role
        get() {
          return undefined;
        },
        set() {
          // No-op
        },
        remove() {
          // No-op
        },
      },
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      },
    }
  );
}