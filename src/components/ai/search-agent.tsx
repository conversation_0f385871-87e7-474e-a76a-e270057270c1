'use client';

/**
 * Search Agent Component
 *
 * Provides a UI for interacting with the AI search agent that uses
 * both Perplexity API and xAI Live Search tools.
 */

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import {
  Search,
  Loader2,
  ExternalLink,
  Clock,
  Zap,
  Brain,
  History,
  Copy,
  CheckCircle,
  AlertCircle,
  MessageSquare
} from 'lucide-react';

interface SearchResult {
  success: boolean;
  query: string;
  response: string;
  sources: string[];
  citations: string[];
  analysis: {
    queryType: string;
    recommendedTool: string;
    reasoning: string;
    suggestedSources: string[];
    timeframe: string;
    priority: string;
  };
  metadata: {
    requestId: string;
    executionTime: number;
    tokensUsed: number;
    timestamp: string;
  };
}

interface SearchHistoryItem {
  id: string;
  query: string;
  response: string;
  sources_used: string[];
  citations: string[];
  analysis: Record<string, unknown>;
  execution_time_ms: number;
  tokens_used: number;
  created_at: string;
}

export function SearchAgent() {
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [currentResult, setCurrentResult] = useState<SearchResult | null>(null);
  const [searchHistory, setSearchHistory] = useState<SearchHistoryItem[]>([]);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);
  const [activeTab, setActiveTab] = useState('search');
  const [copiedText, setCopiedText] = useState<string | null>(null);

  const inputRef = useRef<HTMLInputElement>(null);

  // Load search history when component mounts or when switching to history tab
  useEffect(() => {
    if (activeTab === 'history' && searchHistory.length === 0) {
      loadSearchHistory();
    }
  }, [activeTab, searchHistory.length]);

  const loadSearchHistory = async () => {
    setIsLoadingHistory(true);
    try {
      const response = await fetch('/api/ai/search-agent?limit=20');
      const data = await response.json();

      if (data.success) {
        setSearchHistory(data.searches);
      } else {
        toast.error('Failed to load search history');
      }
    } catch (error) {
      console.error('Error loading search history:', error);
      toast.error('Failed to load search history');
    } finally {
      setIsLoadingHistory(false);
    }
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!query.trim()) {
      toast.error('Please enter a search query');
      return;
    }

    setIsSearching(true);
    setCurrentResult(null);

    try {
      const response = await fetch('/api/ai/search-agent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: query.trim(),
          save_to_history: true,
        }),
      });

      const data = await response.json();

      if (data.success) {
        setCurrentResult(data);
        toast.success('Search completed successfully');

        // Add to history if we're viewing history
        if (activeTab === 'history') {
          loadSearchHistory();
        }
      } else {
        toast.error(data.error || 'Search failed');
      }
    } catch (error) {
      console.error('Search error:', error);
      toast.error('Failed to perform search');
    } finally {
      setIsSearching(false);
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      toast.success(`${label} copied to clipboard`);
      setTimeout(() => setCopiedText(null), 2000);
    } catch {
      toast.error('Failed to copy to clipboard');
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString();
  };

  const getSourceIcon = (source: string) => {
    switch (source) {
      case 'perplexity':
        return <Brain className="h-4 w-4" />;
      case 'xai_live_search':
        return <Zap className="h-4 w-4" />;
      case 'combined':
        return <Search className="h-4 w-4" />;
      default:
        return <Search className="h-4 w-4" />;
    }
  };

  const getSourceColor = (source: string) => {
    switch (source) {
      case 'perplexity':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'xai_live_search':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'combined':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <div className="w-full max-w-6xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">AI Search Agent</h1>
        <p className="text-muted-foreground">
          Powered by Perplexity AI and xAI Live Search for comprehensive research
        </p>
        <div className="flex items-center justify-center gap-2 pt-2">
          <Button variant="outline" size="sm" asChild>
            <a href="/chat" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Try Chat Assistant
            </a>
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="search" className="flex items-center gap-2">
            <Search className="h-4 w-4" />
            Search
          </TabsTrigger>
          <TabsTrigger value="history" className="flex items-center gap-2">
            <History className="h-4 w-4" />
            History
          </TabsTrigger>
        </TabsList>

        <TabsContent value="search" className="space-y-6">
          {/* Search Form */}
          <Card>
            <CardHeader>
              <CardTitle>Search Query</CardTitle>
              <CardDescription>
                Enter your question or research topic. The AI will automatically choose the best tools.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSearch} className="flex gap-2">
                <Input
                  ref={inputRef}
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                  placeholder="Ask anything... (e.g., 'What are the latest developments in AI?' or 'Recent news about climate change')"
                  disabled={isSearching}
                  className="flex-1"
                />
                <Button type="submit" disabled={isSearching || !query.trim()}>
                  {isSearching ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                  {isSearching ? 'Searching...' : 'Search'}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Search Results */}
          {currentResult && (
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">Search Results</CardTitle>
                    <CardDescription>
                      Query: &quot;{currentResult.query}&quot;
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    {currentResult.sources.map((source, index) => (
                      <Badge key={index} className={getSourceColor(source)}>
                        {getSourceIcon(source)}
                        <span className="ml-1 capitalize">
                          {source.replace('_', ' ')}
                        </span>
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Analysis */}
                <div className="bg-muted/50 rounded-lg p-4 space-y-2">
                  <h4 className="font-semibold flex items-center gap-2">
                    <Brain className="h-4 w-4" />
                    Query Analysis
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Type:</span>
                      <p className="capitalize">{currentResult.analysis.queryType}</p>
                    </div>
                    <div>
                      <span className="font-medium">Tool:</span>
                      <p className="capitalize">{currentResult.analysis.recommendedTool}</p>
                    </div>
                    <div>
                      <span className="font-medium">Timeframe:</span>
                      <p className="capitalize">{currentResult.analysis.timeframe}</p>
                    </div>
                    <div>
                      <span className="font-medium">Priority:</span>
                      <p className="capitalize">{currentResult.analysis.priority}</p>
                    </div>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {currentResult.analysis.reasoning}
                  </p>
                </div>

                {/* Response */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h4 className="font-semibold">Response</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(currentResult.response, 'Response')}
                    >
                      {copiedText === 'Response' ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                  <ScrollArea className="h-96 w-full rounded-md border p-4">
                    <div className="whitespace-pre-wrap text-sm">
                      {currentResult.response}
                    </div>
                  </ScrollArea>
                </div>

                {/* Citations */}
                {currentResult.citations.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-semibold">Citations</h4>
                    <div className="space-y-1">
                      {currentResult.citations.map((citation, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm">
                          <ExternalLink className="h-3 w-3" />
                          <a
                            href={citation}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline truncate"
                          >
                            {citation}
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Metadata */}
                <Separator />
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <div className="flex items-center gap-4">
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {currentResult.metadata.executionTime}ms
                    </span>
                    <span>
                      {currentResult.metadata.tokensUsed} tokens
                    </span>
                  </div>
                  <span>
                    {formatTimestamp(currentResult.metadata.timestamp)}
                  </span>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Search History</h3>
            <Button
              variant="outline"
              size="sm"
              onClick={loadSearchHistory}
              disabled={isLoadingHistory}
            >
              {isLoadingHistory ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <History className="h-4 w-4" />
              )}
              Refresh
            </Button>
          </div>

          {isLoadingHistory ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : searchHistory.length === 0 ? (
            <Card>
              <CardContent className="flex items-center justify-center py-8">
                <div className="text-center space-y-2">
                  <AlertCircle className="h-8 w-8 mx-auto text-muted-foreground" />
                  <p className="text-muted-foreground">No search history found</p>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {searchHistory.map((item) => (
                <Card key={item.id}>
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="space-y-1">
                        <CardTitle className="text-base">{item.query}</CardTitle>
                        <CardDescription>
                          {formatTimestamp(item.created_at)}
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        {item.sources_used.map((source, index) => (
                          <Badge key={index} className={getSourceColor(source)}>
                            {getSourceIcon(source)}
                            <span className="ml-1 capitalize">
                              {source.replace('_', ' ')}
                            </span>
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <ScrollArea className="h-32 w-full">
                      <div className="text-sm text-muted-foreground whitespace-pre-wrap">
                        {item.response.substring(0, 500)}
                        {item.response.length > 500 && '...'}
                      </div>
                    </ScrollArea>
                    <div className="flex items-center justify-between mt-4 text-xs text-muted-foreground">
                      <div className="flex items-center gap-4">
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {item.execution_time_ms}ms
                        </span>
                        <span>{item.tokens_used} tokens</span>
                        {item.citations.length > 0 && (
                          <span>{item.citations.length} citations</span>
                        )}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setQuery(item.query);
                          setActiveTab('search');
                        }}
                      >
                        Search Again
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
