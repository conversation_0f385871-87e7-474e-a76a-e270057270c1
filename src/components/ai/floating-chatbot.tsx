'use client';

/**
 * Floating Chatbot Widget
 *
 * A floating chat icon in the bottom right corner that opens a compact
 * chatbot interface with persistent memory and search capabilities.
 */

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import dynamic from 'next/dynamic';

// Dynamic imports to prevent SSR issues
const ReactMarkdown = dynamic(() => import('react-markdown'), {
  ssr: false,
  loading: () => <div className="text-sm">Loading...</div>
});

const SyntaxHighlighter = dynamic(
  () => import('react-syntax-highlighter').then(mod => mod.Prism),
  {
    ssr: false,
    loading: () => <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-xs">Loading code...</div>
  }
);


import {
  Send,
  Loader2,
  Bot,
  User,
  Brain,
  Search,
  X,
  Minimize2,
  Maximize2,
  Clock,
  Zap,
  Settings,
  Sparkles,
  Database,
  Globe,
  MessageSquare,
  BookOpen,
  ChevronUp,
  Expand,
  Shrink
} from 'lucide-react';
import { SyntaxHighlighterProps } from 'react-syntax-highlighter';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  metadata?: {
    toolsUsed?: string[];
    searchResults?: Record<string, unknown>;
    memoryContextUsed?: number;
  };
}

interface ChatSession {
  sessionId: string;
  messages: Message[];
  lastActivity: string;
  isActive: boolean;
}

interface ToolConfig {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  enabled: boolean;
  category: 'search' | 'memory' | 'analysis';
}

const defaultTools: ToolConfig[] = [
  {
    id: 'perplexity',
    name: 'Perplexity Search',
    description: 'Comprehensive web research and analysis',
    icon: <BookOpen className="h-4 w-4" />,
    enabled: true,
    category: 'search'
  },
  {
    id: 'xai_live',
    name: 'xAI Live Search',
    description: 'Real-time data and social media content',
    icon: <MessageSquare className="h-4 w-4" />,
    enabled: true,
    category: 'search'
  },
  {
    id: 'memory_retrieval',
    name: 'Memory Retrieval',
    description: 'Access previous conversation context',
    icon: <Brain className="h-4 w-4" />,
    enabled: true,
    category: 'memory'
  },
  {
    id: 'memory_storage',
    name: 'Memory Storage',
    description: 'Save important information for later',
    icon: <Database className="h-4 w-4" />,
    enabled: true,
    category: 'memory'
  },
  {
    id: 'web_search',
    name: 'General Web Search',
    description: 'Basic web search capabilities',
    icon: <Globe className="h-4 w-4" />,
    enabled: false,
    category: 'search'
  }
];

// Markdown component with syntax highlighting - client-side only
const MarkdownMessage = ({ content }: { content: string }) => {
  const [isMounted, setIsMounted] = useState(false);
  const [syntaxStyle, setSyntaxStyle] = useState<Record<string, React.CSSProperties> | null>(null);

  useEffect(() => {
    setIsMounted(true);
    // Load syntax highlighting style
    if (typeof window !== 'undefined') {
      import('react-syntax-highlighter/dist/esm/styles/prism').then(styles => {
        setSyntaxStyle(styles.oneDark);
      });
    }
  }, []);

  // Fallback to plain text during SSR to prevent hydration mismatch
  if (!isMounted) {
    return (
      <div className="whitespace-pre-wrap text-sm">
        {content}
      </div>
    );
  }

  return (
    <div className="prose prose-sm dark:prose-invert max-w-none">
      <ReactMarkdown
        components={{
          code(props: React.HTMLAttributes<HTMLElement> & { className?: string; children?: React.ReactNode }) {
            const { className, children, style, ...restProps } = props;
            const match = /language-(\w+)/.exec(className || '');
            const isInline = !match;

            if (isInline) {
              return (
                <code className="bg-gray-200 dark:bg-gray-700 px-1 py-0.5 rounded text-xs" {...restProps}>
                  {children}
                </code>
              );
            }

            // Only render SyntaxHighlighter if we have the style loaded
            if (syntaxStyle && SyntaxHighlighter) {
              return (
                <SyntaxHighlighter
                  language={match[1]}
                  PreTag="div"
                  className="rounded-md text-xs !mt-2 !mb-2"
                  {...restProps}
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              );
            }

            // Fallback to simple code block
            return (
              <pre className="bg-gray-100 dark:bg-gray-800 p-2 rounded text-xs overflow-x-auto">
                <code>{children}</code>
              </pre>
            );
          },
          p: ({ children }) => <p className="mb-2 last:mb-0 text-sm">{children}</p>,
          ul: ({ children }) => <ul className="list-disc list-inside mb-2 space-y-1 text-sm">{children}</ul>,
          ol: ({ children }) => <ol className="list-decimal list-inside mb-2 space-y-1 text-sm">{children}</ol>,
          li: ({ children }) => <li className="text-sm">{children}</li>,
          h1: ({ children }) => <h1 className="text-base font-bold mb-2">{children}</h1>,
          h2: ({ children }) => <h2 className="text-sm font-bold mb-2">{children}</h2>,
          h3: ({ children }) => <h3 className="text-sm font-semibold mb-1">{children}</h3>,
          blockquote: ({ children }) => (
            <blockquote className="border-l-4 border-gray-300 dark:border-gray-600 pl-4 italic mb-2 text-sm">
              {children}
            </blockquote>
          ),
          a(props: React.AnchorHTMLAttributes<HTMLAnchorElement> & { children?: React.ReactNode; href?: string }) {
            const { children, href, ...restProps } = props;
            return (
              <a href={href} target="_blank" rel="noopener noreferrer" className="text-blue-600 dark:text-blue-400 hover:underline text-sm" {...restProps}>
                {children}
              </a>
            );
          },
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export function FloatingChatbot() {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [tools, setTools] = useState<ToolConfig[]>(defaultTools);
  const [hasUnreadMessages, setHasUnreadMessages] = useState(false);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (isOpen && !isMinimized) {
      // Use a small delay to ensure the DOM has updated
      setTimeout(() => {
        // Try multiple scroll methods for better compatibility
        if (messagesEndRef.current) {
          messagesEndRef.current.scrollIntoView({
            behavior: 'smooth',
            block: 'end',
            inline: 'nearest'
          });
        }

        // Fallback: scroll the container directly
        if (scrollAreaRef.current) {
          scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
        }
      }, 100);
    }
  }, [currentSession?.messages, isOpen, isMinimized]);

  // Focus input when chat opens
  useEffect(() => {
    if (isOpen && !isMinimized && inputRef.current) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [isOpen, isMinimized]);

  // Mark messages as read when chat is opened
  useEffect(() => {
    if (isOpen) {
      setHasUnreadMessages(false);
    }
  }, [isOpen]);

  const startNewSession = () => {
    const newSession: ChatSession = {
      sessionId: `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      messages: [],
      lastActivity: new Date().toISOString(),
      isActive: true,
    };
    setCurrentSession(newSession);
  };

  const toggleChat = () => {
    if (!isOpen) {
      setIsOpen(true);
      if (!currentSession) {
        startNewSession();
      }
    } else {
      setIsOpen(false);
    }
  };

  const toggleMinimize = () => {
    setIsMinimized(!isMinimized);
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
    if (!isFullscreen) {
      setIsMinimized(false); // Ensure it's not minimized when going fullscreen
    }
  };

  const toggleTool = (toolId: string) => {
    setTools(prev => prev.map(tool => {
      if (tool.id === toolId) {
        const newEnabled = !tool.enabled;
        console.log(`Tool ${tool.name} ${newEnabled ? 'enabled' : 'disabled'}`);
        toast.success(`${tool.name} ${newEnabled ? 'enabled' : 'disabled'}`);
        return { ...tool, enabled: newEnabled };
      }
      return tool;
    }));
  };

  const getEnabledTools = () => {
    return tools.filter(tool => tool.enabled);
  };

  const hasSearchTools = () => {
    return tools.some(tool => tool.category === 'search' && tool.enabled);
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim()) {
      toast.error('Please enter a message');
      return;
    }

    if (!currentSession) {
      startNewSession();
      return;
    }

    const userMessage: Message = {
      id: `msg_${Date.now()}_user`,
      role: 'user',
      content: message.trim(),
      timestamp: new Date().toISOString(),
    };

    // Add user message to current session
    setCurrentSession(prev => prev ? {
      ...prev,
      messages: [...prev.messages, userMessage],
      lastActivity: new Date().toISOString(),
    } : null);

    setMessage('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/ai/search-chatbot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          sessionId: currentSession.sessionId,
          conversationHistory: currentSession.messages.map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          includeSearch: hasSearchTools(),
          enabledTools: getEnabledTools().map(tool => tool.id),
        }),
      });

      const data = await response.json();

      if (data.success) {
        const assistantMessage: Message = {
          id: `msg_${Date.now()}_assistant`,
          role: 'assistant',
          content: data.response,
          timestamp: new Date().toISOString(),
          metadata: {
            toolsUsed: data.metadata.toolsUsed,
            searchResults: data.metadata.searchResults,
            memoryContextUsed: data.metadata.memoryContextUsed,
          },
        };

        setCurrentSession(prev => prev ? {
          ...prev,
          messages: [...prev.messages, assistantMessage],
          lastActivity: new Date().toISOString(),
        } : null);

        // Show unread indicator if chat is closed
        if (!isOpen) {
          setHasUnreadMessages(true);
        }

        toast.success('Response received');
      } else {
        toast.error(data.error || 'Failed to get response');
      }
    } catch (error) {
      console.error('Chat error:', error);
      toast.error('Failed to send message');
    } finally {
      setIsLoading(false);
    }
  };

  const getToolIcon = (tool: string) => {
    switch (tool) {
      case 'searchExecutionTool':
        return <Search className="h-3 w-3" />;
      case 'memoryRetrievalTool':
        return <Brain className="h-3 w-3" />;
      case 'memoryStorageTool':
        return <Clock className="h-3 w-3" />;
      default:
        return <Zap className="h-3 w-3" />;
    }
  };

  return (
    <>
      {/* Floating Chat Button */}
      {!isOpen && (
        <div className="fixed bottom-6 right-6 z-50">
          <div className="relative group">
            {/* Pulse animation for unread messages */}
            {hasUnreadMessages && (
              <div className="absolute inset-0 rounded-full bg-blue-400 animate-ping opacity-75"></div>
            )}

            <Button
              onClick={toggleChat}
              size="lg"
              className="h-16 w-16 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-300 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 relative group-hover:scale-110 border-2 border-white/20"
            >
              <div className="relative">
                <Sparkles className="h-7 w-7 text-white" />
                {hasUnreadMessages && (
                  <div className="absolute -top-2 -right-2 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center border-2 border-white">
                    <div className="h-2 w-2 bg-white rounded-full"></div>
                  </div>
                )}
              </div>
            </Button>

            {/* Tooltip */}
            <div className="absolute bottom-full right-0 mb-2 px-3 py-1 bg-black/80 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap">
              AI Assistant
              <div className="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black/80"></div>
            </div>
          </div>
        </div>
      )}

      {/* Chat Widget */}
      {isOpen && (
        <div className={`fixed z-50 ${
          isFullscreen
            ? 'inset-4'
            : 'bottom-4 right-4 w-80 sm:w-96 h-[500px] sm:h-[600px]'
        }`}>
          <Card className="h-full flex flex-col shadow-xl border bg-white dark:bg-gray-900">
            {/* Header */}
            <CardHeader className="flex-shrink-0 p-4 border-b">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                    <Bot className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <CardTitle className="text-sm">AI Assistant</CardTitle>
                  </div>
                </div>
                <div className="flex items-center gap-1">
                  <div className="flex items-center gap-1 mr-2">
                    <div className="text-xs text-muted-foreground">
                      {getEnabledTools().length} tools
                    </div>
                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSettings(!showSettings)}
                    className="h-8 w-8 p-0"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={toggleFullscreen}
                    className="h-8 w-8 p-0"
                    title={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
                  >
                    {isFullscreen ? <Shrink className="h-4 w-4" /> : <Expand className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={toggleMinimize}
                    className="h-8 w-8 p-0"
                  >
                    {isMinimized ? <Maximize2 className="h-4 w-4" /> : <Minimize2 className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="h-8 w-8 p-0"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>

            {!isMinimized && (
              <>
                {/* Settings Panel */}
                {showSettings && (
                  <div className="border-b bg-gray-50 dark:bg-gray-800 p-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h4 className="text-sm font-medium">AI Tools</h4>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowSettings(false)}
                          className="h-6 w-6 p-0"
                        >
                          <ChevronUp className="h-3 w-3" />
                        </Button>
                      </div>

                      <div className="space-y-2">
                        {tools.map((tool) => (
                          <div key={tool.id} className="flex items-center justify-between p-2 rounded-lg bg-white dark:bg-gray-700 border">
                            <div className="flex items-center gap-2">
                              {tool.icon}
                              <div>
                                <div className="text-xs font-medium">{tool.name}</div>
                                <div className="text-xs text-muted-foreground">{tool.description}</div>
                              </div>
                            </div>
                            <Button
                              variant={tool.enabled ? "default" : "outline"}
                              size="sm"
                              onClick={() => toggleTool(tool.id)}
                              className="h-6 text-xs px-2"
                            >
                              {tool.enabled ? 'ON' : 'OFF'}
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                <Separator />

                {/* Messages Area */}
                <div className="flex-1 min-h-0 overflow-hidden">
                  <div
                    ref={scrollAreaRef}
                    className="h-full overflow-y-auto overflow-x-hidden scrollbar-thin"
                  >
                    <div className="p-4 space-y-3">
                      {!currentSession || currentSession.messages.length === 0 ? (
                        <div className="flex items-center justify-center min-h-[200px]">
                          <div className="text-center space-y-3">
                            <div className="w-12 h-12 mx-auto rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                              <Bot className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                            </div>
                            <div>
                              <h3 className="text-sm font-medium mb-1">AI Assistant</h3>
                              <p className="text-xs text-muted-foreground">
                                How can I help you today?
                              </p>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <>
                          {currentSession.messages.map((msg) => (
                          <div
                            key={msg.id}
                            className={`flex gap-2 ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
                          >
                            {msg.role === 'assistant' && (
                              <div className="flex-shrink-0 mt-1">
                                <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                  <Bot className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                                </div>
                              </div>
                            )}

                            <div className={`${isFullscreen ? 'max-w-[85%]' : 'max-w-[75%]'} ${msg.role === 'user' ? 'order-1' : ''}`}>
                              <div
                                className={`rounded-lg p-3 text-sm ${
                                  msg.role === 'user'
                                    ? 'bg-blue-600 text-white'
                                    : 'bg-gray-100 dark:bg-gray-800'
                                }`}
                              >
                                {msg.role === 'assistant' ? (
                                  <MarkdownMessage content={msg.content} />
                                ) : (
                                  <div className="whitespace-pre-wrap">
                                    {msg.content}
                                  </div>
                                )}
                              </div>

                              {msg.metadata?.toolsUsed && msg.metadata.toolsUsed.length > 0 && (
                                <div className="flex items-center gap-1 mt-1">
                                  {msg.metadata.toolsUsed.slice(0, 2).map((tool, index) => (
                                    <Badge key={index} variant="secondary" className="text-xs h-4 px-1">
                                      {getToolIcon(tool)}
                                    </Badge>
                                  ))}
                                  {msg.metadata.toolsUsed.length > 2 && (
                                    <span className="text-xs text-muted-foreground">+{msg.metadata.toolsUsed.length - 2}</span>
                                  )}
                                </div>
                              )}
                            </div>

                            {msg.role === 'user' && (
                              <div className="flex-shrink-0 mt-1">
                                <div className="w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                  <User className="h-3 w-3 text-gray-600 dark:text-gray-300" />
                                </div>
                              </div>
                            )}
                          </div>
                        ))}

                        {isLoading && (
                          <div className="flex gap-2 justify-start">
                            <div className="flex-shrink-0 mt-1">
                              <div className="w-6 h-6 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                                <Loader2 className="h-3 w-3 animate-spin text-blue-600 dark:text-blue-400" />
                              </div>
                            </div>
                            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-3">
                              <div className="text-sm text-muted-foreground">
                                Thinking...
                              </div>
                            </div>
                          </div>
                        )}

                          <div ref={messagesEndRef} />
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Input Area */}
                <div className="p-4 flex-shrink-0">
                  <form onSubmit={sendMessage} className="flex gap-2">
                    <Input
                      ref={inputRef}
                      value={message}
                      onChange={(e) => setMessage(e.target.value)}
                      placeholder="Type a message..."
                      disabled={isLoading}
                      className="flex-1 text-sm"
                      maxLength={500}
                    />
                    <Button
                      type="submit"
                      disabled={isLoading || !message.trim()}
                      size="sm"
                    >
                      {isLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <Send className="h-4 w-4" />
                      )}
                    </Button>
                  </form>
                </div>
              </>
            )}
          </Card>
        </div>
      )}
    </>
  );
}
