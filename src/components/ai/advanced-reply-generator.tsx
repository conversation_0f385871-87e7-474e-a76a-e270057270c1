'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { <PERSON>ader2, <PERSON><PERSON><PERSON>, Brain, Shield, MessageSquare, ImageIcon } from 'lucide-react';
import { toast } from 'sonner';
import { ImageGenerationButton } from '@/components/ui/image-generation-button';

interface AdvancedSuggestion {
  reply: string;
  confidence: number;
  type: string;
  reasoning: string;
}

interface AdvancedReplyResponse {
  suggestions: AdvancedSuggestion[];
  analysis: {
    tweetAnalysis: Record<string, unknown>;
    userContext: Record<string, unknown>;
    toolsUsed: string[];
    processingSteps: number;
  };
  generation_method: string;
}

interface AdvancedReplyGeneratorProps {
  tweetId: string;
  tweetContent: string;
  tweetAuthor?: string;
  tweetMetrics?: {
    likes: number;
    retweets: number;
    replies: number;
  };
  onSuggestionsGenerated?: (suggestions: AdvancedSuggestion[]) => void;
}

export function AdvancedReplyGenerator({
  tweetId,
  tweetContent,
  tweetAuthor,
  tweetMetrics,
  onSuggestionsGenerated,
}: AdvancedReplyGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [suggestions, setSuggestions] = useState<AdvancedSuggestion[]>([]);
  const [analysis, setAnalysis] = useState<AdvancedReplyResponse['analysis'] | null>(null);
  const [error, setError] = useState<string | null>(null);

  const generateAdvancedReplies = async () => {
    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch('/api/ai/generate-reply-advanced', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tweetId,
          tweetContent,
          tweetAuthor: tweetAuthor || null,
          tweetMetrics: tweetMetrics || null,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate advanced replies');
      }

      const data: AdvancedReplyResponse = await response.json();

      setSuggestions(data.suggestions);
      setAnalysis(data.analysis);
      onSuggestionsGenerated?.(data.suggestions);

      toast.success(`Generated ${data.suggestions.length} advanced reply suggestions using AI agents!`);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'supportive':
        return '💪';
      case 'questioning':
        return '❓';
      case 'informative':
        return '📚';
      case 'humorous':
        return '😄';
      default:
        return '💬';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-600" />
            Advanced AI Reply Generator
          </CardTitle>
          <CardDescription>
            Generate contextual replies using AI agents with multiple tools for analysis,
            context understanding, and safety validation.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Tweet Preview */}
            <div className="p-4 bg-gray-50 rounded-lg border">
              <p className="text-sm text-gray-600 mb-2">Original Tweet:</p>
              <p className="text-gray-900">{tweetContent}</p>
            </div>

            {/* Generate Button */}
            <Button
              onClick={generateAdvancedReplies}
              disabled={isGenerating}
              className="w-full"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  AI Agents Processing...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  Generate Advanced Replies
                </>
              )}
            </Button>

            {/* Error Display */}
            {error && (
              <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-800 text-sm">{error}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Analysis Results */}
      {analysis && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-blue-600" />
              AI Analysis Results
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium text-sm text-gray-700">Tools Used:</h4>
                <div className="flex flex-wrap gap-1">
                  {analysis.toolsUsed?.map((tool: string, index: number) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tool}
                    </Badge>
                  ))}
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium text-sm text-gray-700">Processing Steps:</h4>
                <Badge variant="outline">{analysis.processingSteps} steps</Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Generated Suggestions */}
      {suggestions.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <MessageSquare className="h-5 w-5 text-green-600" />
            Generated Reply Suggestions
          </h3>

          {suggestions.map((suggestion, index) => (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="space-y-4">
                  {/* Reply Content */}
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <p className="text-gray-900">{suggestion.reply}</p>
                  </div>

                  {/* Metadata */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Badge variant="outline" className="flex items-center gap-1">
                        <span>{getTypeIcon(suggestion.type)}</span>
                        {suggestion.type}
                      </Badge>
                      <Badge
                        className={`${getConfidenceColor(suggestion.confidence)} border-0`}
                      >
                        {Math.round(suggestion.confidence * 100)}% confidence
                      </Badge>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        Copy Reply
                      </Button>
                      <ImageGenerationButton
                        contextType="reply"
                        contextContent={`Original: ${tweetContent}\n\nReply: ${suggestion.reply}`}
                        defaultPrompt={`Create an image to accompany this reply: ${suggestion.reply.substring(0, 100)}${suggestion.reply.length > 100 ? '...' : ''}`}
                        tweetId={tweetId}
                        variant="outline"
                        size="sm"
                      >
                        <ImageIcon className="h-4 w-4" />
                      </ImageGenerationButton>
                    </div>
                  </div>

                  {/* Reasoning */}
                  <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                    <div className="flex items-start gap-2">
                      <Shield className="h-4 w-4 mt-0.5 text-gray-500" />
                      <div>
                        <p className="font-medium text-gray-700 mb-1">AI Reasoning:</p>
                        <p>{suggestion.reasoning}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Loading State */}
      {isGenerating && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-center space-y-4 py-8">
              <div className="text-center space-y-4">
                <Loader2 className="h-8 w-8 animate-spin mx-auto text-purple-600" />
                <div className="space-y-2">
                  <p className="text-sm font-medium">AI Agents Working...</p>
                  <p className="text-xs text-gray-600">
                    Analyzing tweet • Understanding context • Generating replies • Validating safety
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
