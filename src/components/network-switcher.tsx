'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { ChevronDown, Wifi, WifiOff, AlertTriangle } from 'lucide-react';
import { useNetwork, NetworkType, getNetworkDisplayInfo, isProductionNetwork } from '@/contexts/network-context';
import { cn } from '@/lib/utils';

interface NetworkSwitcherProps {
  variant?: 'default' | 'compact' | 'badge';
  showChainId?: boolean;
  className?: string;
}

export function NetworkSwitcher({ 
  variant = 'default', 
  showChainId = false,
  className 
}: NetworkSwitcherProps) {
  const { currentNetwork, setNetwork, availableNetworks, isMainnet, isDevnet } = useNetwork();
  const currentNetworkInfo = getNetworkDisplayInfo(currentNetwork);

  const getNetworkIcon = (network: NetworkType) => {
    if (network === 'mainnet-beta') {
      return <Wifi className="h-4 w-4 text-green-500" />;
    } else if (network === 'devnet') {
      return <WifiOff className="h-4 w-4 text-orange-500" />;
    } else {
      return <AlertTriangle className="h-4 w-4 text-blue-500" />;
    }
  };

  const getNetworkBadgeVariant = (network: NetworkType) => {
    if (network === 'mainnet-beta') return 'default';
    if (network === 'devnet') return 'secondary';
    return 'outline';
  };

  const getNetworkColor = (network: NetworkType) => {
    const info = getNetworkDisplayInfo(network);
    switch (info.color) {
      case 'green':
        return 'text-green-600 dark:text-green-400';
      case 'orange':
        return 'text-orange-600 dark:text-orange-400';
      case 'blue':
        return 'text-blue-600 dark:text-blue-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  if (variant === 'badge') {
    return (
      <Badge 
        variant={getNetworkBadgeVariant(currentNetwork)}
        className={cn("cursor-pointer", className)}
      >
        <div className="flex items-center gap-1">
          {getNetworkIcon(currentNetwork)}
          <span>{currentNetworkInfo.name}</span>
          {showChainId && (
            <span className="text-xs opacity-70">({currentNetworkInfo.chainId})</span>
          )}
        </div>
      </Badge>
    );
  }

  if (variant === 'compact') {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            size="sm" 
            className={cn("h-8 px-2", className)}
          >
            {getNetworkIcon(currentNetwork)}
            <span className="ml-1 text-xs">{currentNetworkInfo.name}</span>
            <ChevronDown className="h-3 w-3 ml-1" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-48">
          <DropdownMenuLabel>Solana Network</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {availableNetworks.map((network) => {
            const networkInfo = getNetworkDisplayInfo(network);
            const isSelected = network === currentNetwork;
            const isProd = isProductionNetwork(network);
            
            return (
              <DropdownMenuItem
                key={network}
                onClick={() => setNetwork(network)}
                className={cn(
                  "flex items-center justify-between cursor-pointer",
                  isSelected && "bg-accent"
                )}
              >
                <div className="flex items-center gap-2">
                  {getNetworkIcon(network)}
                  <span className={getNetworkColor(network)}>
                    {networkInfo.name}
                  </span>
                  {isProd && (
                    <Badge variant="outline" className="text-xs">
                      LIVE
                    </Badge>
                  )}
                </div>
                {showChainId && (
                  <span className="text-xs text-muted-foreground">
                    {networkInfo.chainId}
                  </span>
                )}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className={cn("justify-between min-w-[140px]", className)}
        >
          <div className="flex items-center gap-2">
            {getNetworkIcon(currentNetwork)}
            <span className={getNetworkColor(currentNetwork)}>
              {currentNetworkInfo.name}
            </span>
            {isMainnet && (
              <Badge variant="outline" className="text-xs">
                LIVE
              </Badge>
            )}
          </div>
          <ChevronDown className="h-4 w-4 ml-2" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel className="flex items-center gap-2">
          <Wifi className="h-4 w-4" />
          Solana Network
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        {availableNetworks.map((network) => {
          const networkInfo = getNetworkDisplayInfo(network);
          const isSelected = network === currentNetwork;
          const isProd = isProductionNetwork(network);
          
          return (
            <DropdownMenuItem
              key={network}
              onClick={() => setNetwork(network)}
              className={cn(
                "flex items-center justify-between cursor-pointer",
                isSelected && "bg-accent"
              )}
            >
              <div className="flex items-center gap-2">
                {getNetworkIcon(network)}
                <div className="flex flex-col">
                  <span className={getNetworkColor(network)}>
                    {networkInfo.name}
                  </span>
                  {showChainId && (
                    <span className="text-xs text-muted-foreground">
                      Chain ID: {networkInfo.chainId}
                    </span>
                  )}
                </div>
                {isProd && (
                  <Badge variant="outline" className="text-xs">
                    LIVE
                  </Badge>
                )}
              </div>
              {isSelected && (
                <div className="h-2 w-2 rounded-full bg-green-500" />
              )}
            </DropdownMenuItem>
          );
        })}
        <DropdownMenuSeparator />
        <div className="px-2 py-1">
          <p className="text-xs text-muted-foreground">
            {isMainnet ? (
              "⚠️ You're on mainnet - real transactions"
            ) : (
              "🧪 Development network - test transactions only"
            )}
          </p>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

// Network status indicator component
export function NetworkStatus() {
  const { currentNetwork, isMainnet } = useNetwork();
  const networkInfo = getNetworkDisplayInfo(currentNetwork);

  return (
    <div className="flex items-center gap-2 text-sm">
      <div className={cn(
        "h-2 w-2 rounded-full",
        isMainnet ? "bg-green-500" : "bg-orange-500"
      )} />
      <span className="text-muted-foreground">
        {networkInfo.name}
      </span>
    </div>
  );
}

// Warning component for mainnet
export function MainnetWarning() {
  const { isMainnet } = useNetwork();

  if (!isMainnet) return null;

  return (
    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-3 mb-4">
      <div className="flex items-center gap-2">
        <AlertTriangle className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
        <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
          Mainnet Mode
        </span>
      </div>
      <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
        You're connected to Solana mainnet. All transactions will use real SOL and tokens.
      </p>
    </div>
  );
}
