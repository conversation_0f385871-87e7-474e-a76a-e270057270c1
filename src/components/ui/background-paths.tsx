'use client';

import React from 'react';

interface BackgroundPathsProps {
  className?: string;
}

const BackgroundPaths = React.memo(function BackgroundPaths({ className }: BackgroundPathsProps) {
  return (
    <svg
      className={className}
      width="100%"
      height="100%"
      viewBox="0 0 1000 1000"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="rgba(59, 130, 246, 0.1)" />
          <stop offset="100%" stopColor="rgba(147, 51, 234, 0.1)" />
        </linearGradient>
      </defs>
      <path
        d="M0,300 Q250,200 500,300 T1000,300 L1000,1000 L0,1000 Z"
        fill="url(#gradient1)"
        opacity="0.3"
      />
      <path
        d="M0,500 Q250,400 500,500 T1000,500 L1000,1000 L0,1000 Z"
        fill="url(#gradient1)"
        opacity="0.2"
      />
    </svg>
  );
});

export default BackgroundPaths;
