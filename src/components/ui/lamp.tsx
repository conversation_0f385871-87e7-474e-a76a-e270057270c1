'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface LampProps {
  className?: string;
}

const Lamp = React.memo(function Lamp({ className }: LampProps) {
  return (
    <div className={cn("relative", className)}>
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-3xl opacity-20 animate-pulse" />
      <div className="relative bg-gradient-to-r from-blue-500 to-purple-500 rounded-full p-8 shadow-2xl">
        <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse" />
        </div>
      </div>
    </div>
  );
});

export default Lamp;
