"use client";

import React from "react";
import { motion } from "framer-motion";

export function WaveBackground({
  className = "",
  waveOpacity = 0.15,
  colors = ["#3da9fc", "#90b4ce"],
  animate = true,
}: {
  className?: string;
  waveOpacity?: number;
  colors?: string[];
  animate?: boolean;
}) {
  return (
    <div className={`absolute inset-0 overflow-hidden pointer-events-none ${className}`}>
      {/* First wave */}
      <motion.div
        className="absolute inset-0 z-0"
        initial={animate ? { y: 0 } : undefined}
        animate={animate ? { y: [15, -15, 15] } : undefined}
        transition={animate ? { repeat: Infinity, duration: 20, ease: "easeInOut" } : undefined}
      >
        <svg
          className="absolute w-full min-w-[1000px]"
          style={{ bottom: "0", left: "0", right: "0" }}
          viewBox="0 0 1440 320"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill={colors[0]}
            fillOpacity={waveOpacity}
            d="M0,224L48,213.3C96,203,192,181,288,181.3C384,181,480,203,576,224C672,245,768,267,864,261.3C960,256,1056,224,1152,197.3C1248,171,1344,149,1392,138.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
          ></path>
        </svg>
      </motion.div>

      {/* Second wave */}
      <motion.div
        className="absolute inset-0 z-0"
        initial={animate ? { y: 0 } : undefined}
        animate={animate ? { y: [-10, 10, -10] } : undefined}
        transition={animate ? { repeat: Infinity, duration: 15, ease: "easeInOut" } : undefined}
      >
        <svg
          className="absolute w-full min-w-[1000px]"
          style={{ bottom: "0", left: "0", right: "0" }}
          viewBox="0 0 1440 320"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill={colors[1] || colors[0]}
            fillOpacity={waveOpacity * 0.8}
            d="M0,96L48,112C96,128,192,160,288,186.7C384,213,480,235,576,224C672,213,768,171,864,149.3C960,128,1056,128,1152,149.3C1248,171,1344,213,1392,234.7L1440,256L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
          ></path>
        </svg>
      </motion.div>

      {/* Third wave */}
      <motion.div
        className="absolute inset-0 z-0"
        initial={animate ? { y: 0 } : undefined}
        animate={animate ? { y: [5, -5, 5] } : undefined}
        transition={animate ? { repeat: Infinity, duration: 10, ease: "easeInOut" } : undefined}
      >
        <svg
          className="absolute w-full min-w-[1000px]"
          style={{ bottom: "0", left: "0", right: "0" }}
          viewBox="0 0 1440 320"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill={colors[0]}
            fillOpacity={waveOpacity * 0.6}
            d="M0,288L48,272C96,256,192,224,288,197.3C384,171,480,149,576,165.3C672,181,768,235,864,250.7C960,267,1056,245,1152,224C1248,203,1344,181,1392,170.7L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
          ></path>
        </svg>
      </motion.div>
    </div>
  );
}
