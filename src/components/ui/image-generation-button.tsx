'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Button, buttonVariants } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { ImageIcon, Loader2, Download, Copy, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';

interface ImageGenerationButtonProps {
  contextType: 'general' | 'tweet' | 'conversation' | 'copy-ai' | 'reply';
  contextContent?: string;
  defaultPrompt?: string;
  tweetId?: string;
  sessionId?: string;
  variant?: 'default' | 'outline' | 'ghost' | 'secondary';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  children?: React.ReactNode;
}

interface GeneratedImage {
  imageId: string;
  imageBase64: string;
  originalPrompt: string;
  revisedPrompt?: string;
  metadata: {
    executionTime: number;
    generatedAt: string;
    model: string;
    enhancedPrompt?: string;
    enhancementInfo?: {
      wasEnhanced: boolean;
      improvements: string[];
      estimatedQuality: number;
      processingTime: number;
    };
  };
}

export function ImageGenerationButton({
  contextType,
  contextContent,
  defaultPrompt = '',
  tweetId,
  sessionId,
  variant = 'outline',
  size = 'sm',
  className,
  children,
}: ImageGenerationButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [prompt, setPrompt] = useState(defaultPrompt);
  const [imageSize, setImageSize] = useState<'1024x1024' | '1024x1536' | '1536x1024' | 'auto'>('auto');
  const [imageQuality, setImageQuality] = useState<'low' | 'medium' | 'high' | 'auto'>('auto');
  const [enhancePrompt, setEnhancePrompt] = useState<boolean>(true); // Default to enabled
  const [generatedImage, setGeneratedImage] = useState<GeneratedImage | null>(null);
  const [copiedText, setCopiedText] = useState<string | null>(null);

  useEffect(() => {
    console.log('🎨 ImageGenerationButton: Component mounted', {
      contextType,
      hasContextContent: !!contextContent,
      defaultPrompt: defaultPrompt.substring(0, 50),
      variant,
      size
    });
  }, [contextType, contextContent, defaultPrompt, variant, size]);

  const handleGenerateImage = async () => {
    console.log('🎨 ImageGenerationButton: Starting image generation', {
      prompt: prompt.substring(0, 100),
      contextType,
      contextContent: contextContent?.substring(0, 100),
      options: { imageSize, imageQuality }
    });

    if (!prompt.trim()) {
      console.error('❌ ImageGenerationButton: Empty prompt provided');
      toast.error('Please enter a prompt for the image');
      return;
    }

    setIsGenerating(true);

    try {
      console.log('🚀 ImageGenerationButton: Making API request to /api/ai/generate-image');

      const requestBody = {
        prompt,
        context: contextContent ? {
          type: contextType,
          content: contextContent,
          userQuery: prompt,
          tweetId,
          sessionId,
        } : undefined,
        options: {
          size: imageSize,
          quality: imageQuality,
          enhancePrompt,
          enhancementOptions: {
            contextType,
            contextContent,
            style: contextType === 'copy-ai' ? 'vibrant' : 'professional',
            targetAudience: contextType === 'copy-ai' ? 'social_media' : 'general',
          }
        },
        saveToDatabase: false, // Images are saved to clipboard/download only
      };

      console.log('📤 ImageGenerationButton: Request body', requestBody);

      const response = await fetch('/api/ai/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log('📥 ImageGenerationButton: API response status', response.status);

      let data;
      try {
        data = await response.json();
        console.log('📊 ImageGenerationButton: API response data', {
          success: data?.success,
          hasImageBase64: !!(data?.imageBase64),
          imageId: data?.imageId,
          error: data?.error || 'No error message'
        });
      } catch (jsonError) {
        console.error('💥 ImageGenerationButton: Failed to parse JSON response:', jsonError);
        throw new Error('Invalid response from server');
      }

      if (response.ok && data?.success) {
        console.log('✅ ImageGenerationButton: Image generated successfully');

        // Create a safe metadata object with proper fallbacks
        const safeMetadata = {
          executionTime: data.metadata?.executionTime || 0,
          generatedAt: data.metadata?.generatedAt || new Date().toISOString(),
          model: data.metadata?.model || 'gpt-image-1',
          enhancedPrompt: data.metadata?.enhancedPrompt || undefined,
          enhancementInfo: data.metadata?.enhancementInfo || undefined,
        };

        console.log('📊 ImageGenerationButton: Setting generated image with safe metadata', {
          hasImageBase64: !!(data.imageBase64),
          metadataKeys: Object.keys(safeMetadata),
          enhancementInfo: safeMetadata.enhancementInfo
        });

        setGeneratedImage({
          imageId: data.imageId || `img_${Date.now()}`,
          imageBase64: data.imageBase64 || '',
          originalPrompt: data.originalPrompt || prompt,
          revisedPrompt: data.revisedPrompt || undefined,
          metadata: safeMetadata,
        });
        toast.success('Image generated successfully!');
      } else {
        console.error('❌ ImageGenerationButton: API returned error', data?.error);
        toast.error(data?.error || 'Failed to generate image');
      }
    } catch (error) {
      console.error('💥 ImageGenerationButton: Exception during image generation:', error);
      toast.error('Failed to generate image');
    } finally {
      setIsGenerating(false);
      console.log('🏁 ImageGenerationButton: Image generation process completed');
    }
  };

  const downloadImage = () => {
    if (!generatedImage) {
      console.warn('⚠️ ImageGenerationButton: No generated image to download');
      return;
    }

    try {
      console.log('💾 ImageGenerationButton: Starting image download', generatedImage.imageId);

      // Only run on client side to prevent hydration issues
      if (typeof document !== 'undefined') {
        const link = document.createElement('a');
        link.href = `data:image/png;base64,${generatedImage.imageBase64}`;
        link.download = `generated-image-${generatedImage.imageId}.png`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        console.log('✅ ImageGenerationButton: Image downloaded successfully');
        toast.success('Image downloaded!');
      }
    } catch (error) {
      console.error('💥 ImageGenerationButton: Error downloading image:', error);
      toast.error('Failed to download image');
    }
  };

  const copyToClipboard = async (text: string, label: string) => {
    try {
      console.log('📋 ImageGenerationButton: Copying text to clipboard', { label, textLength: text.length });
      await navigator.clipboard.writeText(text);
      setCopiedText(label);
      console.log('✅ ImageGenerationButton: Text copied successfully');
      toast.success(`${label} copied to clipboard`);
      setTimeout(() => setCopiedText(null), 2000);
    } catch (error) {
      console.error('💥 ImageGenerationButton: Error copying text to clipboard:', error);
      toast.error('Failed to copy to clipboard');
    }
  };

  const copyImageToClipboard = async () => {
    if (!generatedImage) {
      console.warn('⚠️ ImageGenerationButton: No generated image to copy');
      return;
    }

    try {
      console.log('🖼️ ImageGenerationButton: Starting image copy to clipboard', generatedImage.imageId);

      // Convert base64 to blob
      const response = await fetch(`data:image/png;base64,${generatedImage.imageBase64}`);
      const blob = await response.blob();
      console.log('🔄 ImageGenerationButton: Converted base64 to blob', { blobSize: blob.size });

      // Copy to clipboard
      await navigator.clipboard.write([
        new ClipboardItem({
          'image/png': blob
        })
      ]);

      console.log('✅ ImageGenerationButton: Image copied to clipboard successfully');
      toast.success('Image copied to clipboard!');
    } catch (error) {
      console.error('💥 ImageGenerationButton: Error copying image to clipboard:', error);
      toast.error('Failed to copy image to clipboard. Try downloading instead.');
    }
  };

  const resetDialog = () => {
    setGeneratedImage(null);
    setPrompt(defaultPrompt);
    setCopiedText(null);
  };

  const getContextDescription = () => {
    switch (contextType) {
      case 'tweet':
        return 'Generate an image related to this tweet';
      case 'conversation':
        return 'Generate an image for this conversation';
      case 'copy-ai':
        return 'Generate an image for your viral content';
      case 'reply':
        return 'Generate an image to accompany your reply';
      default:
        return 'Generate an image';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      setIsOpen(open);
      if (!open) resetDialog();
    }}>
      <DialogTrigger className={cn(buttonVariants({ variant, size }), className)}>
        {children || (
          <>
            <ImageIcon className="h-4 w-4 mr-2" />
            Generate Image
          </>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>AI Image Generation</DialogTitle>
          <DialogDescription>
            {getContextDescription()}
            {contextContent && (
              <div className="mt-2 p-2 bg-muted rounded text-xs">
                <strong>Context:</strong> {contextContent.substring(0, 150)}
                {contextContent.length > 150 && '...'}
              </div>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {!generatedImage ? (
            <>
              {/* Image Generation Form */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="prompt">Image Description</Label>
                  <Textarea
                    id="prompt"
                    placeholder="Describe the image you want to generate..."
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    className="mt-1"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="size">Image Size</Label>
                    <Select value={imageSize} onValueChange={(value: typeof imageSize) => setImageSize(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="auto">Auto (Recommended)</SelectItem>
                        <SelectItem value="1024x1024">Square (1024x1024)</SelectItem>
                        <SelectItem value="1024x1536">Portrait (1024x1536)</SelectItem>
                        <SelectItem value="1536x1024">Landscape (1536x1024)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="quality">Image Quality</Label>
                    <Select value={imageQuality} onValueChange={(value: typeof imageQuality) => setImageQuality(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="auto">Auto (Recommended)</SelectItem>
                        <SelectItem value="low">Low (Faster)</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High (Slower)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* AI Enhancement Toggle */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="enhance-prompt"
                    checked={enhancePrompt}
                    onCheckedChange={(checked) => setEnhancePrompt(checked as boolean)}
                  />
                  <Label htmlFor="enhance-prompt" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    🤖 Enhance prompt with Gemini AI (Recommended)
                  </Label>
                </div>
                {enhancePrompt && (
                  <p className="text-xs text-muted-foreground">
                    Gemini will enhance your prompt with detailed visual descriptions for better image quality.
                  </p>
                )}

                <Button
                  onClick={handleGenerateImage}
                  disabled={isGenerating || !prompt.trim()}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                      Generating Image...
                    </>
                  ) : (
                    <>
                      <ImageIcon className="h-4 w-4 mr-2" />
                      Generate Image
                    </>
                  )}
                </Button>
              </div>
            </>
          ) : (
            <>
              {/* Generated Image Display */}
              <Card>
                <CardContent className="p-4">
                  <div className="space-y-4">
                    {/* Image */}
                    <div className="relative">
                      <Image
                        src={`data:image/png;base64,${generatedImage.imageBase64}`}
                        alt={generatedImage.originalPrompt}
                        width={1024}
                        height={1024}
                        className="w-full rounded-lg shadow-lg"
                        unoptimized
                      />
                    </div>

                    {/* Image Info */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Badge variant="secondary">
                          {generatedImage.metadata.model}
                        </Badge>
                        <Badge variant="outline">
                          {generatedImage.metadata.executionTime}ms
                        </Badge>
                      </div>

                      {/* Enhancement Info */}
                      {generatedImage.metadata?.enhancementInfo && (
                        <div className="flex items-center justify-between">
                          <Badge variant={generatedImage.metadata.enhancementInfo.wasEnhanced ? "default" : "secondary"}>
                            {generatedImage.metadata.enhancementInfo.wasEnhanced ? "🤖 Enhanced by Gemini" : "Original Prompt"}
                          </Badge>
                          {generatedImage.metadata.enhancementInfo.wasEnhanced && (
                            <Badge variant="outline">
                              Quality: {generatedImage.metadata.enhancementInfo.estimatedQuality}/10
                            </Badge>
                          )}
                        </div>
                      )}

                      {/* Enhanced Prompt (if available) */}
                      {generatedImage.metadata?.enhancedPrompt && generatedImage.metadata.enhancedPrompt !== generatedImage.originalPrompt && (
                        <div>
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">🤖 Gemini Enhanced Prompt</Label>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(generatedImage.metadata?.enhancedPrompt || '', 'Enhanced Prompt')}
                            >
                              {copiedText === 'Enhanced Prompt' ? (
                                <CheckCircle className="h-4 w-4" />
                              ) : (
                                <Copy className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                          <p className="text-sm text-muted-foreground bg-muted p-2 rounded">
                            {generatedImage.metadata.enhancedPrompt}
                          </p>
                        </div>
                      )}

                      {/* Original Prompt */}
                      <div>
                        <div className="flex items-center justify-between">
                          <Label className="text-sm font-medium">Original Prompt</Label>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(generatedImage.originalPrompt, 'Original Prompt')}
                          >
                            {copiedText === 'Original Prompt' ? (
                              <CheckCircle className="h-4 w-4" />
                            ) : (
                              <Copy className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <p className="text-sm text-muted-foreground bg-muted p-2 rounded">
                          {generatedImage.originalPrompt}
                        </p>
                      </div>

                      {/* Revised Prompt */}
                      {generatedImage.revisedPrompt && generatedImage.revisedPrompt !== generatedImage.originalPrompt && (
                        <div>
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">AI-Refined Prompt</Label>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => copyToClipboard(generatedImage.revisedPrompt!, 'Refined Prompt')}
                            >
                              {copiedText === 'Refined Prompt' ? (
                                <CheckCircle className="h-4 w-4" />
                              ) : (
                                <Copy className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                          <p className="text-sm text-muted-foreground bg-muted p-2 rounded">
                            {generatedImage.revisedPrompt}
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <Button onClick={copyImageToClipboard} variant="outline" className="flex-1">
                        <Copy className="h-4 w-4 mr-2" />
                        Copy Image
                      </Button>
                      <Button onClick={downloadImage} variant="outline" className="flex-1">
                        <Download className="h-4 w-4 mr-2" />
                        Download
                      </Button>
                      <Button onClick={resetDialog} variant="outline" className="flex-1">
                        Generate Another
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
