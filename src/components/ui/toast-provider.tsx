/**
 * Toast Provider Component
 *
 * A component that provides toast notifications for the application.
 * Uses Sonner for toast notifications.
 */

"use client";

import { Toaster as Sonner } from "sonner";
import { useTheme } from "next-themes";



/**
 * Toast provider component that wraps Sonner toast
 * and applies theme-specific styling
 */
export function ToastProvider() {
  const { theme } = useTheme();

  return (
    <Sonner
      theme={(theme as "light" | "dark" | "system") || "dark"}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",
          description: "group-[.toast]:text-muted-foreground",
          actionButton:
            "group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",
          cancelButton:
            "group-[.toast]:bg-muted group-[.toast]:text-muted-foreground",
          error:
            "group-[.toaster]:bg-destructive/15 group-[.toaster]:text-destructive group-[.toaster]:border-destructive/20",
          success:
            "group-[.toaster]:bg-success/15 group-[.toaster]:text-success group-[.toaster]:border-success/20",
          warning:
            "group-[.toaster]:bg-warning/15 group-[.toaster]:text-warning group-[.toaster]:border-warning/20",
          info:
            "group-[.toaster]:bg-info/15 group-[.toaster]:text-info group-[.toaster]:border-info/20",
        },
      }}
    />
  );
}

/**
 * Toast utility functions for displaying different types of notifications
 */
import { toast } from "sonner";
import { handleClientError } from "@/lib/utils/error-handler";

/**
 * Display an error toast with consistent styling and error handling
 *
 * @param error The error to display
 * @param title Optional title for the toast
 */
export function toastError(error: unknown, title = "Error") {
  const { message, code } = handleClientError(error);

  toast.error(title, {
    description: message,
    id: code, // Use error code as ID to prevent duplicate toasts
    duration: 5000,
  });
}

/**
 * Display a success toast
 *
 * @param title The title of the toast
 * @param description Optional description for the toast
 */
export function toastSuccess(title: string, description?: string) {
  toast.success(title, {
    description,
    duration: 3000,
  });
}

/**
 * Display an info toast
 *
 * @param title The title of the toast
 * @param description Optional description for the toast
 */
export function toastInfo(title: string, description?: string) {
  toast.info(title, {
    description,
    duration: 3000,
  });
}

/**
 * Display a warning toast
 *
 * @param title The title of the toast
 * @param description Optional description for the toast
 */
export function toastWarning(title: string, description?: string) {
  toast.warning(title, {
    description,
    duration: 4000,
  });
}

/**
 * Display a loading toast that can be updated with success or error
 *
 * @param loadingMessage The message to display while loading
 * @param promise The promise to track
 * @param options Success and error messages
 */
export function toastPromise<T>(
  loadingMessage: string,
  promise: Promise<T>,
  options: {
    success: string | ((data: T) => string);
    error: string | ((error: unknown) => string);
  }
) {
  return toast.promise(promise, {
    loading: loadingMessage,
    success: options.success,
    error: options.error,
  });
}
