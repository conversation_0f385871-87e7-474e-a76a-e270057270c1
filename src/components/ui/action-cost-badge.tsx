'use client';

import { Badge } from '@/components/ui/badge';
import { Coins, AlertCircle } from 'lucide-react';
import { TokenAction } from '@/lib/web3/token-usage-types';
import { useActionCostDisplay, useActionAffordability } from '@/hooks/useTokenUsage';
import { cn } from '@/lib/utils';

interface ActionCostBadgeProps {
  actionType: TokenAction;
  userBalance?: number;
  variant?: 'default' | 'outline' | 'secondary' | 'destructive';
  size?: 'sm' | 'default' | 'lg';
  showIcon?: boolean;
  className?: string;
}

/**
 * Badge component to display action costs and affordability
 */
export function ActionCostBadge({
  actionType,
  userBalance = 0,
  variant = 'outline',
  size = 'default',
  showIcon = true,
  className,
}: ActionCostBadgeProps) {
  try {
    const { getDisplayInfo } = useActionCostDisplay();
    const { canAfford, shortfall } = useActionAffordability(actionType, userBalance);

    const displayInfo = getDisplayInfo(actionType);

  if (displayInfo.loading) {
    return (
      <Badge variant="outline" className={cn('animate-pulse', className)}>
        Loading...
      </Badge>
    );
  }

  if (displayInfo.isFree) {
    return (
      <Badge variant="secondary" className={cn('text-green-600', className)}>
        {showIcon && <Coins className="w-3 h-3 mr-1" />}
        Free
      </Badge>
    );
  }

  const badgeVariant = canAfford ? variant : 'destructive';
  const icon = canAfford ? (
    showIcon && <Coins className="w-3 h-3 mr-1" />
  ) : (
    showIcon && <AlertCircle className="w-3 h-3 mr-1" />
  );

  return (
    <Badge
      variant={badgeVariant}
      className={cn(
        size === 'sm' && 'text-xs px-2 py-0.5',
        size === 'lg' && 'text-sm px-3 py-1',
        !canAfford && 'border-red-500 text-red-600',
        className
      )}
      title={!canAfford ? `Need ${shortfall.toFixed(3)} more $COPY` : undefined}
    >
      {icon}
      {displayInfo.displayText}
    </Badge>
  );
  } catch (error) {
    console.error('Error in ActionCostBadge:', error);
    return (
      <Badge variant="outline" className={cn('text-muted-foreground', className)}>
        {showIcon && <Coins className="w-3 h-3 mr-1" />}
        Cost: --
      </Badge>
    );
  }
}

interface ActionCostDisplayProps {
  actionType: TokenAction;
  userBalance?: number;
  showAffordability?: boolean;
  className?: string;
}

/**
 * More detailed component for displaying action costs with affordability info
 */
export function ActionCostDisplay({
  actionType,
  userBalance = 0,
  showAffordability = true,
  className,
}: ActionCostDisplayProps) {
  const { getDisplayInfo } = useActionCostDisplay();
  const { canAfford, shortfall, formattedCost } = useActionAffordability(actionType, userBalance);

  const displayInfo = getDisplayInfo(actionType);

  if (displayInfo.loading) {
    return (
      <div className={cn('flex items-center gap-2 text-sm text-muted-foreground', className)}>
        <Coins className="w-4 h-4 animate-pulse" />
        <span>Loading cost...</span>
      </div>
    );
  }

  if (displayInfo.isFree) {
    return (
      <div className={cn('flex items-center gap-2 text-sm text-green-600', className)}>
        <Coins className="w-4 h-4" />
        <span>Free action</span>
      </div>
    );
  }

  return (
    <div className={cn('flex items-center gap-2 text-sm', className)}>
      <Coins className={cn('w-4 h-4', canAfford ? 'text-blue-500' : 'text-red-500')} />
      <span className={canAfford ? 'text-foreground' : 'text-red-600'}>
        Cost: {formattedCost}
      </span>
      {showAffordability && !canAfford && (
        <span className="text-xs text-red-500">
          (Need {shortfall.toFixed(3)} more)
        </span>
      )}
    </div>
  );
}

interface ActionButtonProps {
  actionType: TokenAction;
  userBalance?: number;
  onClick: () => void;
  disabled?: boolean;
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
}

/**
 * Button component that shows cost and handles affordability
 */
export function ActionButton({
  actionType,
  userBalance = 0,
  onClick,
  disabled = false,
  children,
  className,
  variant = 'default',
}: ActionButtonProps) {
  const { canAfford } = useActionAffordability(actionType, userBalance);
  const { getDisplayInfo } = useActionCostDisplay();

  const displayInfo = getDisplayInfo(actionType);
  const isDisabled = disabled || !canAfford || displayInfo.loading;

  const handleClick = () => {
    if (!isDisabled) {
      onClick();
    }
  };

  return (
    <div className="space-y-2">
      <button
        onClick={handleClick}
        disabled={isDisabled}
        className={cn(
          'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors',
          'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
          'disabled:opacity-50 disabled:pointer-events-none',
          'h-10 px-4 py-2',
          variant === 'default' && 'bg-primary text-primary-foreground hover:bg-primary/90',
          variant === 'outline' && 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
          variant === 'secondary' && 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
          variant === 'ghost' && 'hover:bg-accent hover:text-accent-foreground',
          variant === 'link' && 'text-primary underline-offset-4 hover:underline',
          className
        )}
        title={!canAfford ? `Insufficient balance. Need ${displayInfo.formattedCost}` : undefined}
      >
        {children}
      </button>

      <ActionCostBadge
        actionType={actionType}
        userBalance={userBalance}
        size="sm"
        className="ml-0"
      />
    </div>
  );
}
