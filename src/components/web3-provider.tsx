'use client';

import { ReactNode, Suspense } from 'react';
import dynamic from 'next/dynamic';

// Dynamic imports for heavy Solana wallet components
const SolanaWalletProvider = dynamic(() => import('./solana-wallet-provider'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center p-4">
      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
      <span className="ml-2 text-sm text-muted-foreground">Loading wallet...</span>
    </div>
  ),
});

interface Web3ProviderProps {
  children: ReactNode;
}

// Main Web3Provider component with optimized loading
export function Web3Provider({ children }: Web3ProviderProps) {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        <span className="ml-2 text-sm text-muted-foreground">Initializing Web3...</span>
      </div>
    }>
      <SolanaWalletProvider>
        {children}
      </SolanaWalletProvider>
    </Suspense>
  );
}


