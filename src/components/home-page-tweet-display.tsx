'use client';

import { useState, useEffect, useCallback } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, RefreshCw } from 'lucide-react';
import TweetCard from '@/components/tweet-card';
import { Button } from './ui/button';

interface Tweet {
  id: number;
  tweet_id: string;
  author_handle: string;
  content: string;
  fetched_at: string;
  tweet_created_at: string;
  sentiment_score: number | null;
  importance_score: number | null;
  is_marked_irrelevant: boolean;
  raw_data: Record<string, unknown>;
  ai_analysis?: {
    relevance_score?: number;
    worth_replying?: boolean;
    evaluation_reason?: string;
    reply_suggestions?: string[];
    analyzed_at?: string;
    last_generated_at?: string;
  };
}

export default function HomePageTweetDisplay() {
  const [tweets, setTweets] = useState<Tweet[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const fetchAndProcessTweets = useCallback(async () => {
    try {
      // For the first load, set isLoading to true. For refresh, use isRefreshing.
      if (!isRefreshing) setIsLoading(true);
      setError(null);

      const response = await fetch('/api/twitter/tweets');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch tweets');
      }

      const data = await response.json();
      const allTweets: Tweet[] = data.tweets || [];

      const processedTweets = allTweets
        .filter(tweet => !tweet.is_marked_irrelevant)
        .sort((a, b) => {
          const aHasAnalysis = !!a.ai_analysis?.analyzed_at;
          const bHasAnalysis = !!b.ai_analysis?.analyzed_at;

          if (aHasAnalysis && !bHasAnalysis) return -1; // a comes first
          if (!aHasAnalysis && bHasAnalysis) return 1;  // b comes first

          if (aHasAnalysis && bHasAnalysis) {
            // Both have analysis, sort by relevance score (desc) then analyzed_at (desc)
            const relevanceDiff = (b.ai_analysis?.relevance_score || 0) - (a.ai_analysis?.relevance_score || 0);
            if (relevanceDiff !== 0) return relevanceDiff;
            // Ensure analyzed_at is valid before creating Date objects
            const dateA = a.ai_analysis!.analyzed_at ? new Date(a.ai_analysis!.analyzed_at).getTime() : 0;
            const dateB = b.ai_analysis!.analyzed_at ? new Date(b.ai_analysis!.analyzed_at).getTime() : 0;
            return dateB - dateA;
          }

          // Neither has analysis (or as fallback), sort by tweet_created_at (desc)
          return new Date(b.tweet_created_at).getTime() - new Date(a.tweet_created_at).getTime();
        })
        .slice(0, 5);

      setTweets(processedTweets);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      setTweets([]);
    } finally {
      setIsLoading(false);
      if (isRefreshing) setIsRefreshing(false);
    }
  }, [isRefreshing]);

  useEffect(() => {
    fetchAndProcessTweets();

    // Only add event listener on client side to prevent hydration issues
    if (typeof window !== 'undefined') {
      const handleTweetsAnalyzed = () => {
        fetchAndProcessTweets(); // Re-fetch and process when tweets are analyzed
      };
      window.addEventListener('tweetsAnalyzed', handleTweetsAnalyzed);
      return () => {
        window.removeEventListener('tweetsAnalyzed', handleTweetsAnalyzed);
      };
    }
  }, [fetchAndProcessTweets]); // Include fetchAndProcessTweets dependency

  const refreshTweets = async () => {
    setIsRefreshing(true);
    await fetchAndProcessTweets();
    // setIsRefreshing(false) is handled in the finally block of fetchAndProcessTweets
  };

  const handleMarkAsIrrelevant = async (tweetId: number) => {
    // For the home page, we'll just refresh the list.
    // A more complete implementation would call an API to mark as irrelevant.
    setTweets(prevTweets => prevTweets.filter(tweet => tweet.id !== tweetId));
    // Optionally, trigger a re-fetch if the source of truth needs to be updated server-side
    // For now, this provides immediate UI feedback and then a full refresh will correct.
    await fetchAndProcessTweets();
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6"> {/* Added space-y-6 for overall section spacing */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl md:text-3xl font-bold text-primary">Your Latest Tweets <span className="text-sm font-normal text-muted-foreground">(10 most recent per account)</span></h2>
        <Button onClick={refreshTweets} disabled={isRefreshing} variant="outline" size="sm">
          {isRefreshing ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>

      {tweets.length === 0 && !isRefreshing ? (
         <div className="text-center py-8 bg-card rounded-lg shadow p-6">
            <p className="text-muted-foreground mb-4">No tweets to display currently. Try ingesting tweets from your dashboard! Only the 10 most recent tweets per account will be retrieved.</p>
         </div>
      ) : (
        <div className="space-y-4">
          {tweets.map(tweet => (
            <TweetCard
              key={tweet.id}
              tweet={tweet}
              onMarkIrrelevant={() => handleMarkAsIrrelevant(tweet.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
}