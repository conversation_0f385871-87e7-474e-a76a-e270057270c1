'use client';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState } from 'react';
import { PERFORMANCE_CONFIG } from '@/lib/config/client';

interface QueryProviderProps {
  children: React.ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Use configuration from client config
            staleTime: PERFORMANCE_CONFIG.CACHE_STALE_TIME,
            gcTime: PERFORMANCE_CONFIG.CACHE_GC_TIME,
            // Intelligent retry logic
            retry: (failureCount, error: any) => {
              // Don't retry on 4xx errors (client errors)
              if (error?.status >= 400 && error?.status < 500) {
                return false;
              }
              // Retry up to 3 times for other errors
              return failureCount < 3;
            },
            // Exponential backoff with jitter
            retryDelay: (attemptIndex) => {
              const baseDelay = Math.min(1000 * 2 ** attemptIndex, 30000);
              const jitter = Math.random() * 0.1 * baseDelay;
              return baseDelay + jitter;
            },
            // Performance optimizations
            refetchOnWindowFocus: false,
            refetchOnReconnect: true,
            refetchOnMount: false,
            // Network mode for better offline handling
            networkMode: 'online',
          },
          mutations: {
            // Retry failed mutations once with delay
            retry: 1,
            retryDelay: 1000,
            // Network mode for mutations
            networkMode: 'online',
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  );
}
