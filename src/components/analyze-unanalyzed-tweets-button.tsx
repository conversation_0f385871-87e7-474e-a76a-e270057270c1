'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Brain } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { ButtonLoading } from '@/components/ui/loading';
import { useAsyncOperations } from '@/hooks/use-loading';
import { toastSuccess, toastError, toastInfo } from '@/components/ui/toast-provider';
import { logger } from '@/lib/utils/logger';
import { useRouter } from 'next/navigation';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';

export default function AnalyzeUnanalyzedTweetsButton() {
  const router = useRouter();
  const [analyzeAll, setAnalyzeAll] = useState(false);
  const [progress, setProgress] = useState<{
    analyzed: number;
    remaining: number;
    total: number;
  } | null>(null);

  const { executeOperation, isLoadingKey, errors, clearError } = useAsyncOperations();

  const analyzeBatch = async (unanalyzedTweets: Array<{ id: string }>) => {
    // Limit to 10 tweets to avoid rate limits
    const tweetsToAnalyze = unanalyzedTweets.slice(0, 10);
    const tweetIds = tweetsToAnalyze.map((tweet) => tweet.id);

    // Call the batch analyze API
    const analyzeResponse = await fetch('/api/ai/analyze-tweet', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ tweet_ids: tweetIds }),
    });

    if (!analyzeResponse.ok) {
      const errorData = await analyzeResponse.json();
      throw new Error(errorData.error || 'Failed to analyze tweets');
    }

    return await analyzeResponse.json();
  };

  const handleAnalyzeUnanalyzedTweets = async () => {
    const analyzeOperation = async () => {
      logger.info('Starting tweet analysis operation', { analyzeAll });
      setProgress(null);

      // First, fetch all tweets to find unanalyzed ones
      const tweetsResponse = await fetch('/api/twitter/tweets');

      if (!tweetsResponse.ok) {
        const errorData = await tweetsResponse.json();
        throw new Error(errorData.error || 'Failed to fetch tweets');
      }

      const tweetsData = await tweetsResponse.json();

      // Filter for unanalyzed tweets
      const unanalyzedTweets = tweetsData.tweets.filter((tweet: {
        id: string;
        is_marked_irrelevant: boolean;
        ai_analysis?: { analyzed_at?: string }
      }) =>
        !tweet.is_marked_irrelevant && !tweet.ai_analysis?.analyzed_at
      );

      if (unanalyzedTweets.length === 0) {
        toastInfo('No unanalyzed tweets found', 'All tweets have been analyzed');
        return { processed: 0, total: 0 };
      }

      // Initialize progress
      setProgress({
        analyzed: 0,
        remaining: unanalyzedTweets.length,
        total: unanalyzedTweets.length
      });

      let totalAnalyzed = 0;

      if (analyzeAll) {
        // Process all tweets in batches of 10
        let remainingTweets = [...unanalyzedTweets];

        while (remainingTweets.length > 0) {
          try {
            // Analyze a batch of tweets
            const analyzeData = await analyzeBatch(remainingTweets);
            totalAnalyzed += analyzeData.processed;

            // Remove the analyzed tweets from the remaining tweets
            remainingTweets = remainingTweets.slice(10);

            // Update progress
            setProgress({
              analyzed: totalAnalyzed,
              remaining: remainingTweets.length,
              total: unanalyzedTweets.length
            });

            logger.info('Batch analyzed', {
              processed: analyzeData.processed,
              totalAnalyzed,
              remaining: remainingTweets.length
            });

            // If we've analyzed all tweets or the user cancelled, stop
            if (remainingTweets.length === 0 || !analyzeAll) {
              break;
            }

            // Add a small delay to avoid rate limits
            await new Promise(resolve => setTimeout(resolve, 1000));
          } catch (batchError: unknown) {
            logger.error('Error analyzing batch:', batchError);
            const errorMessage = batchError instanceof Error ? batchError.message : 'Unknown error';
            throw new Error(`Error analyzing batch: ${errorMessage}. ${totalAnalyzed} tweets were successfully analyzed.`);
          }
        }
      } else {
        // Just analyze one batch of 10 tweets
        const analyzeData = await analyzeBatch(unanalyzedTweets);
        totalAnalyzed = analyzeData.processed;
      }

      // Dispatch an event to notify other components
      window.dispatchEvent(new CustomEvent('tweetsAnalyzed'));
      // Refresh the page to show the updated tweets
      router.refresh();

      logger.info('Tweet analysis completed', { totalAnalyzed, total: unanalyzedTweets.length });
      return { processed: totalAnalyzed, total: unanalyzedTweets.length };
    };

    await executeOperation(
      analyzeOperation,
      'analyze-tweets',
      (result) => {
        if (result.processed > 0) {
          if (analyzeAll) {
            toastSuccess(`Analysis Complete`, `Successfully analyzed ${result.processed}/${result.total} tweets`);
          } else {
            toastSuccess(`Batch Analyzed`, `Successfully analyzed ${result.processed} tweets. ${result.total - result.processed} tweets remain unanalyzed.`);
          }
        }
        setProgress(null);
      },
      (error) => {
        logger.error('Error analyzing tweets:', error);
        toastError(error, 'Analysis Failed');
        setProgress(null);
      }
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2 mb-4">
        <Checkbox
          id="analyzeAll"
          checked={analyzeAll}
          onCheckedChange={(checked) => setAnalyzeAll(checked as boolean)}
          disabled={isLoadingKey('analyze-tweets')}
        />
        <Label
          htmlFor="analyzeAll"
          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
        >
          Continue analyzing until all tweets are processed
        </Label>
      </div>

      <Button
        onClick={handleAnalyzeUnanalyzedTweets}
        disabled={isLoadingKey('analyze-tweets')}
        className="w-full"
        variant="outline"
      >
        {isLoadingKey('analyze-tweets') ? (
          <>
            <ButtonLoading className="mr-2" />
            {progress
              ? `Analyzing Tweets (${progress.analyzed}/${progress.total})...`
              : 'Analyzing Tweets...'}
          </>
        ) : (
          <>
            <Brain className="mr-2 h-4 w-4" /> Analyze Unanalyzed Tweets
          </>
        )}
      </Button>

      {progress && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Progress: {progress.analyzed}/{progress.total}</span>
            <span>{Math.round((progress.analyzed / progress.total) * 100)}%</span>
          </div>
          <Progress value={(progress.analyzed / progress.total) * 100} className="w-full" />
        </div>
      )}

      {errors['analyze-tweets'] && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {errors['analyze-tweets']}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={() => clearError('analyze-tweets')}
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
}
