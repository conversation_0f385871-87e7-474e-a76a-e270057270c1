'use client';

import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton, WalletDisconnectButton } from '@solana/wallet-adapter-react-ui';
import { Button } from '@/components/ui/button';
import { Wallet, ChevronDown, Copy } from 'lucide-react';
import { useState } from 'react';
import { CURRENT_NETWORK, SOLANA_NETWORKS } from '@/lib/web3/config';

interface WalletConnectButtonProps {
  variant?: 'desktop' | 'mobile';
}

export function WalletConnectButton({ variant = 'desktop' }: WalletConnectButtonProps) {
  const { wallet, publicKey, connected, connecting, disconnecting, connect, disconnect } = useWallet();
  const [showDetails, setShowDetails] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);

  console.log('💳 Wallet state:', {
    connected,
    connecting,
    disconnecting,
    publicKey: publicKey?.toString(),
    walletName: wallet?.adapter.name,
    readyState: wallet?.readyState,
  });

  // Handle wallet connection with error handling
  const _handleConnect = async () => {
    if (!wallet) {
      console.warn('⚠️ No wallet selected');
      return;
    }

    try {
      setIsConnecting(true);
      console.log('🔌 Attempting to connect wallet:', wallet.adapter.name);

      // Check if wallet is ready
      if (wallet.readyState !== 'Installed') {
        console.warn('⚠️ Wallet not installed or ready:', {
          name: wallet.adapter.name,
          readyState: wallet.readyState,
        });

        // Open wallet installation page if available
        if (wallet.adapter.url) {
          window.open(wallet.adapter.url, '_blank');
        }
        return;
      }

      await connect();
      console.log('✅ Wallet connected successfully');
    } catch (error) {
      console.error('❌ Failed to connect wallet:', error);
    } finally {
      setIsConnecting(false);
    }
  };

  // Handle wallet disconnection
  const handleDisconnect = async () => {
    try {
      console.log('🔌 Disconnecting wallet');
      await disconnect();
      console.log('✅ Wallet disconnected successfully');
    } catch (error) {
      console.error('❌ Failed to disconnect wallet:', error);
    }
  };

  // Format public key for display
  const formatPublicKey = (key: string) => {
    return `${key.slice(0, 4)}...${key.slice(-4)}`;
  };

  // Copy address to clipboard
  const copyAddress = async () => {
    if (publicKey) {
      await navigator.clipboard.writeText(publicKey.toString());
      console.log('📋 Address copied to clipboard');
    }
  };

  // Get current network info
  const currentNetwork = SOLANA_NETWORKS[CURRENT_NETWORK as keyof typeof SOLANA_NETWORKS];

  if (!connected) {
    return (
      <div className={variant === 'mobile' ? 'w-full' : ''}>
        <WalletMultiButton
          className={`!bg-transparent !border !border-border !text-foreground hover:!bg-muted !rounded-md !h-9 !px-3 !text-sm !font-medium !transition-colors ${
            variant === 'mobile' ? '!w-full !justify-start' : ''
          }`}
        >
          <Wallet className="h-4 w-4 mr-2" />
          {connecting || isConnecting ? 'Connecting...' : 'Connect Wallet'}
        </WalletMultiButton>
      </div>
    );
  }

  if (variant === 'mobile') {
    return (
      <div className="flex flex-col gap-2 w-full">
        <Button
          onClick={() => setShowDetails(!showDetails)}
          variant="outline"
          className="w-full justify-between text-foreground hover:bg-muted"
        >
          <div className="flex items-center gap-2">
            <Wallet className="h-4 w-4" />
            <span>{wallet?.adapter.name}</span>
          </div>
          <ChevronDown className={`h-3 w-3 transition-transform ${showDetails ? 'rotate-180' : ''}`} />
        </Button>

        {showDetails && (
          <div className="flex flex-col gap-2 pl-4">
            <Button
              onClick={copyAddress}
              variant="ghost"
              size="sm"
              className="justify-start text-muted-foreground"
            >
              <Copy className="h-3 w-3 mr-2" />
              {publicKey ? formatPublicKey(publicKey.toString()) : 'No address'}
            </Button>
            <div className="text-xs text-muted-foreground px-2">
              Network: {currentNetwork.name}
            </div>
            <WalletDisconnectButton className="!bg-transparent !border !border-border !text-foreground hover:!bg-muted !rounded-md !h-8 !px-2 !text-xs" />
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="flex gap-2">
      <Button
        variant="outline"
        size="sm"
        className="flex items-center gap-2"
        onClick={() => setShowDetails(!showDetails)}
      >
        <div className="w-3 h-3 bg-green-500 rounded-full" />
        {currentNetwork.name}
        <ChevronDown className="h-3 w-3" />
      </Button>

      <Button
        onClick={copyAddress}
        variant="outline"
        size="sm"
        className="flex items-center gap-2"
      >
        <Wallet className="h-4 w-4" />
        {publicKey ? formatPublicKey(publicKey.toString()) : 'No address'}
        <Copy className="h-3 w-3" />
      </Button>

      <Button
        onClick={handleDisconnect}
        variant="outline"
        size="sm"
        className="text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950"
        disabled={disconnecting}
      >
        {disconnecting ? 'Disconnecting...' : 'Disconnect'}
      </Button>
    </div>
  );
}
