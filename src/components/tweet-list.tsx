'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON>ertCircle, RefreshCw, Database } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import TweetCard from '@/components/tweet-card';

interface Tweet {
  id: number;
  tweet_id: string;
  author_handle: string;
  content: string;
  fetched_at: string;
  tweet_created_at: string;
  sentiment_score: number | null;
  importance_score: number | null;
  is_marked_irrelevant: boolean;
  raw_data: Record<string, unknown>;
  ai_analysis?: {
    relevance_score?: number;
    worth_replying?: boolean;
    evaluation_reason?: string;
    reply_suggestions?: string[];
    analyzed_at?: string;
    last_generated_at?: string;
  };
}

export default function TweetList() {
  const [tweets, setTweets] = useState<Tweet[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    fetchTweets(); // Initial fetch

    // Only add event listener on client side to prevent hydration issues
    if (typeof window !== 'undefined') {
      const handleTweetsAnalyzed = () => {
        // console.log('tweetsAnalyzed event received, refreshing TweetList...');
        fetchTweets();
      };

      window.addEventListener('tweetsAnalyzed', handleTweetsAnalyzed);

      // Cleanup
      return () => {
        window.removeEventListener('tweetsAnalyzed', handleTweetsAnalyzed);
      };
    }
  }, []); // Empty dependency array: runs once on mount, cleans up on unmount

  const fetchTweets = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Attempt to fetch tweets from the API
      const response = await fetch('/api/twitter/tweets');

      if (!response.ok) {
        // If we get a 404 or 500, it might be because the tables don't exist yet
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch tweets');
      }

      const data = await response.json();
      setTweets(data.tweets || []);
    } catch (err: unknown) {
      // Check if the error is related to missing database tables
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      if (errorMessage && (
        errorMessage.includes('does not exist') ||
        errorMessage.includes('relation') ||
        errorMessage.includes('Failed to fetch tweets')
      )) {
        setError('Database tables not set up yet. Please set up your Supabase database first.');
      } else {
        setError(errorMessage);
      }
      setTweets([]);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshTweets = async () => {
    try {
      setIsRefreshing(true);
      await fetchTweets();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh tweets';
      setError(errorMessage);
    } finally {
      setIsRefreshing(false);
    }
  };

  const markAsIrrelevant = async (tweetId: number) => {
    try {
      // Call the API to mark the tweet as irrelevant
      const response = await fetch('/api/twitter/tweets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tweet_id: tweetId,
          is_irrelevant: true
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to mark tweet as irrelevant');
      }

      // Update the local state
      setTweets(tweets.map(tweet =>
        tweet.id === tweetId
          ? { ...tweet, is_marked_irrelevant: true }
          : tweet
      ));
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error && error.includes('Database tables not set up')) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Setup Required
          </CardTitle>
          <CardDescription>
            Your Supabase database tables need to be created before you can use this feature.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>

          <div className="rounded-md bg-muted p-4">
            <h3 className="font-medium mb-2">Next Steps:</h3>
            <ol className="list-decimal pl-5 space-y-2">
              <li>Create a Supabase project if you haven&apos;t already</li>
              <li>Run the database setup SQL script from the docs folder</li>
              <li>Configure your environment variables in .env</li>
              <li>Restart the application</li>
            </ol>
          </div>

          <Button onClick={refreshTweets} disabled={isRefreshing}>
            {isRefreshing ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Checking...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Check Again
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (tweets.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground mb-4">No tweets found. Add Twitter accounts to follow and ingest tweets.</p>
        <Button onClick={refreshTweets} disabled={isRefreshing}>
          {isRefreshing ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Tweets
            </>
          )}
        </Button>
      </div>
    );
  }

  // The rest of the component for displaying tweets would go here
  // We've removed it since we don't have any tweets to display yet
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Your Tweets</h2>
        <Button
          variant="outline"
          size="sm"
          onClick={refreshTweets}
          disabled={isRefreshing}
        >
          {isRefreshing ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Refreshing...
            </>
          ) : (
            <>
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh
            </>
          )}
        </Button>
      </div>

      <Tabs defaultValue="all">
        <TabsList className="grid grid-cols-5">
          <TabsTrigger value="all">
            All ({tweets.filter(tweet => !tweet.is_marked_irrelevant).length})
          </TabsTrigger>
          <TabsTrigger value="important">
            Important ({tweets.filter(tweet => !tweet.is_marked_irrelevant && (tweet.importance_score || 0) > 7).length})
          </TabsTrigger>
          <TabsTrigger value="positive">
            Positive ({tweets.filter(tweet => !tweet.is_marked_irrelevant && (tweet.sentiment_score || 0) > 0.3).length})
          </TabsTrigger>
          <TabsTrigger value="negative">
            Negative ({tweets.filter(tweet => !tweet.is_marked_irrelevant && (tweet.sentiment_score || 0) < -0.3).length})
          </TabsTrigger>
          <TabsTrigger value="irrelevant">
            Irrelevant ({tweets.filter(tweet => tweet.is_marked_irrelevant).length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4 mt-4">
          {tweets.length > 0 ? (
            tweets.filter(tweet => !tweet.is_marked_irrelevant).map(tweet => (
              <TweetCard
                key={tweet.id}
                tweet={tweet}
                onMarkIrrelevant={() => markAsIrrelevant(tweet.id)}
              />
            ))
          ) : (
            <p className="text-center py-4 text-muted-foreground">
              Tweets will appear here once you&apos;ve added Twitter accounts to follow and ingested tweets.
            </p>
          )}
        </TabsContent>

        <TabsContent value="important" className="space-y-4 mt-4">
          {tweets.filter(tweet => !tweet.is_marked_irrelevant && (tweet.importance_score || 0) > 7).length > 0 ? (
            tweets
              .filter(tweet => !tweet.is_marked_irrelevant && (tweet.importance_score || 0) > 7)
              .sort((a, b) => (b.importance_score || 0) - (a.importance_score || 0))
              .map(tweet => (
                <TweetCard
                  key={tweet.id}
                  tweet={tweet}
                  onMarkIrrelevant={() => markAsIrrelevant(tweet.id)}
                />
              ))
          ) : (
            <p className="text-center py-4 text-muted-foreground">No important tweets found.</p>
          )}
        </TabsContent>

        <TabsContent value="positive" className="space-y-4 mt-4">
          {tweets.filter(tweet => !tweet.is_marked_irrelevant && (tweet.sentiment_score || 0) > 0.3).length > 0 ? (
            tweets
              .filter(tweet => !tweet.is_marked_irrelevant && (tweet.sentiment_score || 0) > 0.3)
              .sort((a, b) => (b.sentiment_score || 0) - (a.sentiment_score || 0))
              .map(tweet => (
                <TweetCard
                  key={tweet.id}
                  tweet={tweet}
                  onMarkIrrelevant={() => markAsIrrelevant(tweet.id)}
                />
              ))
          ) : (
            <p className="text-center py-4 text-muted-foreground">No positive tweets found.</p>
          )}
        </TabsContent>

        <TabsContent value="negative" className="space-y-4 mt-4">
          {tweets.filter(tweet => !tweet.is_marked_irrelevant && (tweet.sentiment_score || 0) < -0.3).length > 0 ? (
            tweets
              .filter(tweet => !tweet.is_marked_irrelevant && (tweet.sentiment_score || 0) < -0.3)
              .sort((a, b) => (a.sentiment_score || 0) - (b.sentiment_score || 0))
              .map(tweet => (
                <TweetCard
                  key={tweet.id}
                  tweet={tweet}
                  onMarkIrrelevant={() => markAsIrrelevant(tweet.id)}
                />
              ))
          ) : (
            <p className="text-center py-4 text-muted-foreground">No negative tweets found.</p>
          )}
        </TabsContent>

        <TabsContent value="irrelevant" className="space-y-4 mt-4">
          {tweets.filter(tweet => tweet.is_marked_irrelevant).length > 0 ? (
            tweets
              .filter(tweet => tweet.is_marked_irrelevant)
              .map(tweet => (
                <TweetCard
                  key={tweet.id}
                  tweet={tweet}
                  isIrrelevant={true}
                  onMarkIrrelevant={() => {}} // No action needed for irrelevant tweets
                />
              ))
          ) : (
            <p className="text-center py-4 text-muted-foreground">No irrelevant tweets found.</p>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
