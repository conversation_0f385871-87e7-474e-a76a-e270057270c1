'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Save } from 'lucide-react';

import { Loading, ButtonLoading } from '@/components/ui/loading';
import { useAsyncOperations } from '@/hooks/use-loading';
import { toastSuccess, toastError } from '@/components/ui/toast-provider';
import { logger } from '@/lib/utils/logger';

interface UserPreferencesFormProps {
  onSaved?: () => void;
}

export default function UserPreferencesForm({ onSaved }: UserPreferencesFormProps) {
  const [replyStyle, setReplyStyle] = useState('Professional and friendly');
  const [interests, setInterests] = useState('General business and technology');
  const [tone, setTone] = useState('Helpful and informative');
  const [systemPrompt, setSystemPrompt] = useState('');

  const { executeOperation, isLoadingKey, errors, clearError } = useAsyncOperations();

  useEffect(() => {
    const fetchPreferences = async () => {
      logger.info('Fetching user preferences');

      const response = await fetch('/api/user/preferences');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch preferences');
      }

      const data = await response.json();

      if (data) {
        setReplyStyle(data.reply_style || 'Professional and friendly');
        setInterests(data.interests || 'General business and technology');
        setTone(data.tone || 'Helpful and informative');
        setSystemPrompt(data.system_prompt || '');
      }

      logger.info('User preferences fetched successfully');
    };

    executeOperation(
      fetchPreferences,
      'fetch-preferences',
      undefined,
      (error) => {
        logger.error('Error fetching preferences:', error);
        toastError(error, 'Failed to load preferences');
      }
    );
  }, [executeOperation]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const savePreferences = async () => {
      logger.info('Saving user preferences');

      const response = await fetch('/api/user/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          reply_style: replyStyle,
          interests,
          tone,
          system_prompt: systemPrompt,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save preferences');
      }

      logger.info('User preferences saved successfully');
      return response.json();
    };

    await executeOperation(
      savePreferences,
      'save-preferences',
      () => {
        toastSuccess('Preferences saved successfully');
        if (onSaved) {
          onSaved();
        }
      },
      (error) => {
        logger.error('Error saving preferences:', error);
        toastError(error, 'Failed to save preferences');
      }
    );
  };

  if (isLoadingKey('fetch-preferences')) {
    return (
      <Card>
        <CardContent className="flex justify-center py-8">
          <Loading variant="card" text="Loading preferences..." />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {errors['fetch-preferences'] && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {errors['fetch-preferences']}
                <Button
                  variant="outline"
                  size="sm"
                  className="ml-2"
                  onClick={() => {
                    clearError('fetch-preferences');
                    window.location.reload();
                  }}
                >
                  Retry
                </Button>
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-2">
            <Label htmlFor="replyStyle">Reply Style</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
              {['Professional and friendly', 'Casual and conversational', 'Formal and authoritative', 'Humorous and light', 'Concise and direct'].map((style) => (
                <div
                  key={style}
                  className={`
                    border rounded-md p-3 cursor-pointer transition-colors
                    ${replyStyle === style ? 'border-primary bg-primary/10' : 'border-input hover:border-primary/50'}
                  `}
                  onClick={() => setReplyStyle(style)}
                >
                  <div className="font-medium">{style}</div>
                </div>
              ))}
              <div className="md:col-span-3 mt-2">
                <Input
                  id="replyStyle"
                  value={replyStyle}
                  onChange={(e) => setReplyStyle(e.target.value)}
                  placeholder="Or enter a custom style..."
                  required
                />
              </div>
            </div>
            <p className="text-sm text-muted-foreground">
              How would you like your replies to sound? Select a style or enter your own.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="interests">Interests & Topics</Label>
            <Textarea
              id="interests"
              value={interests}
              onChange={(e) => setInterests(e.target.value)}
              placeholder="e.g., Technology, Marketing, Finance, AI, Software Development"
              rows={3}
              required
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              What topics are you interested in or knowledgeable about? List multiple topics separated by commas.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="tone">Preferred Tone</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {['Helpful', 'Authoritative', 'Friendly', 'Empathetic', 'Enthusiastic', 'Neutral', 'Confident', 'Thoughtful'].map((toneOption) => (
                <div
                  key={toneOption}
                  className={`
                    border rounded-md p-2 cursor-pointer text-center transition-colors
                    ${tone.includes(toneOption) ? 'border-primary bg-primary/10' : 'border-input hover:border-primary/50'}
                  `}
                  onClick={() => {
                    if (tone.includes(toneOption)) {
                      // Remove the tone if it's already selected
                      setTone(tone.split(',').map(t => t.trim()).filter(t => t !== toneOption).join(', '));
                    } else {
                      // Add the tone if it's not already selected
                      const currentTones = tone ? tone.split(',').map(t => t.trim()) : [];
                      currentTones.push(toneOption);
                      setTone(currentTones.join(', '));
                    }
                  }}
                >
                  <div className="text-sm">{toneOption}</div>
                </div>
              ))}
            </div>
            <Input
              id="tone"
              value={tone}
              onChange={(e) => setTone(e.target.value)}
              placeholder="e.g., Helpful, Authoritative, Friendly"
              className="mt-2"
              required
            />
            <p className="text-sm text-muted-foreground">
              What tone would you like your replies to have? Select multiple or enter your own.
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="systemPrompt">Custom System Prompt (Advanced)</Label>
            <Textarea
              id="systemPrompt"
              value={systemPrompt}
              onChange={(e) => setSystemPrompt(e.target.value)}
              placeholder="Optional: Provide specific instructions for the AI when generating replies"
              rows={5}
              className="w-full"
            />
            <p className="text-sm text-muted-foreground">
              Advanced: Provide specific instructions for the AI when generating replies.
              Leave blank to use the default system prompt.
            </p>
            <div className="mt-4 p-4 bg-muted rounded-md">
              <h4 className="text-sm font-medium mb-2">Example System Prompts:</h4>
              <div className="space-y-3">
                <div
                  className="p-3 bg-card rounded-md cursor-pointer border border-input hover:border-primary transition-colors"
                  onClick={() => setSystemPrompt("You are a helpful assistant that generates professional and concise replies to tweets. Focus on providing value and maintaining a friendly tone. Keep responses under 280 characters. Always be respectful and avoid controversial topics.")}
                >
                  <p className="text-sm font-medium">Professional Assistant</p>
                  <p className="text-xs text-muted-foreground">Generates professional and concise replies with a friendly tone.</p>
                </div>
                <div
                  className="p-3 bg-card rounded-md cursor-pointer border border-input hover:border-primary transition-colors"
                  onClick={() => setSystemPrompt("You are a social media expert who crafts engaging and conversational replies to tweets. Use a casual tone, occasional emojis, and ask follow-up questions to encourage engagement. Keep responses under 280 characters and focus on building relationships.")}
                >
                  <p className="text-sm font-medium">Social Media Expert</p>
                  <p className="text-xs text-muted-foreground">Crafts engaging replies with emojis and follow-up questions.</p>
                </div>
                <div
                  className="p-3 bg-card rounded-md cursor-pointer border border-input hover:border-primary transition-colors"
                  onClick={() => setSystemPrompt("You are a subject matter expert who provides authoritative and informative replies to tweets. Focus on accuracy, clarity, and adding value through your expertise. Cite sources when appropriate. Maintain a professional tone while being accessible to a general audience.")}
                >
                  <p className="text-sm font-medium">Subject Matter Expert</p>
                  <p className="text-xs text-muted-foreground">Provides authoritative and informative replies with citations.</p>
                </div>
              </div>
            </div>
          </div>

          <Button type="submit" disabled={isLoadingKey('save-preferences')} className="w-full">
            {isLoadingKey('save-preferences') ? (
              <>
                <ButtonLoading className="mr-2" />
                Saving...
              </>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Save Preferences
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
