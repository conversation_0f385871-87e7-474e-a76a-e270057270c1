'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Database } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ButtonLoading } from '@/components/ui/loading';
import { useAsyncOperations } from '@/hooks/use-loading';
import { toastSuccess, toastError } from '@/components/ui/toast-provider';
import { logger } from '@/lib/utils/logger';

export default function AddTwitterAccountForm() {
  const router = useRouter();
  const [twitterHandle, setTwitterHandle] = useState('');
  const [databaseError, setDatabaseError] = useState<boolean>(false);

  const { executeOperation, isLoadingKey, errors, clearError } = useAsyncOperations();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!twitterHandle.trim()) {
      toastError(new Error('Please enter a Twitter handle'), 'Invalid Input');
      return;
    }

    const addAccount = async () => {
      logger.info('Adding Twitter account', { handle: twitterHandle });

      const response = await fetch('/api/twitter/accounts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ twitter_handle: twitterHandle }),
      });

      const data = await response.json();

      if (!response.ok) {
        // Check if the error is related to missing database tables
        if (data.error && (
          data.error.includes('does not exist') ||
          data.error.includes('relation')
        )) {
          setDatabaseError(true);
          throw new Error('Database tables not set up yet. Please set up your Supabase database first.');
        }
        throw new Error(data.error || 'Failed to add Twitter account');
      }

      logger.info('Twitter account added successfully', { handle: data.account.twitter_handle });
      return data;
    };

    await executeOperation(
      addAccount,
      'add-account',
      (data) => {
        toastSuccess(`Successfully added @${data.account.twitter_handle}`);
        setTwitterHandle('');
        router.refresh(); // Refresh the page to show the new account
      },
      (error) => {
        logger.error('Error adding Twitter account:', error);
        toastError(error, 'Failed to add account');
      }
    );
  };

  const checkDatabaseStatus = async () => {
    const checkStatus = async () => {
      logger.info('Checking database status');

      const response = await fetch('/api/twitter/accounts');

      if (response.ok) {
        setDatabaseError(false);
        logger.info('Database status check successful');
        return { success: true };
      } else {
        const data = await response.json();
        if (data.error && (
          data.error.includes('does not exist') ||
          data.error.includes('relation')
        )) {
          setDatabaseError(true);
          throw new Error('Database tables not set up yet. Please set up your Supabase database first.');
        } else {
          throw new Error(data.error || 'Failed to check database status');
        }
      }
    };

    await executeOperation(
      checkStatus,
      'check-database',
      () => {
        toastSuccess('Database is properly configured');
      },
      (error) => {
        logger.error('Database status check failed:', error);
        toastError(error, 'Database Check Failed');
      }
    );
  };

  if (databaseError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Setup Required
          </CardTitle>
          <CardDescription>
            Your Supabase database tables need to be created before you can use this feature.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {errors['check-database'] && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{errors['check-database']}</AlertDescription>
            </Alert>
          )}

          <div className="rounded-md bg-muted p-4">
            <h3 className="font-medium mb-2">Next Steps:</h3>
            <ol className="list-decimal pl-5 space-y-2">
              <li>Create a Supabase project if you haven&apos;t already</li>
              <li>Run the database setup SQL script from the docs folder</li>
              <li>Configure your environment variables in .env</li>
              <li>Restart the application</li>
            </ol>
          </div>

          <Button onClick={checkDatabaseStatus} disabled={isLoadingKey('check-database')}>
            {isLoadingKey('check-database') ? (
              <>
                <ButtonLoading className="mr-2" />
                Checking...
              </>
            ) : (
              <>
                <Database className="mr-2 h-4 w-4" />
                Check Again
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="twitter-handle">Twitter Handle</Label>
        <div className="flex gap-2">
          <Input
            id="twitter-handle"
            placeholder="e.g., elonmusk"
            value={twitterHandle}
            onChange={(e) => setTwitterHandle(e.target.value)}
            disabled={isLoadingKey('add-account')}
          />
          <Button type="submit" disabled={isLoadingKey('add-account')}>
            {isLoadingKey('add-account') ? (
              <>
                <ButtonLoading className="mr-2" />
                Adding...
              </>
            ) : (
              'Add'
            )}
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          Enter the Twitter handle without the @ symbol
        </p>
      </div>

      {errors['add-account'] && !databaseError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {errors['add-account']}
            <Button
              variant="outline"
              size="sm"
              className="ml-2"
              onClick={() => clearError('add-account')}
            >
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}
    </form>
  );
}
