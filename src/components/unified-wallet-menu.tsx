'use client';

import React from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import {
  User,
  LogOut,
  Settings2,
  Wallet,
  ChevronDown,
  Wifi,
  WifiOff,
  AlertTriangle,
  Copy,
} from 'lucide-react';
import { useNetwork, NetworkType, getNetworkDisplayInfo, isProductionNetwork } from '@/contexts/network-context';
import { cn } from '@/lib/utils';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

interface UnifiedWalletMenuProps {
  variant?: 'desktop' | 'mobile';
  onPreferencesClick?: () => void;
  className?: string;
}

export function UnifiedWalletMenu({
  variant = 'desktop',
  onPreferencesClick,
  className
}: UnifiedWalletMenuProps) {
  const { wallet, publicKey, connected, connecting, disconnecting, connect, disconnect } = useWallet();
  const { currentNetwork, setNetwork, availableNetworks, isMainnet } = useNetwork();
  const router = useRouter();

  // Get network display info
  const currentNetworkInfo = getNetworkDisplayInfo(currentNetwork);

  // Get network icon
  const getNetworkIcon = (network: NetworkType) => {
    if (network === 'mainnet-beta') {
      return <Wifi className="h-4 w-4 text-green-500" />;
    } else if (network === 'devnet') {
      return <WifiOff className="h-4 w-4 text-orange-500" />;
    } else {
      return <AlertTriangle className="h-4 w-4 text-blue-500" />;
    }
  };

  // Handle wallet disconnection
  const handleDisconnect = async () => {
    try {
      await disconnect();
      toast.success('Wallet disconnected');
    } catch (error) {
      console.error('Failed to disconnect wallet:', error);
      toast.error('Failed to disconnect wallet');
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      const supabase = createSupabaseBrowserClient();
      await supabase.auth.signOut();
      router.push('/');
      toast.success('Signed out successfully');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  };

  // Copy wallet address
  const copyAddress = () => {
    if (publicKey) {
      navigator.clipboard.writeText(publicKey.toString());
      toast.success('Address copied to clipboard');
    }
  };

  // Format wallet address
  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  // If not connected, show wallet connect button
  if (!connected) {
    return (
      <div className={cn("flex items-center gap-2", className)}>
        {/* Network Switcher */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="h-9 px-3"
            >
              {getNetworkIcon(currentNetwork)}
              <span className="ml-1 text-xs">{currentNetworkInfo.name}</span>
              <ChevronDown className="h-3 w-3 ml-1" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel>Solana Network</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {availableNetworks.map((network) => {
              const networkInfo = getNetworkDisplayInfo(network);
              const isSelected = network === currentNetwork;
              const isProd = isProductionNetwork(network);

              return (
                <DropdownMenuItem
                  key={network}
                  onClick={() => setNetwork(network)}
                  className={cn(
                    "flex items-center justify-between cursor-pointer",
                    isSelected && "bg-accent"
                  )}
                >
                  <div className="flex items-center gap-2">
                    {getNetworkIcon(network)}
                    <span>{networkInfo.name}</span>
                    {isProd && (
                      <Badge variant="outline" className="text-xs">
                        LIVE
                      </Badge>
                    )}
                  </div>
                  {isSelected && (
                    <div className="h-2 w-2 rounded-full bg-green-500" />
                  )}
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Wallet Connect */}
        <WalletMultiButton
          className={cn(
            "!bg-primary !text-primary-foreground hover:!bg-primary/90 !rounded-md !h-9 !px-3 !text-sm !font-medium !transition-colors",
            variant === 'mobile' && "!w-full !justify-start"
          )}
        >
          <Wallet className="h-4 w-4 mr-2" />
          {connecting ? 'Connecting...' : 'Connect Wallet'}
        </WalletMultiButton>
      </div>
    );
  }

  // If connected, show unified menu
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "flex items-center gap-2 h-9 px-3",
            variant === 'mobile' && "w-full justify-start",
            className
          )}
        >
          {/* Network indicator */}
          {getNetworkIcon(currentNetwork)}

          {/* Wallet info */}
          <div className="flex items-center gap-2">
            <Wallet className="h-4 w-4" />
            <span className="text-sm">
              {publicKey ? formatAddress(publicKey.toString()) : 'Connected'}
            </span>
          </div>

          {/* User icon */}
          <User className="h-4 w-4" />

          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="w-64">
        {/* Wallet Section */}
        <DropdownMenuLabel className="flex items-center gap-2">
          <Wallet className="h-4 w-4" />
          Wallet & Network
        </DropdownMenuLabel>

        {/* Wallet Address */}
        {publicKey && (
          <DropdownMenuItem onClick={copyAddress} className="cursor-pointer">
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-2">
                <Copy className="h-4 w-4" />
                <span className="font-mono text-sm">
                  {formatAddress(publicKey.toString())}
                </span>
              </div>
              <span className="text-xs text-muted-foreground">Copy</span>
            </div>
          </DropdownMenuItem>
        )}

        {/* Network Submenu */}
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <div className="flex items-center gap-2">
              {getNetworkIcon(currentNetwork)}
              <span>Network: {currentNetworkInfo.name}</span>
              {isMainnet && (
                <Badge variant="outline" className="text-xs">
                  LIVE
                </Badge>
              )}
            </div>
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            {availableNetworks.map((network) => {
              const networkInfo = getNetworkDisplayInfo(network);
              const isSelected = network === currentNetwork;
              const isProd = isProductionNetwork(network);

              return (
                <DropdownMenuItem
                  key={network}
                  onClick={() => setNetwork(network)}
                  className={cn(
                    "flex items-center justify-between cursor-pointer",
                    isSelected && "bg-accent"
                  )}
                >
                  <div className="flex items-center gap-2">
                    {getNetworkIcon(network)}
                    <span>{networkInfo.name}</span>
                    {isProd && (
                      <Badge variant="outline" className="text-xs">
                        LIVE
                      </Badge>
                    )}
                  </div>
                  {isSelected && (
                    <div className="h-2 w-2 rounded-full bg-green-500" />
                  )}
                </DropdownMenuItem>
              );
            })}
          </DropdownMenuSubContent>
        </DropdownMenuSub>

        {/* Wallet Actions */}
        <DropdownMenuItem
          onClick={handleDisconnect}
          disabled={disconnecting}
          className="cursor-pointer text-orange-600 hover:text-orange-700 hover:bg-orange-50 dark:hover:bg-orange-950"
        >
          <Wallet className="h-4 w-4 mr-2" />
          {disconnecting ? 'Disconnecting...' : 'Disconnect Wallet'}
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        {/* User Section */}
        <DropdownMenuLabel className="flex items-center gap-2">
          <User className="h-4 w-4" />
          Account
        </DropdownMenuLabel>

        {/* AI Preferences */}
        {onPreferencesClick && (
          <DropdownMenuItem onClick={onPreferencesClick} className="cursor-pointer">
            <Settings2 className="h-4 w-4 mr-2" />
            AI Preferences
          </DropdownMenuItem>
        )}

        {/* Sign Out */}
        <DropdownMenuItem
          onClick={handleSignOut}
          className="cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950"
        >
          <LogOut className="h-4 w-4 mr-2" />
          Sign out
        </DropdownMenuItem>

        {/* Network Warning */}
        {isMainnet && (
          <>
            <DropdownMenuSeparator />
            <div className="px-2 py-1">
              <div className="flex items-center gap-2 text-xs text-yellow-600 dark:text-yellow-400">
                <AlertTriangle className="h-3 w-3" />
                <span>Mainnet - Real transactions</span>
              </div>
            </div>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
