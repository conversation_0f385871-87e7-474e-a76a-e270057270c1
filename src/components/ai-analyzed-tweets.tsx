'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, RefreshCw, SortDesc, SortAsc } from 'lucide-react';
import Tweet<PERSON>ard from '@/components/tweet-card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface Tweet {
  id: number;
  tweet_id: string;
  author_handle: string;
  content: string;
  fetched_at: string;
  tweet_created_at: string;
  sentiment_score: number | null;
  importance_score: number | null;
  is_marked_irrelevant: boolean;
  raw_data: Record<string, unknown>;
  source_account_id: number;
  source_account: {
    twitter_handle: string;
  };
  ai_analysis?: {
    relevance_score?: number;
    worth_replying?: boolean;
    evaluation_reason?: string;
    reply_suggestions?: string[];
    analyzed_at?: string;
    last_generated_at?: string;
  };
}

interface Account {
  id: number;
  twitter_handle: string;
}

export default function AIAnalyzedTweets() {
  const [tweets, setTweets] = useState<Tweet[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [selectedAccountId, setSelectedAccountId] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('latest');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch accounts for the filter dropdown
  const fetchAccounts = async () => {
    try {
      const response = await fetch('/api/twitter/accounts');

      if (!response.ok) {
        console.error('Failed to fetch accounts');
        return;
      }

      const data = await response.json();
      setAccounts(data.accounts || []);
    } catch (err) {
      console.error('Error fetching accounts:', err);
    }
  };

  const fetchTweets = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (selectedAccountId !== 'all') {
        params.append('account_id', selectedAccountId);
      }
      params.append('sort_by', sortBy);

      const response = await fetch(`/api/twitter/analyzed-tweets?${params}`);
      const data = await response.json();

      if (response.ok) {
        setTweets(data.tweets || []);
      } else {
        setError(data.error || 'Failed to fetch tweets');
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [selectedAccountId, sortBy]);

  // Fetch accounts and tweets on component mount
  useEffect(() => {
    fetchAccounts();
    fetchTweets(); // Initial fetch
  }, [fetchTweets]); // Include fetchTweets dependency

  // Set up event listener for tweet analysis updates
  useEffect(() => {
    // Only add event listener on client side to prevent hydration issues
    if (typeof window !== 'undefined') {
      const handleTweetsAnalyzed = () => {
        // console.log('tweetsAnalyzed event received, refreshing AIAnalyzedTweets...');
        fetchTweets();
      };

      window.addEventListener('tweetsAnalyzed', handleTweetsAnalyzed);

      // Cleanup
      return () => {
        window.removeEventListener('tweetsAnalyzed', handleTweetsAnalyzed);
      };
    }
  }, [fetchTweets]); // Include fetchTweets dependency

  // Fetch tweets when filters change
  useEffect(() => {
    fetchTweets();
  }, [selectedAccountId, sortBy, fetchTweets]);

  const refreshTweets = async () => {
    try {
      setIsRefreshing(true);
      await fetchTweets();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh tweets';
      setError(errorMessage);
    } finally {
      setIsRefreshing(false);
    }
  };

  const markAsIrrelevant = async (tweetId: number) => {
    try {
      // Call the API to mark the tweet as irrelevant
      const response = await fetch('/api/twitter/tweets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tweet_id: tweetId,
          is_irrelevant: true
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to mark tweet as irrelevant');
      }

      // Update the local state
      setTweets(tweets.map(tweet =>
        tweet.id === tweetId
          ? { ...tweet, is_marked_irrelevant: true }
          : tweet
      ));
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Filter tweets for different categories
  const analyzedTweets = tweets.filter(tweet =>
    !tweet.is_marked_irrelevant && tweet.ai_analysis?.analyzed_at
  );

  const worthReplyingTweets = analyzedTweets.filter(tweet =>
    tweet.ai_analysis?.worth_replying === true
  ).sort((a, b) =>
    (b.ai_analysis?.relevance_score || 0) - (a.ai_analysis?.relevance_score || 0)
  );

  const notWorthReplyingTweets = analyzedTweets.filter(tweet =>
    tweet.ai_analysis?.worth_replying === false
  );

  const unanalyzedTweets = tweets.filter(tweet =>
    !tweet.is_marked_irrelevant && !tweet.ai_analysis?.analyzed_at
  );

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className="text-xl font-semibold">AI-Analyzed Tweets <span className="text-sm font-normal text-muted-foreground">(10 most recent per account)</span></h2>
        <div className="flex flex-wrap gap-2 w-full md:w-auto">
          {/* Account Filter */}
          <div className="w-full md:w-auto">
            <Select
              value={selectedAccountId}
              onValueChange={(value) => setSelectedAccountId(value)}
            >
              <SelectTrigger className="w-full md:w-[200px]">
                <SelectValue placeholder="Filter by account" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Accounts</SelectItem>
                {accounts.map((account) => (
                  <SelectItem key={account.id} value={account.id.toString()}>
                    @{account.twitter_handle}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Sort Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="w-full md:w-auto">
                {sortBy === 'latest' ? (
                  <>
                    <SortDesc className="mr-2 h-4 w-4" />
                    Latest
                  </>
                ) : (
                  <>
                    <SortAsc className="mr-2 h-4 w-4" />
                    Most Engagement
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setSortBy('latest')}>
                <SortDesc className="mr-2 h-4 w-4" />
                Latest
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('engagement')}>
                <SortAsc className="mr-2 h-4 w-4" />
                Most Engagement
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Refresh Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={refreshTweets}
            disabled={isRefreshing}
            className="w-full md:w-auto"
          >
            {isRefreshing ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant={error.includes('Successfully') ? 'default' : 'destructive'}>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="worth-replying">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="worth-replying">
            Worth Replying ({worthReplyingTweets.length})
          </TabsTrigger>
          <TabsTrigger value="not-worth-replying">
            Not Worth Replying ({notWorthReplyingTweets.length})
          </TabsTrigger>
          <TabsTrigger value="unanalyzed">
            Unanalyzed ({unanalyzedTweets.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="worth-replying" className="space-y-4 mt-4">
          {worthReplyingTweets.length > 0 ? (
            worthReplyingTweets.map(tweet => (
              <TweetCard
                key={tweet.id}
                tweet={tweet}
                onMarkIrrelevant={() => markAsIrrelevant(tweet.id)}
                showEngagementScore={sortBy === 'engagement'}
              />
            ))
          ) : (
            <p className="text-center py-4 text-muted-foreground">
              No tweets worth replying to found. Try analyzing more tweets.
            </p>
          )}
        </TabsContent>

        <TabsContent value="not-worth-replying" className="space-y-4 mt-4">
          {notWorthReplyingTweets.length > 0 ? (
            notWorthReplyingTweets.map(tweet => (
              <TweetCard
                key={tweet.id}
                tweet={tweet}
                onMarkIrrelevant={() => markAsIrrelevant(tweet.id)}
                showEngagementScore={sortBy === 'engagement'}
              />
            ))
          ) : (
            <p className="text-center py-4 text-muted-foreground">
              No tweets marked as not worth replying to.
            </p>
          )}
        </TabsContent>

        <TabsContent value="unanalyzed" className="space-y-4 mt-4">
          {unanalyzedTweets.length > 0 ? (
            unanalyzedTweets.map(tweet => (
              <TweetCard
                key={tweet.id}
                tweet={tweet}
                onMarkIrrelevant={() => markAsIrrelevant(tweet.id)}
                showEngagementScore={sortBy === 'engagement'}
              />
            ))
          ) : (
            <p className="text-center py-4 text-muted-foreground">
              No unanalyzed tweets found. All tweets have been analyzed.
            </p>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
