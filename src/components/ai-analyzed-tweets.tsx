'use client';

import { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, RefreshCw, SortDesc, SortAsc } from 'lucide-react';
import TweetCard from '@/components/tweet-card';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';


interface Tweet {
  id: number;
  tweet_id: string;
  author_handle: string;
  content: string;
  fetched_at: string;
  tweet_created_at: string;
  sentiment_score: number | null;
  importance_score: number | null;
  is_marked_irrelevant: boolean;
  raw_data: Record<string, unknown>;
  source_account_id: number;
  source_account: {
    twitter_handle: string;
  };
  ai_analysis?: {
    relevance_score?: number;
    worth_replying?: boolean;
    evaluation_reason?: string;
    reply_suggestions?: string[];
    analyzed_at?: string;
    last_generated_at?: string;
  };
}

interface Account {
  id: number;
  twitter_handle: string;
}

// Custom hooks for data fetching with React Query
function useTwitterAccounts() {
  return useQuery({
    queryKey: ['twitter-accounts'],
    queryFn: async () => {
      const response = await fetch('/api/twitter/accounts');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch accounts');
      }

      return data.accounts || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

function useAnalyzedTweets(selectedAccountId: string, sortBy: string) {
  return useQuery({
    queryKey: ['analyzed-tweets', selectedAccountId, sortBy],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (selectedAccountId !== 'all') {
        params.append('account_id', selectedAccountId);
      }
      params.append('sort_by', sortBy);

      const response = await fetch(`/api/twitter/analyzed-tweets?${params}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch tweets');
      }

      return data.tweets || [];
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
}

const AIAnalyzedTweets = memo(function AIAnalyzedTweets() {
  const [selectedAccountId, setSelectedAccountId] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('latest');
  const queryClient = useQueryClient();

  // Fetch data using React Query
  const { data: accounts = [], isLoading: accountsLoading } = useTwitterAccounts();
  const {
    data: tweets = [],
    isLoading: tweetsLoading,
    error: tweetsError,
    refetch: refetchTweets
  } = useAnalyzedTweets(selectedAccountId, sortBy);

  const isLoading = accountsLoading || tweetsLoading;
  const error = tweetsError?.message;

  // Memoized filtered tweets for better performance - moved before early returns
  const analyzedTweets = useMemo(() =>
    tweets.filter((tweet: Tweet) =>
      !tweet.is_marked_irrelevant && tweet.ai_analysis?.analyzed_at
    ), [tweets]
  );

  const worthReplyingTweets = useMemo(() =>
    analyzedTweets.filter((tweet: Tweet) =>
      tweet.ai_analysis?.worth_replying === true
    ).sort((a: Tweet, b: Tweet) =>
      (b.ai_analysis?.relevance_score || 0) - (a.ai_analysis?.relevance_score || 0)
    ), [analyzedTweets]
  );

  const notWorthReplyingTweets = useMemo(() =>
    analyzedTweets.filter((tweet: Tweet) =>
      tweet.ai_analysis?.worth_replying === false
    ), [analyzedTweets]
  );

  const unanalyzedTweets = useMemo(() =>
    tweets.filter((tweet: Tweet) =>
      !tweet.is_marked_irrelevant && !tweet.ai_analysis?.analyzed_at
    ), [tweets]
  );

  // Set up event listener for tweet analysis updates
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleTweetsAnalyzed = () => {
        // Invalidate and refetch tweets when analysis is complete
        queryClient.invalidateQueries({ queryKey: ['analyzed-tweets'] });
      };

      window.addEventListener('tweetsAnalyzed', handleTweetsAnalyzed);

      return () => {
        window.removeEventListener('tweetsAnalyzed', handleTweetsAnalyzed);
      };
    }
  }, [queryClient]);

  const refreshTweets = useCallback(async () => {
    await refetchTweets();
  }, [refetchTweets]);

  const markAsIrrelevant = useCallback(async (tweetId: number) => {
    try {
      // Call the API to mark the tweet as irrelevant
      const response = await fetch('/api/twitter/tweets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          tweet_id: tweetId,
          is_irrelevant: true
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to mark tweet as irrelevant');
      }

      // Invalidate queries to refetch updated data
      queryClient.invalidateQueries({ queryKey: ['analyzed-tweets'] });
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      console.error('Failed to mark tweet as irrelevant:', errorMessage);
    }
  }, [queryClient]);

  if (isLoading) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className="text-xl font-semibold">AI-Analyzed Tweets <span className="text-sm font-normal text-muted-foreground">(10 most recent per account)</span></h2>
        <div className="flex flex-wrap gap-2 w-full md:w-auto">
          {/* Account Filter */}
          <div className="w-full md:w-auto">
            <Select
              value={selectedAccountId}
              onValueChange={(value) => setSelectedAccountId(value)}
            >
              <SelectTrigger className="w-full md:w-[200px]">
                <SelectValue placeholder="Filter by account" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Accounts</SelectItem>
                {accounts.map((account: Account) => (
                  <SelectItem key={account.id} value={account.id.toString()}>
                    @{account.twitter_handle}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Sort Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="w-full md:w-auto">
                {sortBy === 'latest' ? (
                  <>
                    <SortDesc className="mr-2 h-4 w-4" />
                    Latest
                  </>
                ) : (
                  <>
                    <SortAsc className="mr-2 h-4 w-4" />
                    Most Engagement
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setSortBy('latest')}>
                <SortDesc className="mr-2 h-4 w-4" />
                Latest
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setSortBy('engagement')}>
                <SortAsc className="mr-2 h-4 w-4" />
                Most Engagement
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Refresh Button */}
          <Button
            variant="outline"
            size="sm"
            onClick={refreshTweets}
            disabled={tweetsLoading}
            className="w-full md:w-auto"
          >
            {tweetsLoading ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Refresh
              </>
            )}
          </Button>
        </div>
      </div>

      {error && (
        <Alert variant={error.includes('Successfully') ? 'default' : 'destructive'}>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="worth-replying">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="worth-replying">
            Worth Replying ({worthReplyingTweets.length})
          </TabsTrigger>
          <TabsTrigger value="not-worth-replying">
            Not Worth Replying ({notWorthReplyingTweets.length})
          </TabsTrigger>
          <TabsTrigger value="unanalyzed">
            Unanalyzed ({unanalyzedTweets.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="worth-replying" className="space-y-4 mt-4">
          {worthReplyingTweets.length > 0 ? (
            worthReplyingTweets.map((tweet: Tweet) => (
              <TweetCard
                key={tweet.id}
                tweet={tweet}
                onMarkIrrelevant={() => markAsIrrelevant(tweet.id)}
                showEngagementScore={sortBy === 'engagement'}
              />
            ))
          ) : (
            <p className="text-center py-4 text-muted-foreground">
              No tweets worth replying to found. Try analyzing more tweets.
            </p>
          )}
        </TabsContent>

        <TabsContent value="not-worth-replying" className="space-y-4 mt-4">
          {notWorthReplyingTweets.length > 0 ? (
            notWorthReplyingTweets.map((tweet: Tweet) => (
              <TweetCard
                key={tweet.id}
                tweet={tweet}
                onMarkIrrelevant={() => markAsIrrelevant(tweet.id)}
                showEngagementScore={sortBy === 'engagement'}
              />
            ))
          ) : (
            <p className="text-center py-4 text-muted-foreground">
              No tweets marked as not worth replying to.
            </p>
          )}
        </TabsContent>

        <TabsContent value="unanalyzed" className="space-y-4 mt-4">
          {unanalyzedTweets.length > 0 ? (
            unanalyzedTweets.map((tweet: Tweet) => (
              <TweetCard
                key={tweet.id}
                tweet={tweet}
                onMarkIrrelevant={() => markAsIrrelevant(tweet.id)}
                showEngagementScore={sortBy === 'engagement'}
              />
            ))
          ) : (
            <p className="text-center py-4 text-muted-foreground">
              No unanalyzed tweets found. All tweets have been analyzed.
            </p>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
});

export default AIAnalyzedTweets;
