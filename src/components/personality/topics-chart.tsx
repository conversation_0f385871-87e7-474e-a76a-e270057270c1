'use client';

import { Topic } from '@/types/personality';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface TopicsChartProps {
  topics: Topic[];
}

export function TopicsChart({ topics }: TopicsChartProps) {
  // Sort topics by score (descending)
  const sortedTopics = [...topics].sort((a, b) => b.score - a.score);

  // Get color based on score
  const getColorClass = (score: number) => {
    if (score >= 0.8) return 'bg-primary';
    if (score >= 0.6) return 'bg-primary/80';
    if (score >= 0.4) return 'bg-primary/60';
    if (score >= 0.2) return 'bg-primary/40';
    return 'bg-primary/20';
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-wrap gap-2 mb-6">
        {sortedTopics.slice(0, 10).map((topic) => (
          <Badge
            key={topic.name}
            variant="outline"
            className={`text-sm py-1 px-3 ${topic.score >= 0.7 ? 'border-primary' : ''}`}
          >
            {topic.name}
            <span className="ml-2 text-xs opacity-70">{Math.round(topic.score * 100)}%</span>
          </Badge>
        ))}
      </div>

      <div className="grid grid-cols-1 gap-4">
        {sortedTopics.slice(0, 5).map((topic) => (
          <Card key={topic.name} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg">{topic.name}</CardTitle>
                <Badge variant="outline">{Math.round(topic.score * 100)}%</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="w-full bg-secondary h-2 rounded-full">
                <div
                  className={`h-2 rounded-full ${getColorClass(topic.score)}`}
                  style={{ width: `${topic.score * 100}%` }}
                ></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-sm text-muted-foreground mt-4">
        <p>
          <strong>Note:</strong> These topics represent the subjects you discuss most frequently in your tweets.
          Higher percentages indicate stronger interest or engagement with these topics.
        </p>
      </div>
    </div>
  );
}
