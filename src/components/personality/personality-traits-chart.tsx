'use client';

import { PersonalityTraits, traitDescriptions } from '@/types/personality';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface PersonalityTraitsChartProps {
  traits: PersonalityTraits;
}

export function PersonalityTraitsChart({ traits }: PersonalityTraitsChartProps) {
  // Convert traits to array for easier rendering
  const traitEntries = Object.entries(traits).filter(([key]) => 
    ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism'].includes(key)
  );

  // Sort traits by score (descending)
  traitEntries.sort((a, b) => b[1] - a[1]);

  // Format trait name for display
  const formatTraitName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1);
  };

  // Determine if a trait score is high or low
  const getTraitLevel = (score: number) => {
    return score >= 0.5 ? 'high' : 'low';
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {traitEntries.map(([trait, score]) => (
          <Card key={trait} className={`overflow-hidden ${score >= 0.7 ? 'border-primary' : ''}`}>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">{formatTraitName(trait)}</CardTitle>
              <CardDescription>
                Score: {Math.round(score * 100)}%
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="w-full bg-secondary h-2 rounded-full mb-4">
                <div 
                  className="bg-primary h-2 rounded-full" 
                  style={{ width: `${score * 100}%` }}
                ></div>
              </div>
              <p className="text-sm">
                {traitDescriptions[trait as keyof typeof traitDescriptions]?.[getTraitLevel(score)] || 
                  "This trait reflects your personality tendencies in this dimension."}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-sm text-muted-foreground mt-4">
        <p>
          <strong>Note:</strong> These traits are based on the Big Five personality model, 
          which is widely used in psychology to describe human personality.
        </p>
      </div>
    </div>
  );
}
