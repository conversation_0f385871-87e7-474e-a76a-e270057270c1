'use client';

import { PersonalityAnalysis } from '@/types/personality';
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface PersonalityInsightsProps {
  analysis: PersonalityAnalysis;
}

export function PersonalityInsights({ analysis }: PersonalityInsightsProps) {
  // Get dominant traits (top 2)
  const dominantTraits = Object.entries(analysis.traits)
    .filter(([key]) => ['openness', 'conscientiousness', 'extraversion', 'agreeableness', 'neuroticism'].includes(key))
    .sort((a, b) => b[1] - a[1])
    .slice(0, 2)
    .map(([trait]) => trait.charAt(0).toUpperCase() + trait.slice(1));

  // Get top topics (top 3)
  const topTopics = [...analysis.topics]
    .sort((a, b) => b.score - a.score)
    .slice(0, 3)
    .map(topic => topic.name);

  // Get writing style characteristics
  const writingStyleTraits = [];
  if (analysis.writing_style.formality >= 0.6) writingStyleTraits.push('formal');
  else if (analysis.writing_style.formality <= 0.4) writingStyleTraits.push('casual');

  if (analysis.writing_style.emotionality >= 0.6) writingStyleTraits.push('emotional');
  else if (analysis.writing_style.emotionality <= 0.4) writingStyleTraits.push('neutral');

  if (analysis.writing_style.complexity >= 0.6) writingStyleTraits.push('complex');
  else if (analysis.writing_style.complexity <= 0.4) writingStyleTraits.push('straightforward');

  if (analysis.writing_style.assertiveness >= 0.6) writingStyleTraits.push('assertive');
  else if (analysis.writing_style.assertiveness <= 0.4) writingStyleTraits.push('thoughtful');

  // Generate personality summary
  const generatePersonalitySummary = () => {
    const traits = dominantTraits.join(' and ');
    const topics = topTopics.join(', ');
    const style = writingStyleTraits.join(', ');

    return `Your Twitter personality shows strong ${traits} traits. You're most interested in ${topics}. Your writing style is ${style}.`;
  };

  // Generate communication tips based on personality
  const generateCommunicationTips = () => {
    const tips = [];

    // Tips based on extraversion
    if (analysis.traits.extraversion >= 0.7) {
      tips.push('Your high extraversion suggests you engage well with others. Continue to initiate conversations and build connections.');
    } else if (analysis.traits.extraversion <= 0.3) {
      tips.push('Your more reserved nature means you likely prefer meaningful one-on-one interactions. Focus on quality over quantity in your engagements.');
    }

    // Tips based on openness
    if (analysis.traits.openness >= 0.7) {
      tips.push('Your high openness indicates you enjoy exploring new ideas. Share your creative insights to engage your audience.');
    } else if (analysis.traits.openness <= 0.3) {
      tips.push('Your practical approach is valuable. Focus on sharing concrete, useful information that others can apply immediately.');
    }

    // Tips based on writing style
    if (analysis.writing_style.formality >= 0.7) {
      tips.push('Your formal writing style conveys authority. Consider occasionally using more casual language to increase relatability.');
    } else if (analysis.writing_style.formality <= 0.3) {
      tips.push('Your casual style is approachable. For professional topics, you might occasionally benefit from a slightly more formal tone.');
    }

    return tips;
  };

  const communicationTips = generateCommunicationTips();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Personality Insights</CardTitle>
          <CardDescription>
            What your Twitter activity reveals about you
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-medium mb-2">Summary</h3>
            <p>{generatePersonalitySummary()}</p>
          </div>

          <div>
            <h3 className="font-medium mb-2">Communication Tips</h3>
            <ul className="list-disc pl-5 space-y-1">
              {communicationTips.map((tip, index) => (
                <li key={index}>{tip}</li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="font-medium mb-2">Content Suggestions</h3>
            <p>
              Based on your interests in {topTopics.join(', ')}, consider creating content that combines these topics
              with your unique perspective shaped by your {dominantTraits.join(' and ')} traits.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
