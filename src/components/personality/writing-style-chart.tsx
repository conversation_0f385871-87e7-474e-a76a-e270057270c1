'use client';

import { WritingStyle, writingStyleDescriptions } from '@/types/personality';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface WritingStyleChartProps {
  writingStyle: WritingStyle;
}

export function WritingStyleChart({ writingStyle }: WritingStyleChartProps) {
  // Convert writing style to array for easier rendering
  const styleEntries = Object.entries(writingStyle).filter(([key]) => 
    ['formality', 'emotionality', 'complexity', 'assertiveness'].includes(key)
  );

  // Sort by score (descending)
  styleEntries.sort((a, b) => b[1] - a[1]);

  // Format style name for display
  const formatStyleName = (name: string) => {
    return name.charAt(0).toUpperCase() + name.slice(1);
  };

  // Determine if a style score is high or low
  const getStyleLevel = (score: number) => {
    return score >= 0.5 ? 'high' : 'low';
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {styleEntries.map(([style, score]) => (
          <Card key={style} className={`overflow-hidden ${score >= 0.7 ? 'border-primary' : ''}`}>
            <CardHeader className="pb-2">
              <CardTitle className="text-lg">{formatStyleName(style)}</CardTitle>
              <CardDescription>
                Score: {Math.round(score * 100)}%
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="w-full bg-secondary h-2 rounded-full mb-4">
                <div 
                  className="bg-primary h-2 rounded-full" 
                  style={{ width: `${score * 100}%` }}
                ></div>
              </div>
              <p className="text-sm">
                {writingStyleDescriptions[style as keyof typeof writingStyleDescriptions]?.[getStyleLevel(score)] || 
                  "This metric reflects your writing tendencies in this dimension."}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      <div className="text-sm text-muted-foreground mt-4">
        <p>
          <strong>Note:</strong> These metrics describe your writing style based on the language patterns in your tweets.
          They can help you understand how you communicate and how others might perceive your messages.
        </p>
      </div>
    </div>
  );
}
