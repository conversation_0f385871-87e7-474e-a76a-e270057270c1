'use client';

import { useState } from 'react';
import { useWallet } from '@solana/wallet-adapter-react';
import { PublicKey } from '@solana/web3.js';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { toast } from 'sonner';
import { prepareTopUpTransaction, formatTransactionSignature, getTransactionExplorerUrl } from '@/lib/solana/topup-service';
import {
  Send,
  CheckCircle,
  AlertTriangle,
  Loader2,
  ExternalLink,
  Copy,
  Wallet
} from 'lucide-react';

interface TopUpFormProps {
  topUpInfo: {
    limits: {
      minAmount: number;
      maxAmount: number;
      feePercentage: number;
    };
    recipientAddress: string;
  };
  userWallet: PublicKey;
  selectedAmount?: number;
}

type TransactionStatus = 'idle' | 'preparing' | 'signing' | 'confirming' | 'recording' | 'success' | 'error';

export function TopUpForm({ topUpInfo, userWallet, selectedAmount }: TopUpFormProps) {
  const { signTransaction } = useWallet();

  const [status, setStatus] = useState<TransactionStatus>('idle');
  const [error, setError] = useState<string | null>(null);
  const [transactionSignature, setTransactionSignature] = useState<string | null>(null);
  const [progress, setProgress] = useState(0);

  const isValidAmount = selectedAmount &&
    selectedAmount >= topUpInfo.limits.minAmount &&
    selectedAmount <= topUpInfo.limits.maxAmount;

  const calculateFee = (amount: number): number => {
    return amount * (topUpInfo.limits.feePercentage / 100);
  };

  const calculateNetAmount = (amount: number): number => {
    return amount - calculateFee(amount);
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Copied!", {
        description: "Address copied to clipboard",
      });
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const handleTopUp = async () => {
    if (!selectedAmount || !isValidAmount || !signTransaction) {
      return;
    }

    try {
      setStatus('preparing');
      setError(null);
      setProgress(10);

      console.log('🚀 Starting top-up process:', {
        amount: selectedAmount,
        userWallet: userWallet.toString(),
        recipient: topUpInfo.recipientAddress,
      });

      // Prepare the transaction (using testnet for now)
      const transaction = await prepareTopUpTransaction(userWallet, topUpInfo.recipientAddress, selectedAmount, undefined, 'testnet');
      setProgress(30);

      setStatus('signing');
      console.log('✍️ Requesting transaction signature...');

      // Sign the transaction
      const signedTransaction = await signTransaction(transaction);
      setProgress(50);

      setStatus('confirming');
      console.log('📡 Sending transaction...');

      // Send the transaction
      const { solanaConnection } = await import('@/lib/solana/connection');
      const signature = await solanaConnection.sendRawTransaction(signedTransaction.serialize());
      setTransactionSignature(signature);
      setProgress(70);

      console.log('⏳ Confirming transaction:', signature);

      // Confirm the transaction
      const confirmation = await solanaConnection.confirmTransaction(signature, 'confirmed');

      if (confirmation.value.err) {
        throw new Error(`Transaction failed: ${confirmation.value.err}`);
      }

      setProgress(85);
      setStatus('recording');

      console.log('📝 Recording transaction in database...');

      // Record the transaction in the database
      const recordResponse = await fetch('/api/topup/record', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          transactionSignature: signature,
          amount: selectedAmount,
          walletAddress: userWallet.toString(),
          network: 'devnet', // TODO: Get from network context
        }),
      });

      if (!recordResponse.ok) {
        const errorData = await recordResponse.json();
        throw new Error(errorData.error || 'Failed to record transaction');
      }

      const recordResult = await recordResponse.json();
      setProgress(100);
      setStatus('success');

      console.log('✅ Top-up completed successfully:', recordResult);

      toast.success("Top-up Successful!", {
        description: `${calculateNetAmount(selectedAmount).toFixed(4)} $COPY added to your account`,
      });

    } catch (err) {
      console.error('❌ Top-up failed:', err);
      setStatus('error');

      // Parse specific error messages
      let errorMessage = 'Unknown error occurred';
      if (err instanceof Error) {
        if (err.message.includes('insufficient funds')) {
          errorMessage = 'Insufficient SOL for transaction fees. Please add SOL to your wallet.';
        } else if (err.message.includes('TokenAccountNotFoundError')) {
          errorMessage = 'Token account not found. Please ensure you have $COPY tokens in your wallet.';
        } else if (err.message.includes('User rejected')) {
          errorMessage = 'Transaction was rejected by user.';
        } else {
          errorMessage = err.message;
        }
      }

      setError(errorMessage);

      toast.error("Top-up Failed", {
        description: errorMessage,
      });
    }
  };

  const getStatusMessage = (): string => {
    switch (status) {
      case 'preparing': return 'Preparing transaction...';
      case 'signing': return 'Please sign the transaction in your wallet';
      case 'confirming': return 'Confirming transaction on blockchain...';
      case 'recording': return 'Recording transaction...';
      case 'success': return 'Top-up completed successfully!';
      case 'error': return 'Transaction failed';
      default: return '';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'preparing':
      case 'signing':
      case 'confirming':
      case 'recording':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Send className="h-4 w-4" />;
    }
  };

  const isProcessing = ['preparing', 'signing', 'confirming', 'recording'].includes(status);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wallet className="h-5 w-5" />
          Send Transaction
        </CardTitle>
        <CardDescription>
          Review and confirm your top-up transaction
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Recipient Info */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Recipient Address:</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => copyToClipboard(topUpInfo.recipientAddress)}
              className="h-auto p-1"
            >
              <Copy className="h-3 w-3" />
            </Button>
          </div>
          <div className="bg-muted p-3 rounded-md font-mono text-sm break-all">
            {topUpInfo.recipientAddress}
          </div>
        </div>

        {/* Transaction Summary */}
        {selectedAmount && isValidAmount && (
          <div className="space-y-3">
            <h4 className="font-medium">Transaction Summary</h4>
            <div className="bg-muted/50 p-4 rounded-md space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Amount to Send:</span>
                <span className="font-medium">{selectedAmount} $COPY</span>
              </div>
              <div className="flex justify-between text-muted-foreground">
                <span>Transaction Fee:</span>
                <span>-{calculateFee(selectedAmount).toFixed(4)} $COPY</span>
              </div>
              <div className="border-t pt-2 flex justify-between font-medium">
                <span>You'll Receive:</span>
                <span className="text-green-600">
                  {calculateNetAmount(selectedAmount).toFixed(4)} $COPY
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Progress */}
        {isProcessing && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              {getStatusIcon()}
              <span className="text-sm">{getStatusMessage()}</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        {/* Error */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Success */}
        {status === 'success' && transactionSignature && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription className="space-y-2">
              <p>Transaction completed successfully!</p>
              <div className="flex items-center gap-2">
                <span className="text-xs">Signature:</span>
                <code className="text-xs bg-muted px-2 py-1 rounded">
                  {formatTransactionSignature(transactionSignature)}
                </code>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => window.open(getTransactionExplorerUrl(transactionSignature), '_blank')}
                  className="h-auto p-1"
                >
                  <ExternalLink className="h-3 w-3" />
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Action Button */}
        <Button
          onClick={handleTopUp}
          disabled={!isValidAmount || isProcessing || status === 'success'}
          className="w-full"
          size="lg"
        >
          {getStatusIcon()}
          {isProcessing ? getStatusMessage() :
           status === 'success' ? 'Transaction Complete' :
           !selectedAmount ? 'Select Amount' :
           !isValidAmount ? 'Invalid Amount' :
           `Send ${selectedAmount} $COPY`}
        </Button>

        {!isValidAmount && selectedAmount && (
          <p className="text-sm text-muted-foreground text-center">
            Please select an amount between {topUpInfo.limits.minAmount} and {topUpInfo.limits.maxAmount} $COPY
          </p>
        )}
      </CardContent>
    </Card>
  );
}
