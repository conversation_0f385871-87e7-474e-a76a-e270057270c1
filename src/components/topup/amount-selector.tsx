'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Coins, Calculator } from 'lucide-react';

interface AmountSelectorProps {
  predefinedAmounts: number[];
  limits: {
    minAmount: number;
    maxAmount: number;
    feePercentage: number;
  };
  onAmountSelect?: (amount: number) => void;
  selectedAmount?: number;
}

export function AmountSelector({ 
  predefinedAmounts, 
  limits, 
  onAmountSelect,
  selectedAmount 
}: AmountSelectorProps) {
  const [customAmount, setCustomAmount] = useState<string>('');
  const [showCustom, setShowCustom] = useState(false);

  const calculateFee = (amount: number): number => {
    return amount * (limits.feePercentage / 100);
  };

  const calculateNetAmount = (amount: number): number => {
    return amount - calculateFee(amount);
  };

  const handlePredefinedSelect = (amount: number) => {
    setCustomAmount('');
    setShowCustom(false);
    onAmountSelect?.(amount);
  };

  const handleCustomAmountChange = (value: string) => {
    setCustomAmount(value);
    const numValue = parseFloat(value);
    if (!isNaN(numValue) && numValue > 0) {
      onAmountSelect?.(numValue);
    }
  };

  const isValidAmount = (amount: number): boolean => {
    return amount >= limits.minAmount && amount <= limits.maxAmount;
  };

  const getAmountStatus = (amount: number): 'valid' | 'low' | 'high' => {
    if (amount < limits.minAmount) return 'low';
    if (amount > limits.maxAmount) return 'high';
    return 'valid';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Coins className="h-5 w-5" />
          Select Amount
        </CardTitle>
        <CardDescription>
          Choose how many $COPY tokens you want to send for top-up
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Predefined Amounts */}
        <div>
          <Label className="text-sm font-medium mb-3 block">Quick Select</Label>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
            {predefinedAmounts.map((amount) => {
              const isSelected = selectedAmount === amount && !showCustom;
              const status = getAmountStatus(amount);
              
              return (
                <Button
                  key={amount}
                  variant={isSelected ? "default" : "outline"}
                  className={`relative h-16 flex flex-col items-center justify-center ${
                    status !== 'valid' ? 'opacity-50' : ''
                  }`}
                  onClick={() => handlePredefinedSelect(amount)}
                  disabled={status !== 'valid'}
                >
                  <span className="text-lg font-bold">{amount}</span>
                  <span className="text-xs text-muted-foreground">$COPY</span>
                  {status !== 'valid' && (
                    <Badge 
                      variant="destructive" 
                      className="absolute -top-2 -right-2 text-xs px-1"
                    >
                      {status === 'low' ? 'Low' : 'High'}
                    </Badge>
                  )}
                </Button>
              );
            })}
          </div>
        </div>

        {/* Custom Amount */}
        <div>
          <div className="flex items-center justify-between mb-3">
            <Label className="text-sm font-medium">Custom Amount</Label>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowCustom(!showCustom)}
            >
              {showCustom ? 'Hide' : 'Show'} Custom
            </Button>
          </div>
          
          {showCustom && (
            <div className="space-y-3">
              <div className="relative">
                <Input
                  type="number"
                  placeholder={`Enter amount (${limits.minAmount} - ${limits.maxAmount})`}
                  value={customAmount}
                  onChange={(e) => handleCustomAmountChange(e.target.value)}
                  min={limits.minAmount}
                  max={limits.maxAmount}
                  step="0.1"
                  className="pr-16"
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                  $COPY
                </div>
              </div>
              
              {customAmount && (
                <div className="text-sm text-muted-foreground">
                  {(() => {
                    const amount = parseFloat(customAmount);
                    if (isNaN(amount)) return 'Invalid amount';
                    
                    const status = getAmountStatus(amount);
                    if (status === 'low') {
                      return `Minimum amount is ${limits.minAmount} $COPY`;
                    }
                    if (status === 'high') {
                      return `Maximum amount is ${limits.maxAmount} $COPY`;
                    }
                    return `Valid amount: ${amount} $COPY`;
                  })()}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Amount Breakdown */}
        {selectedAmount && selectedAmount > 0 && (
          <Card className="bg-muted/50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <Calculator className="h-4 w-4" />
                <span className="font-medium">Transaction Breakdown</span>
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Amount to Send:</span>
                  <span className="font-medium">{selectedAmount} $COPY</span>
                </div>
                <div className="flex justify-between text-muted-foreground">
                  <span>Transaction Fee ({limits.feePercentage}%):</span>
                  <span>-{calculateFee(selectedAmount).toFixed(4)} $COPY</span>
                </div>
                <div className="border-t pt-2 flex justify-between font-medium">
                  <span>You'll Receive:</span>
                  <span className="text-green-600">
                    {calculateNetAmount(selectedAmount).toFixed(4)} $COPY
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Limits Info */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>• Minimum top-up: {limits.minAmount} $COPY</p>
          <p>• Maximum top-up: {limits.maxAmount} $COPY</p>
          <p>• Transaction fee: {limits.feePercentage}% of the amount</p>
        </div>
      </CardContent>
    </Card>
  );
}
