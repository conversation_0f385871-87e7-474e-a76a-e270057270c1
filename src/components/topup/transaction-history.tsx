'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { formatTransactionSignature, getTransactionExplorerUrl } from '@/lib/solana/topup-service';
import { 
  History, 
  ExternalLink, 
  RefreshCw, 
  AlertTriangle,
  CheckCircle,
  Clock,
  ArrowUpCircle
} from 'lucide-react';

interface Transaction {
  id: string;
  amount: number;
  description: string;
  transactionSignature: string;
  recipientAddress: string;
  network: string;
  confirmationStatus: string;
  grossAmount: number;
  feeAmount: number;
  netAmount: number;
  createdAt: string;
  updatedAt: string;
}

interface TransactionHistoryData {
  transactions: Transaction[];
  pagination: {
    limit: number;
    offset: number;
    total: number;
    hasMore: boolean;
  };
  balance: {
    balance: number;
    totalEarned: number;
    totalSpent: number;
  };
}

export function TransactionHistory() {
  const [data, setData] = useState<TransactionHistoryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchHistory = async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const response = await fetch('/api/topup/history?limit=20&offset=0');
      if (!response.ok) {
        throw new Error('Failed to fetch transaction history');
      }

      const historyData = await response.json();
      setData(historyData);
    } catch (err) {
      console.error('❌ Error fetching history:', err);
      setError(err instanceof Error ? err.message : 'Failed to load transaction history');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchHistory();
  }, []);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmed':
        return (
          <Badge variant="default" className="bg-green-500/10 text-green-600 border-green-500/20">
            <CheckCircle className="h-3 w-3 mr-1" />
            Confirmed
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        );
      case 'failed':
        return (
          <Badge variant="destructive">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Failed
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        );
    }
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Transaction History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-16 bg-muted rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Transaction History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            onClick={() => fetchHistory()} 
            className="mt-4"
            variant="outline"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <History className="h-5 w-5" />
              Transaction History
            </CardTitle>
            <CardDescription>
              Your top-up transaction history and current balance
            </CardDescription>
          </div>
          <Button
            onClick={() => fetchHistory(true)}
            variant="outline"
            size="sm"
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Balance Summary */}
        {data?.balance && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Current Balance</p>
              <p className="text-2xl font-bold text-green-600">
                {data.balance.balance.toFixed(4)} $COPY
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Total Earned</p>
              <p className="text-lg font-semibold">
                {data.balance.totalEarned.toFixed(4)} $COPY
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Total Spent</p>
              <p className="text-lg font-semibold">
                {data.balance.totalSpent.toFixed(4)} $COPY
              </p>
            </div>
          </div>
        )}

        {/* Transactions List */}
        {data?.transactions && data.transactions.length > 0 ? (
          <div className="space-y-4">
            <h4 className="font-medium">Recent Transactions</h4>
            {data.transactions.map((transaction) => (
              <div
                key={transaction.id}
                className="border rounded-lg p-4 space-y-3"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <ArrowUpCircle className="h-4 w-4 text-green-500" />
                    <span className="font-medium">Top-up Deposit</span>
                  </div>
                  {getStatusBadge(transaction.confirmationStatus)}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Amount Sent:</p>
                    <p className="font-medium">{transaction.grossAmount} $COPY</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Amount Received:</p>
                    <p className="font-medium text-green-600">
                      {transaction.netAmount.toFixed(4)} $COPY
                    </p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Transaction Fee:</p>
                    <p className="font-medium">{transaction.feeAmount.toFixed(4)} $COPY</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Date:</p>
                    <p className="font-medium">{formatDate(transaction.createdAt)}</p>
                  </div>
                </div>

                {transaction.transactionSignature && (
                  <div className="flex items-center gap-2 pt-2 border-t">
                    <span className="text-xs text-muted-foreground">Signature:</span>
                    <code className="text-xs bg-muted px-2 py-1 rounded font-mono">
                      {formatTransactionSignature(transaction.transactionSignature)}
                    </code>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(
                        getTransactionExplorerUrl(transaction.transactionSignature, transaction.network),
                        '_blank'
                      )}
                      className="h-auto p-1"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                )}
              </div>
            ))}

            {/* Pagination Info */}
            {data.pagination.total > data.transactions.length && (
              <div className="text-center text-sm text-muted-foreground">
                Showing {data.transactions.length} of {data.pagination.total} transactions
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <History className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="font-medium mb-2">No Transactions Yet</h3>
            <p className="text-sm text-muted-foreground">
              Your top-up transactions will appear here once you make your first deposit.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
