'use client';

import { <PERSON>actNode, useMemo, useCallback } from 'react';
import { ConnectionProvider, WalletProvider } from '@solana/wallet-adapter-react';
import { WalletError } from '@solana/wallet-adapter-base';
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui';
import {
  PhantomWalletAdapter,
  SolflareWalletAdapter,
  TorusWalletAdapter,
  LedgerWalletAdapter,
} from '@solana/wallet-adapter-wallets';
import { WalletConnectWalletAdapter } from '@solana/wallet-adapter-walletconnect';
import { useNetwork, getWalletAdapterNetwork } from '@/contexts/network-context';
import { CLIENT_CONFIG } from '@/lib/config/client';

// Import Solana wallet adapter styles
import '@solana/wallet-adapter-react-ui/styles.css';

interface SolanaWalletProviderProps {
  children: ReactNode;
}

// Internal component that uses the network context
function SolanaWalletProviderInternal({ children }: SolanaWalletProviderProps) {
  // Get the current network from context
  const { currentNetwork, networkConfig } = useNetwork();

  // Convert to WalletAdapterNetwork
  const network = getWalletAdapterNetwork(currentNetwork);

  // Get the RPC endpoint
  const endpoint = useMemo(() => {
    console.log('🔗 Solana connection initialized:', {
      network: currentNetwork,
      url: networkConfig.url,
      chainId: networkConfig.chainId
    });
    return networkConfig.url;
  }, [currentNetwork, networkConfig]);

  // Error handler for wallet errors
  const onError = useCallback((error: WalletError) => {
    console.error('🚨 Wallet error occurred:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });

    // Handle specific wallet errors
    switch (error.name) {
      case 'WalletNotReadyError':
        console.warn('⚠️ Wallet not ready - user may need to install or unlock wallet');
        break;
      case 'WalletConnectionError':
        console.warn('⚠️ Wallet connection failed - retrying may help');
        break;
      case 'WalletDisconnectedError':
        console.warn('⚠️ Wallet disconnected unexpectedly');
        break;
      case 'WalletNotConnectedError':
        // Don't log this as an error - it's expected when wallet isn't connected
        console.log('ℹ️ Wallet not connected');
        break;
      default:
        // Don't show error for user rejection
        if (error.message?.includes('User rejected')) {
          console.log('ℹ️ User rejected wallet connection');
          return;
        }
        console.error('❌ Unknown wallet error:', error);
    }
  }, []);

  // Configure supported wallets
  const wallets = useMemo(() => {
    console.log('🔧 Configuring Solana wallets for network:', currentNetwork);

    try {
      const walletAdapters = [
        new PhantomWalletAdapter(),
        new SolflareWalletAdapter({ network }),
        new TorusWalletAdapter(),
        new LedgerWalletAdapter(),
      ];

      // Only add WalletConnect if project ID is available
      const projectId = CLIENT_CONFIG.WALLETCONNECT_PROJECT_ID;
      if (projectId && projectId !== 'demo-project-id') {
        try {
          walletAdapters.push(
            new WalletConnectWalletAdapter({
              network,
              options: {
                projectId,
                metadata: {
                  name: CLIENT_CONFIG.APP_NAME,
                  description: 'Your Twitter Assistant with Solana Integration',
                  url: CLIENT_CONFIG.APP_URL,
                  icons: [`${CLIENT_CONFIG.APP_URL}/favicon.ico`],
                },
              },
            })
          );
          console.log('✅ WalletConnect adapter added successfully');
        } catch (wcError) {
          console.warn('⚠️ Failed to initialize WalletConnect adapter:', wcError);
        }
      } else {
        console.warn('⚠️ WalletConnect project ID not configured, skipping WalletConnect adapter');
      }

      return walletAdapters;
    } catch (error) {
      console.error('❌ Error configuring wallet adapters:', error);
      return [];
    }
  }, [network, currentNetwork]);

  console.log('🌐 Solana Web3Provider initialized:', {
    network: currentNetwork,
    endpoint,
    walletsCount: wallets.length,
  });

  return (
    <ConnectionProvider endpoint={endpoint}>
      <WalletProvider wallets={wallets} onError={onError} autoConnect>
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  );
}

// Main SolanaWalletProvider component that can be used without network context
export default function SolanaWalletProvider({ children }: SolanaWalletProviderProps) {
  return <SolanaWalletProviderInternal>{children}</SolanaWalletProviderInternal>;
}
