'use client';

import { useState, FormEvent } from 'react';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button'; // Assuming shadcn UI Button
import { Input } from '@/components/ui/input';   // Assuming shadcn UI Input
import { Label } from '@/components/ui/label';   // Assuming shadcn UI Label

type AuthFormMode = 'login' | 'signup';

interface AuthFormProps {
  mode: AuthFormMode;
}

export function AuthForm({ mode }: AuthFormProps) {
  const router = useRouter();
  const supabase = createSupabaseBrowserClient();

  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      if (mode === 'signup') {
        const { error: signUpError, data } = await supabase.auth.signUp({
          email,
          password,
          options: {
            emailRedirectTo: `${window.location.origin}/auth/callback?next=/dashboard`, // Redirect to dashboard after confirmation
          },
        });
        if (signUpError) throw signUpError;

        // If auto-confirm is enabled (development) or email verification is not required
        if (data?.user && data?.session) {
          router.push('/dashboard');
        } else {
          alert('Signup successful! Please check your email for confirmation if required.');
          router.push('/login');
        }
      } else {
        const { error: signInError } = await supabase.auth.signInWithPassword({
          email,
          password,
        });
        if (signInError) throw signInError;
        router.push('/dashboard'); // Redirect to dashboard after login
        router.refresh(); // Ensure layout re-renders and picks up new auth state
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred.';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  // Simple social login example (add more providers as needed)
  const handleOAuthLogin = async (provider: 'github' | 'google') => {
    setIsLoading(true);
    const { error: oauthError } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback?next=/dashboard`, // Redirect to dashboard after auth
      },
    });
    if (oauthError) {
      setError(oauthError.message);
      setIsLoading(false);
    }
    // Browser will redirect to provider, then to callback URL, then to dashboard
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4 w-full max-w-sm">
      <div>
        <Label htmlFor="email">Email</Label>
        <Input
          type="email"
          id="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          required
          placeholder="<EMAIL>"
        />
      </div>
      <div>
        <Label htmlFor="password">Password</Label>
        <Input
          type="password"
          id="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          required
          placeholder="••••••••"
        />
      </div>
      {error && <p className="text-red-500 text-sm">{error}</p>}
      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? 'Processing...' : mode === 'login' ? 'Log In' : 'Sign Up'}
      </Button>

      <div className="relative my-4">
        <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t"></span>
        </div>
        <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
            Or continue with
            </span>
        </div>
      </div>

      {/* Example for GitHub OAuth. Add more buttons for other providers. */}
      <Button
        variant="outline"
        type="button"
        onClick={() => handleOAuthLogin('github')}
        disabled={isLoading}
        className="w-full"
      >
        {/* Replace with an actual GitHub icon component if you have one */}
        {isLoading ? 'Processing...' : 'GitHub'}
      </Button>
       {/* Example for Google OAuth */}
      <Button
        variant="outline"
        type="button"
        onClick={() => handleOAuthLogin('google')}
        disabled={isLoading}
        className="w-full mt-2" // Added margin top for spacing
      >
        {/* Replace with an actual Google icon component if you have one */}
        {isLoading ? 'Processing...' : 'Google'}
      </Button>

      {mode === 'login' ? (
        <p className="text-center text-sm">
          Don&apos;t have an account?{' '}
          <a href="/signup" className="font-medium text-primary hover:underline">
            Sign up
          </a>
        </p>
      ) : (
        <p className="text-center text-sm">
          Already have an account?{' '}
          <a href="/login" className="font-medium text-primary hover:underline">
            Log in
          </a>
        </p>
      )}
    </form>
  );
}