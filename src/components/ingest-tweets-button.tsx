'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Download } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function IngestTweetsButton() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleIngestTweets = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);

      // Call the ingest API
      const response = await fetch('/api/twitter/ingest', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to ingest tweets');
      }

      const data = await response.json();

      // Check if any tweets were ingested
      const totalTweets = data.results.reduce((acc: number, result: { saved?: number }) => {
        return acc + (result.saved || 0);
      }, 0);

      if (totalTweets > 0) {
        setSuccess(`Successfully ingested ${totalTweets} tweets from ${data.results.length} accounts (limited to 10 most recent tweets per account).`);

        // Get the IDs of newly ingested tweets
        const tweetIds: number[] = [];

        // Fetch all tweets to get their IDs
        const tweetsResponse = await fetch('/api/twitter/tweets');
        if (tweetsResponse.ok) {
          const tweetsData = await tweetsResponse.json();

          // Get the most recent tweets (up to 10)
          const recentTweets = tweetsData.tweets
            .sort((a: { tweet_created_at: string }, b: { tweet_created_at: string }) =>
              new Date(b.tweet_created_at).getTime() - new Date(a.tweet_created_at).getTime())
            .slice(0, 10);

          recentTweets.forEach((tweet: { id: number }) => {
            tweetIds.push(tweet.id);
          });

          // If we have tweet IDs, analyze them
          if (tweetIds.length > 0) {
            try {
              setSuccess(`Ingested ${totalTweets} tweets (limited to 10 most recent per account). Analyzing ${tweetIds.length} most recent tweets...`);

              // Call the batch analyze API
              const analyzeResponse = await fetch('/api/ai/analyze-tweet', {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ tweet_ids: tweetIds }),
              });

              if (analyzeResponse.ok) {
                const analyzeData = await analyzeResponse.json();
                setSuccess(`Successfully ingested ${totalTweets} tweets (limited to 10 most recent per account) and analyzed ${analyzeData.processed} tweets.`);
              }
            } catch (analyzeError) {
              console.error('Error analyzing tweets:', analyzeError);
              // Don't fail the whole operation if analysis fails
              setSuccess(`Successfully ingested ${totalTweets} tweets (limited to 10 most recent per account), but automatic analysis failed.`);
            }
          }
        }
      } else {
        setSuccess('No new tweets found to ingest.');
      }

      // Refresh the page to show the new tweets
      router.refresh();
    } catch (err: unknown) {
      console.error('Error ingesting tweets:', err);
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <Button
        onClick={handleIngestTweets}
        disabled={isLoading}
        className="w-full"
      >
        {isLoading ? (
          <>
            <span className="animate-spin mr-2">⟳</span> Ingesting Tweets...
          </>
        ) : (
          <>
            <Download className="mr-2 h-4 w-4" /> Ingest Tweets
          </>
        )}
      </Button>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="bg-green-50 text-green-800 border-green-200">
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
