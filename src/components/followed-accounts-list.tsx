'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Trash2, Database, RefreshCw } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';

interface FollowedAccount {
  id: number;
  twitter_handle: string;
  twitter_user_id: string | null;
  created_at: string;
}

export default function FollowedAccountsList() {
  const router = useRouter();
  const [accounts, setAccounts] = useState<FollowedAccount[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch accounts on component mount
  useEffect(() => {
    fetchAccounts();
  }, []);

  const fetchAccounts = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/twitter/accounts');
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch accounts');
      }

      setAccounts(data.accounts || []);
    } catch (err: unknown) {
      // Check if the error is related to missing database tables
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      if (errorMessage && (
        errorMessage.includes('does not exist') ||
        errorMessage.includes('relation') ||
        errorMessage.includes('Failed to fetch accounts')
      )) {
        setError('Database tables not set up yet. Please set up your Supabase database first.');
      } else {
        setError(errorMessage);
      }
      setAccounts([]);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshAccounts = async () => {
    try {
      setIsRefreshing(true);
      await fetchAccounts();
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh accounts';
      setError(errorMessage);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleDelete = async (id: number) => {
    try {
      setDeletingId(id);

      const response = await fetch(`/api/twitter/accounts?id=${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete account');
      }

      // Remove the deleted account from the state
      setAccounts(accounts.filter(account => account.id !== id));
      router.refresh(); // Refresh the page
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
    } finally {
      setDeletingId(null);
    }
  };

  if (isLoading) {
    return <div className="text-center py-4">Loading accounts...</div>;
  }

  if (error && error.includes('Database tables not set up')) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Setup Required
          </CardTitle>
          <CardDescription>
            Your Supabase database tables need to be created before you can use this feature.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>

          <div className="rounded-md bg-muted p-4">
            <h3 className="font-medium mb-2">Next Steps:</h3>
            <ol className="list-decimal pl-5 space-y-2">
              <li>Create a Supabase project if you haven&apos;t already</li>
              <li>Run the database setup SQL script from the docs folder</li>
              <li>Configure your environment variables in .env</li>
              <li>Restart the application</li>
            </ol>
          </div>

          <Button onClick={refreshAccounts} disabled={isRefreshing}>
            {isRefreshing ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                Checking...
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Check Again
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  if (accounts.length === 0) {
    return (
      <div className="text-center py-4 text-muted-foreground">
        You are not following any Twitter accounts yet.
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Twitter Handle</TableHead>
          <TableHead>Added On</TableHead>
          <TableHead className="w-[100px]">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {accounts.map((account) => (
          <TableRow key={account.id}>
            <TableCell className="font-medium">
              <a
                href={`https://twitter.com/${account.twitter_handle}`}
                target="_blank"
                rel="noopener noreferrer"
                className="hover:underline"
              >
                @{account.twitter_handle}
              </a>
            </TableCell>
            <TableCell>
              {new Date(account.created_at).toLocaleDateString()}
            </TableCell>
            <TableCell>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => handleDelete(account.id)}
                disabled={deletingId === account.id}
              >
                <Trash2 className="h-4 w-4 text-red-500" />
                <span className="sr-only">Delete</span>
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
