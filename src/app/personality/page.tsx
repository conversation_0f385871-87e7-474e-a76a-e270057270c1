'use client';

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { PersonalityTraitsChart } from '@/components/personality/personality-traits-chart';
import { TopicsChart } from '@/components/personality/topics-chart';
import { WritingStyleChart } from '@/components/personality/writing-style-chart';
import { PersonalityInsights } from '@/components/personality/personality-insights';
import { PersonalityAnalysis } from '@/types/personality';
import { AlertCircle, RefreshCw, Twitter } from 'lucide-react';

export default function PersonalityPage() {
  const [twitterHandle, setTwitterHandle] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isFetching, setIsFetching] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [analysis, setAnalysis] = useState<PersonalityAnalysis | null>(null);
  const [jobProgress, setJobProgress] = useState(0);
  const [activeJobId, setActiveJobId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('traits');

  const fetchPersonalityResults = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/personality/results');

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch personality results');
      }

      const data = await response.json();
      setAnalysis(data.analysis);

      if (data.jobStatus && ['pending', 'in_progress'].includes(data.jobStatus)) {
        // There's an active job, start polling
        const jobsResponse = await fetch('/api/personality/job-status');
        if (jobsResponse.ok) {
          const jobsData = await jobsResponse.json();
          const latestJob = jobsData[0]; // Assuming jobs are sorted by creation date
          if (latestJob) {
            setActiveJobId(latestJob.id);
            setJobProgress(latestJob.progress);
          }
        }
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const checkJobStatus = useCallback(async (jobId: string) => {
    try {
      const response = await fetch(`/api/personality/job-status/${jobId}`);

      if (!response.ok) {
        return;
      }

      const data = await response.json();
      setJobProgress(data.progress);

      if (data.status === 'completed') {
        setActiveJobId(null);
        fetchPersonalityResults();
        if (isAnalyzing) setIsAnalyzing(false);
        if (isFetching) setIsFetching(false);
      } else if (data.status === 'failed') {
        setActiveJobId(null);
        setError(`Job failed: ${data.error || 'Unknown error'}`);
        if (isAnalyzing) setIsAnalyzing(false);
        if (isFetching) setIsFetching(false);
      }
    } catch (err) {
      console.error('Error checking job status:', err);
    }
  }, [isAnalyzing, isFetching, fetchPersonalityResults]);

  // Fetch initial data
  useEffect(() => {
    fetchPersonalityResults();
  }, [fetchPersonalityResults]);

  // Poll job status if there's an active job
  useEffect(() => {
    if (activeJobId) {
      const interval = setInterval(() => {
        checkJobStatus(activeJobId);
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [activeJobId, checkJobStatus]);

  const handleFetchTweets = async () => {
    try {
      setIsFetching(true);
      setError(null);

      if (!twitterHandle) {
        setError('Please enter your Twitter handle');
        setIsFetching(false);
        return;
      }

      const response = await fetch('/api/personality/fetch-tweets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ twitterHandle }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch tweets');
      }

      const data = await response.json();
      setActiveJobId(data.jobId);
      setJobProgress(0);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      setIsFetching(false);
    }
  };

  const handleAnalyzePersonality = async () => {
    try {
      setIsAnalyzing(true);
      setError(null);

      const response = await fetch('/api/personality/analyze', {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to analyze personality');
      }

      const data = await response.json();
      setActiveJobId(data.jobId);
      setJobProgress(0);
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
      setError(errorMessage);
      setIsAnalyzing(false);
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle>Personality Analysis</CardTitle>
            <CardDescription>Loading your personality profile...</CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center py-8">
            <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Your Personality Profile</h1>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {(isFetching || isAnalyzing || activeJobId) && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>{isFetching ? 'Fetching Tweets' : 'Analyzing Personality'}</CardTitle>
            <CardDescription>
              {isFetching
                ? 'We are fetching your tweets. This may take a few minutes.'
                : 'We are analyzing your personality based on your tweets. This may take a few minutes.'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Progress value={jobProgress} className="w-full" />
            <p className="text-sm text-muted-foreground mt-2">{jobProgress}% complete</p>
          </CardContent>
        </Card>
      )}

      {!analysis && !activeJobId && (
        <Card className="mb-6">
          <CardHeader>
            <CardTitle>Get Started</CardTitle>
            <CardDescription>
              To analyze your personality, we need to fetch your tweets first.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid w-full items-center gap-4">
              <div className="flex flex-col space-y-1.5">
                <Label htmlFor="twitter-handle">Twitter Handle</Label>
                <div className="flex gap-2">
                  <Input
                    id="twitter-handle"
                    placeholder="e.g. elonmusk"
                    value={twitterHandle}
                    onChange={(e) => setTwitterHandle(e.target.value)}
                  />
                  <Button onClick={handleFetchTweets} disabled={isFetching}>
                    {isFetching ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Fetching...
                      </>
                    ) : (
                      <>
                        <Twitter className="mr-2 h-4 w-4" />
                        Fetch Tweets
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {analysis && (
        <>
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Personality Analysis</CardTitle>
              <CardDescription>
                Based on {analysis.tweet_count} tweets analyzed on {new Date(analysis.analysis_date).toLocaleDateString()}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs value={activeTab} onValueChange={setActiveTab}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="traits">Personality Traits</TabsTrigger>
                  <TabsTrigger value="topics">Topics of Interest</TabsTrigger>
                  <TabsTrigger value="writing">Writing Style</TabsTrigger>
                </TabsList>
                <TabsContent value="traits" className="pt-4">
                  <PersonalityTraitsChart traits={analysis.traits} />
                </TabsContent>
                <TabsContent value="topics" className="pt-4">
                  <TopicsChart topics={analysis.topics} />
                </TabsContent>
                <TabsContent value="writing" className="pt-4">
                  <WritingStyleChart writingStyle={analysis.writing_style} />
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          <PersonalityInsights analysis={analysis} />

          <div className="flex justify-end mt-6">
            <Button
              variant="outline"
              onClick={handleAnalyzePersonality}
              disabled={isAnalyzing}
              className="mr-2"
            >
              {isAnalyzing ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                'Refresh Analysis'
              )}
            </Button>
          </div>
        </>
      )}
    </div>
  );
}
