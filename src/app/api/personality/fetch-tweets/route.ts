/**
 * API Route: /api/personality/fetch-tweets
 *
 * Initiates the process of fetching a user's tweets for personality analysis.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { fetchUserTweets } from '@/lib/twitter/user-tweets-service';

export async function POST(_request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { twitterHandle, count } = body;

    if (!twitterHandle) {
      return NextResponse.json(
        { error: 'Twitter handle is required' },
        { status: 400 }
      );
    }

    // Start the tweet fetching process
    const result = await fetchUserTweets(user.id, twitterHandle, count);

    return NextResponse.json(result);
  } catch (error: unknown) {
    console.error('Error fetching tweets:', error);

    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tweets';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
