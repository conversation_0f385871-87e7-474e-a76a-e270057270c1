/**
 * API Route: /api/personality/results
 * 
 * Retrieves the personality analysis results for the authenticated user.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { getUserPersonalityAnalysis } from '@/lib/ai/personality-analysis-service';
import { withErrorHandling } from '@/lib/utils/error-handler';
import { AuthenticationError, NotFoundError } from '@/lib/utils/errors';
import { createLogger } from '@/lib/utils/logger';

// Create a route-specific logger
const routeLogger = createLogger({ component: 'api-personality-results' });

/**
 * GET handler to retrieve personality analysis results
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Create a request-specific logger with a unique request ID
  const requestId = request.headers.get('x-request-id') || `req-${Date.now()}`;
  const logger = routeLogger.child({ 
    requestId,
    method: 'GET',
    path: '/api/personality/results'
  });
  
  logger.info('Received request to fetch personality analysis results');
  
  const supabase = await createSupabaseServerClient();
  
  // Get the authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();
  
  if (userError || !user) {
    logger.warn('Authentication failed', { error: userError });
    throw new AuthenticationError('You must be logged in to view personality analysis results');
  }
  
  logger.info('Fetching personality analysis results', { userId: user.id });
  
  // Get the personality analysis results
  const result = await getUserPersonalityAnalysis(user.id);
  
  if (!result || !result.analysis) {
    logger.info('No personality analysis results found', { userId: user.id });
    throw new NotFoundError(
      'No personality analysis results found. Please run an analysis first.',
      'PERSONALITY_ANALYSIS_NOT_FOUND'
    );
  }
  
  logger.info('Personality analysis results retrieved', { 
    userId: user.id
  });
  
  return NextResponse.json(result);
});
