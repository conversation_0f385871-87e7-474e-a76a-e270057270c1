/**
 * API Route: /api/personality/results
 *
 * Retrieves the personality analysis results for the authenticated user.
 */

import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { getUserPersonalityAnalysis } from '@/lib/ai/personality-analysis-service';

export async function GET() {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the personality analysis results
    const result = await getUserPersonalityAnalysis(user.id);

    return NextResponse.json(result);
  } catch (error: unknown) {
    console.error('Error fetching personality analysis results:', error);

    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch personality analysis results';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
