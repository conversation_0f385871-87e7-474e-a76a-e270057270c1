/**
 * API Route: /api/personality/analyze
 *
 * Initiates the process of analyzing a user's personality based on their tweets.
 */

import { NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { analyzeUserPersonality } from '@/lib/ai/personality-analysis-service';

export async function POST() {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Start the personality analysis process
    const result = await analyzeUserPersonality(user.id);

    return NextResponse.json(result);
  } catch (error: unknown) {
    console.error('Error analyzing personality:', error);

    const errorMessage = error instanceof Error ? error.message : 'Failed to analyze personality';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
