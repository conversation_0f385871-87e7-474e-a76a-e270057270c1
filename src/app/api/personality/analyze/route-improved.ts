/**
 * API Route: /api/personality/analyze
 *
 * Initiates the process of analyzing a user's personality based on their tweets.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { analyzeUserPersonality } from '@/lib/ai/personality-analysis-service';
import { withErrorHandling } from '@/lib/utils/error-handler';
import { AuthenticationError } from '@/lib/utils/errors';
import { createLogger } from '@/lib/utils/logger';
import { validateData, schemas } from '@/lib/utils/validation';

// Create a route-specific logger
const routeLogger = createLogger({ component: 'api-personality-analyze' });

/**
 * POST handler to initiate personality analysis
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  // Create a request-specific logger with a unique request ID
  const requestId = request.headers.get('x-request-id') || `req-${Date.now()}`;
  const logger = routeLogger.child({
    requestId,
    method: 'POST',
    path: '/api/personality/analyze'
  });

  logger.info('Received request to analyze personality');

  const supabase = await createSupabaseServerClient();

  // Get the authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    logger.warn('Authentication failed', { error: userError });
    throw new AuthenticationError('You must be logged in to analyze personality');
  }

  // Get and validate request body (optional parameters)
  let minTweets = 10; // Default minimum tweets required

  try {
    const body = await request.json();
    if (body) {
      const validatedBody = validateData(
        schemas.personalityAnalysis,
        body,
        'Invalid personality analysis request'
      );

      if (validatedBody.min_tweets) {
        minTweets = validatedBody.min_tweets;
      }
    }
  } catch (error) {
    // If body parsing fails, continue with defaults
    logger.debug('No request body or invalid body, using defaults', { error });
  }

  logger.info('Starting personality analysis', {
    userId: user.id,
    minTweets
  });

  // Start the personality analysis process
  const result = await analyzeUserPersonality(user.id);

  logger.info('Personality analysis initiated', { 
    userId: user.id,
    jobId: result.jobId
  });

  return NextResponse.json(result);
});
