/**
 * API Route: /api/personality/job-status/[jobId]
 *
 * Retrieves the status of a personality analysis or tweet fetching job.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { getAnalysisJobStatus } from '@/lib/ai/personality-analysis-service';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { jobId } = await params;

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      );
    }

    // Get the job status
    const result = await getAnalysisJobStatus(jobId);

    return NextResponse.json(result);
  } catch (error: unknown) {
    console.error('Error fetching job status:', error);

    const errorMessage = error instanceof Error ? error.message : 'Failed to fetch job status';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
