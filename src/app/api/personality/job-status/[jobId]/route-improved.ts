/**
 * API Route: /api/personality/job-status/[jobId]
 * 
 * Retrieves the status of a personality analysis or tweet fetching job.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { getAnalysisJobStatus } from '@/lib/ai/personality-analysis-service';
import { handleApiError } from '@/lib/utils/error-handler';
import { AuthenticationError, ValidationError, NotFoundError } from '@/lib/utils/errors';
import { createLogger } from '@/lib/utils/logger';

// Create a route-specific logger
const routeLogger = createLogger({ component: 'api-job-status' });

/**
 * GET handler to retrieve job status
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  const requestLogger = routeLogger.child({
    requestId: request.headers.get('x-request-id') || `job-status-${Date.now()}`,
    endpoint: '/api/personality/job-status/[jobId]',
    method: 'GET'
  });

  try {
    requestLogger.info('Fetching job status');

    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      const error = new AuthenticationError('User not authenticated');
      requestLogger.warn('Unauthorized access attempt', error);
      return handleApiError(error, request);
    }

    const { jobId } = await params;

    if (!jobId) {
      requestLogger.warn('Missing job ID in request');
      throw new ValidationError('Job ID is required');
    }

    requestLogger.info('Fetching job status', { jobId, userId: user.id });

    // Get the job status
    const result = await getAnalysisJobStatus(jobId);

    if (!result) {
      requestLogger.warn('Job not found', { jobId });
      throw new NotFoundError('Job not found', 'JOB_NOT_FOUND', { jobId });
    }

    requestLogger.info('Job status retrieved', { 
      jobId,
      status: result.status,
      progress: result.progress
    });

    return NextResponse.json(result);
  } catch (error) {
    return handleApiError(error, request);
  }
}
