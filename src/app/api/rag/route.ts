import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { ensureProfile } from '@/lib/utils/ensure-profile';

// POST handler to generate a response to a tweet
export async function POST(_request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure profile exists
    await ensureProfile(supabase, user.id);

    // Get request body
    const body = await request.json();
    const { tweet_id } = body;

    if (!tweet_id) {
      return NextResponse.json(
        { error: 'Tweet ID is required' },
        { status: 400 }
      );
    }

    // Fetch the tweet
    const { data: tweet, error: tweetError } = await supabase
      .from('buddychip_tweets')
      .select('*')
      .eq('id', tweet_id)
      .eq('user_id', user.id)
      .single();

    if (tweetError || !tweet) {
      return NextResponse.json(
        { error: 'Tweet not found or not authorized' },
        { status: 404 }
      );
    }

    // In a real implementation, you would:
    // 1. Retrieve relevant context from Mem0 or another vector database
    // 2. Call an LLM API with the tweet content and context to generate a response

    // For now, we'll generate a mock response
    const response = generateMockResponse(tweet);

    return NextResponse.json({
      tweet_id: tweet.id,
      generated_response: response
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// Helper function to generate a mock response
function generateMockResponse(tweet: { content: string }) {
  // Extract the first few words from the tweet to personalize the response
  const words = tweet.content.split(' ');
  const firstFewWords = words.slice(0, Math.min(3, words.length)).join(' ');

  // Generate a random response type
  const responseTypes = [
    `Thank you for your tweet about "${firstFewWords}...". I appreciate your feedback!`,
    `I saw your message regarding "${firstFewWords}...". Let me look into this for you.`,
    `Thanks for reaching out about "${firstFewWords}...". I'd be happy to help with this.`,
    `I noticed your tweet about "${firstFewWords}...". That's an interesting point!`,
    `Regarding your tweet about "${firstFewWords}...", I wanted to follow up with you.`
  ];

  const randomIndex = Math.floor(Math.random() * responseTypes.length);
  return responseTypes[randomIndex];
}

// GET handler to retrieve context for a tweet
export async function GET(_request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure profile exists
    await ensureProfile(supabase, user.id);

    // Get the tweet ID from the URL
    const url = new URL(request.url);
    const tweetId = url.searchParams.get('tweet_id');

    if (!tweetId) {
      return NextResponse.json(
        { error: 'Tweet ID is required' },
        { status: 400 }
      );
    }

    // Fetch the tweet
    const { data: tweet, error: tweetError } = await supabase
      .from('buddychip_tweets')
      .select('*')
      .eq('id', tweetId)
      .eq('user_id', user.id)
      .single();

    if (tweetError || !tweet) {
      return NextResponse.json(
        { error: 'Tweet not found or not authorized' },
        { status: 404 }
      );
    }

    // In a real implementation, you would:
    // 1. Extract key entities or topics from the tweet
    // 2. Query Mem0 or another vector database for relevant context

    // For now, we'll return mock context
    const context = {
      tweet_id: tweet.id,
      author: tweet.author_handle,
      related_tweets: generateMockRelatedTweets(tweet),
      suggested_topics: generateMockTopics(tweet)
    };

    return NextResponse.json(context);
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// Helper function to generate mock related tweets
function generateMockRelatedTweets(tweet: { id: string; author_handle: string }) {
  // In a real implementation, these would be actual related tweets from the database
  return [
    {
      id: `mock-related-1-${tweet.id}`,
      content: `This is a related tweet to the topic in ${tweet.author_handle}'s message.`,
      author: 'related_user1',
      created_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
    },
    {
      id: `mock-related-2-${tweet.id}`,
      content: `Another tweet that provides context for ${tweet.author_handle}'s question.`,
      author: 'related_user2',
      created_at: new Date(Date.now() - 172800000).toISOString() // 2 days ago
    }
  ];
}

// Helper function to generate mock topics
function generateMockTopics(tweet: { content: string }) {
  // Extract potential topics from the tweet content
  const content = tweet.content.toLowerCase();
  const topics = [];

  if (content.includes('help') || content.includes('support')) {
    topics.push('Customer Support');
  }

  if (content.includes('buy') || content.includes('price') || content.includes('cost')) {
    topics.push('Sales Inquiry');
  }

  if (content.includes('bug') || content.includes('error') || content.includes('issue')) {
    topics.push('Technical Issue');
  }

  if (content.includes('thank') || content.includes('great') || content.includes('love')) {
    topics.push('Positive Feedback');
  }

  // Add some default topics if none were detected
  if (topics.length === 0) {
    topics.push('General Inquiry', 'Product Feedback');
  }

  return topics;
}