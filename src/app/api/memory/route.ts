import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { memoryService } from '@/lib/memory/mem0-service';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';

const memoryLogger = logger.child({ component: 'MemoryAPI' });

// Request validation schemas
const addMemorySchema = z.object({
  messages: z.array(z.object({
    role: z.enum(['user', 'assistant']),
    content: z.string().min(1),
  })),
  metadata: z.record(z.any()).optional(),
  sessionId: z.string().optional(),
});



const updateMemorySchema = z.object({
  memoryId: z.string().min(1),
  content: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

// POST /api/memory - Add memories
export async function POST(_request: NextRequest) {
  const requestId = `memory-add-${Date.now()}`;
  memoryLogger.info('Memory add request received', { requestId });

  try {
    // Parse and validate request body
    const body = await request.json();
    const { messages, metadata, sessionId } = addMemorySchema.parse(body);

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      memoryLogger.warn('Unauthorized memory add attempt', { requestId });
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    memoryLogger.info('Processing memory add request', {
      userId: user.id,
      messageCount: messages.length,
      sessionId,
      requestId
    });

    // Add memories using the memory service
    const memoryIds = await memoryService.add(messages, {
      userId: user.id,
      metadata,
      sessionId,
    });

    memoryLogger.info('Memories added successfully', {
      userId: user.id,
      memoriesCreated: memoryIds.length,
      requestId
    });

    return NextResponse.json({
      success: true,
      memoryIds,
      count: memoryIds.length,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      memoryLogger.warn('Invalid request data', { error: error.errors, requestId });
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    memoryLogger.error('Unexpected error in memory add', { error, requestId });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/memory - Get all memories or search
export async function GET(_request: NextRequest) {
  const requestId = `memory-get-${Date.now()}`;
  const { searchParams } = new URL(request.url);

  const query = searchParams.get('query');
  const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 50;
  const type = searchParams.get('type');

  memoryLogger.info('Memory get request received', { requestId, query, limit, type });

  try {
    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      memoryLogger.warn('Unauthorized memory get attempt', { requestId });
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    let memories;

    if (query) {
      // Search memories
      memoryLogger.info('Searching memories', { userId: user.id, query, requestId });

      const metadata = type ? { type } : undefined;
      memories = await memoryService.search(query, {
        userId: user.id,
        limit,
        metadata,
      });
    } else {
      // Get all memories
      memoryLogger.info('Getting all memories', { userId: user.id, limit, requestId });
      memories = await memoryService.getAll(user.id, limit);
    }

    memoryLogger.info('Memory retrieval completed', {
      userId: user.id,
      memoriesCount: memories.length,
      requestId
    });

    return NextResponse.json({
      memories,
      count: memories.length,
      query: query || null,
    });

  } catch (error) {
    memoryLogger.error('Unexpected error in memory get', { error, requestId });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/memory - Update memory
export async function PUT(_request: NextRequest) {
  const requestId = `memory-update-${Date.now()}`;
  memoryLogger.info('Memory update request received', { requestId });

  try {
    // Parse and validate request body
    const body = await request.json();
    const { memoryId, content, metadata } = updateMemorySchema.parse(body);

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      memoryLogger.warn('Unauthorized memory update attempt', { requestId });
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    memoryLogger.info('Processing memory update request', {
      userId: user.id,
      memoryId,
      requestId
    });

    // Update memory using the memory service
    const success = await memoryService.update(memoryId, user.id, {
      content,
      metadata,
    });

    if (!success) {
      memoryLogger.warn('Memory update failed', {
        userId: user.id,
        memoryId,
        requestId
      });
      return NextResponse.json(
        { error: 'Memory not found or update failed' },
        { status: 404 }
      );
    }

    memoryLogger.info('Memory updated successfully', {
      userId: user.id,
      memoryId,
      requestId
    });

    return NextResponse.json({
      success: true,
      memoryId,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      memoryLogger.warn('Invalid request data', { error: error.errors, requestId });
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    memoryLogger.error('Unexpected error in memory update', { error, requestId });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/memory - Delete memory
export async function DELETE(_request: NextRequest) {
  const requestId = `memory-delete-${Date.now()}`;
  const { searchParams } = new URL(request.url);
  const memoryId = searchParams.get('memoryId');

  memoryLogger.info('Memory delete request received', { requestId, memoryId });

  try {
    if (!memoryId) {
      return NextResponse.json(
        { error: 'Memory ID is required' },
        { status: 400 }
      );
    }

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      memoryLogger.warn('Unauthorized memory delete attempt', { requestId });
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    memoryLogger.info('Processing memory delete request', {
      userId: user.id,
      memoryId,
      requestId
    });

    // Delete memory using the memory service
    const success = await memoryService.delete(memoryId, user.id);

    if (!success) {
      memoryLogger.warn('Memory delete failed', {
        userId: user.id,
        memoryId,
        requestId
      });
      return NextResponse.json(
        { error: 'Memory not found or delete failed' },
        { status: 404 }
      );
    }

    memoryLogger.info('Memory deleted successfully', {
      userId: user.id,
      memoryId,
      requestId
    });

    return NextResponse.json({
      success: true,
      memoryId,
    });

  } catch (error) {
    memoryLogger.error('Unexpected error in memory delete', { error, requestId });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
