/**
 * API Route: /api/ai/generate-reply
 *
 * <PERSON><PERSON> generating reply suggestions for tweets.
 */

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { generateReplySuggestions } from '@/lib/ai/openrouter-service';
import { ensureProfile } from '@/lib/utils/ensure-profile';
import { withErrorHandling } from '@/lib/utils/error-handler';
import { AuthenticationError, NotFoundError, DatabaseError } from '@/lib/utils/errors';
import { createLogger } from '@/lib/utils/logger';
import { validateData, schemas } from '@/lib/utils/validation';

// Create a route-specific logger
const routeLogger = createLogger({ component: 'api-generate-reply' });

/**
 * POST handler to generate reply suggestions for a tweet
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  // Create a request-specific logger with a unique request ID
  const requestId = request.headers.get('x-request-id') || `req-${Date.now()}`;
  const logger = routeLogger.child({
    requestId,
    method: 'POST',
    path: '/api/ai/generate-reply'
  });

  logger.info('Received request to generate reply suggestions');

  const supabase = await createSupabaseServerClient();

  // Get the authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    logger.warn('Authentication failed', { error: userError });
    throw new AuthenticationError('You must be logged in to generate reply suggestions');
  }

  // Ensure profile exists
  await ensureProfile(supabase, user.id);

  // Get and validate request body
  const body = await request.json();
  const { tweet_id, style } = validateData(
    schemas.replyGeneration,
    body,
    'Invalid reply generation request'
  );

  logger.info('Generating reply suggestions', {
    tweet_id,
    userId: user.id,
    hasCustomStyle: !!style
  });

  // Fetch the tweet
  const { data: tweet, error: tweetError } = await supabase
    .from('buddychip_tweets')
    .select('*')
    .eq('id', tweet_id)
    .eq('user_id', user.id)
    .single();

  if (tweetError || !tweet) {
    logger.warn('Tweet not found', { tweet_id, error: tweetError });
    throw new NotFoundError('Tweet not found or not authorized', 'TWEET_NOT_FOUND', { tweet_id });
  }

  // Fetch user preferences
  const { data: userPreferences, error: preferencesError } = await supabase
    .from('buddychip_user_preferences')
    .select('reply_style, system_prompt')
    .eq('user_id', user.id)
    .single();

  if (preferencesError) {
    logger.warn('Error fetching user preferences', { error: preferencesError });
    // Continue with default preferences
  }

  // Use custom style if provided, otherwise use user preferences or default
  const replyStyle = style ||
    (userPreferences?.reply_style || 'Professional and friendly');

  logger.debug('Using reply style', { replyStyle });

  // Generate reply suggestions
  const suggestions = await generateReplySuggestions(
    tweet.content,
    tweet.author_handle,
    replyStyle,
    userPreferences?.system_prompt
  );

  logger.info('Reply suggestions generated', {
    tweet_id,
    suggestionCount: suggestions.length
  });

  // Store the suggestions in the database
  const { error: updateError } = await supabase
    .from('buddychip_tweets')
    .update({
      ai_analysis: {
        ...tweet.ai_analysis,
        reply_suggestions: suggestions,
        last_generated_at: new Date().toISOString()
      }
    })
    .eq('id', tweet_id)
    .eq('user_id', user.id);

  if (updateError) {
    logger.error('Error updating tweet with reply suggestions', updateError);
    throw new DatabaseError(
      'Failed to save reply suggestions',
      'SAVE_SUGGESTIONS_FAILED',
      { tweet_id, updateError }
    );
  }

  return NextResponse.json({
    tweet_id,
    suggestions
  });
});
