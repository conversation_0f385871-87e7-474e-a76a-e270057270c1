import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { generateReplySuggestions } from '@/lib/ai/openrouter-service';
import { ensureProfile } from '@/lib/utils/ensure-profile';

/**
 * POST handler to generate reply suggestions for a tweet
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure profile exists
    await ensureProfile(supabase, user.id);

    // Get request body
    const body = await request.json();
    const { tweet_id, custom_style } = body;

    if (!tweet_id) {
      return NextResponse.json(
        { error: 'Tweet ID is required' },
        { status: 400 }
      );
    }

    // Fetch the tweet
    const { data: tweet, error: tweetError } = await supabase
      .from('buddychip_tweets')
      .select('*')
      .eq('id', tweet_id)
      .eq('user_id', user.id)
      .single();

    if (tweetError || !tweet) {
      return NextResponse.json(
        { error: 'Tweet not found' },
        { status: 404 }
      );
    }

    // Fetch user preferences
    const { data: userPreferences } = await supabase
      .from('buddychip_user_preferences')
      .select('reply_style, system_prompt')
      .eq('user_id', user.id)
      .single();

    // Use custom style if provided, otherwise use user preferences or default
    const replyStyle = custom_style ||
      (userPreferences?.reply_style || 'Professional and friendly');

    // Generate reply suggestions
    const suggestions = await generateReplySuggestions(
      tweet.content,
      tweet.author_handle,
      replyStyle,
      userPreferences?.system_prompt
    );

    // Store the suggestions in the database
    const { error: updateError } = await supabase
      .from('buddychip_tweets')
      .update({
        ai_analysis: {
          ...tweet.ai_analysis,
          reply_suggestions: suggestions,
          last_generated_at: new Date().toISOString()
        }
      })
      .eq('id', tweet_id)
      .eq('user_id', user.id);

    if (updateError) {
      console.error('Error updating tweet with reply suggestions:', updateError);
    }

    return NextResponse.json({
      tweet_id,
      suggestions
    });
  } catch (error) {
    console.error('Error generating reply suggestions:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
