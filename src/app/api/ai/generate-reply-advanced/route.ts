import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { generateAdvancedReply } from '@/lib/ai/agents/reply-agent';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';

const advancedReplyLogger = logger.child({ component: 'AdvancedReplyAPI' });

// Request validation schema
const generateReplySchema = z.object({
  tweet_id: z.string().min(1),
  custom_style: z.string().optional(),
  reply_types: z.array(z.enum(['supportive', 'questioning', 'informative', 'humorous'])).optional(),
});

export async function POST(request: NextRequest) {
  const requestId = `advanced-reply-${Date.now()}`;
  advancedReplyLogger.info('Advanced reply generation request received', { requestId });

  try {
    // Parse and validate request body
    const body = await request.json();
    const { tweet_id } = generateReplySchema.parse(body);

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      advancedReplyLogger.warn('Unauthorized advanced reply attempt', { requestId });
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    advancedReplyLogger.info('Processing advanced reply request', {
      userId: user.id,
      tweetId: tweet_id,
      requestId
    });

    // Fetch the tweet from the database
    const { data: tweet, error: tweetError } = await supabase
      .from('buddychip_tweets')
      .select('*')
      .eq('id', tweet_id)
      .eq('user_id', user.id)
      .single();

    if (tweetError || !tweet) {
      advancedReplyLogger.warn('Tweet not found', {
        userId: user.id,
        tweetId: tweet_id,
        requestId
      });
      return NextResponse.json(
        { error: 'Tweet not found' },
        { status: 404 }
      );
    }

    // Extract metrics from the raw tweet data
    const metrics = tweet.raw_data?.public_metrics || {
      retweet_count: 0,
      reply_count: 0,
      like_count: 0,
      quote_count: 0
    };

    const tweetMetrics = {
      likes: metrics.like_count || 0,
      retweets: metrics.retweet_count || 0,
      replies: metrics.reply_count || 0,
    };

    // Generate advanced reply using AI SDK agents
    const result = await generateAdvancedReply(
      tweet.content,
      tweet.author_handle,
      tweetMetrics,
      user.id,
      requestId
    );

    // Store the advanced suggestions in the database
    const advancedAnalysis = {
      ...tweet.ai_analysis,
      advanced_reply_suggestions: result.suggestions,
      advanced_analysis: result.analysis,
      last_advanced_generated_at: new Date().toISOString(),
      generation_method: 'ai_sdk_agents',
    };

    const { error: updateError } = await supabase
      .from('buddychip_tweets')
      .update({
        ai_analysis: advancedAnalysis
      })
      .eq('id', tweet_id)
      .eq('user_id', user.id);

    if (updateError) {
      advancedReplyLogger.error('Error updating tweet with advanced suggestions', {
        userId: user.id,
        tweetId: tweet_id,
        updateError,
        requestId
      });
    }

    advancedReplyLogger.info('Advanced reply generation completed successfully', {
      userId: user.id,
      tweetId: tweet_id,
      suggestionCount: result.suggestions.length,
      requestId
    });

    return NextResponse.json({
      tweet_id,
      suggestions: result.suggestions,
      analysis: result.analysis,
      generation_method: 'ai_sdk_agents',
      tools_used: result.analysis.toolsUsed,
      processing_steps: result.analysis.processingSteps,
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      advancedReplyLogger.warn('Invalid request data', { error: error.errors, requestId });
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    advancedReplyLogger.error('Unexpected error in advanced reply generation', { error, requestId });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get tweets with advanced reply suggestions
    const { data: tweets, error: tweetsError } = await supabase
      .from('buddychip_tweets')
      .select('id, content, author_handle, ai_analysis')
      .eq('user_id', user.id)
      .not('ai_analysis->advanced_reply_suggestions', 'is', null)
      .order('created_at', { ascending: false })
      .limit(20);

    if (tweetsError) {
      advancedReplyLogger.error('Error fetching tweets with advanced suggestions', {
        userId: user.id,
        tweetsError
      });
      return NextResponse.json(
        { error: 'Failed to fetch tweets' },
        { status: 500 }
      );
    }

    const formattedTweets = tweets?.map(tweet => ({
      id: tweet.id,
      content: tweet.content,
      author_handle: tweet.author_handle,
      advanced_suggestions: tweet.ai_analysis?.advanced_reply_suggestions || [],
      analysis: tweet.ai_analysis?.advanced_analysis || {},
      generated_at: tweet.ai_analysis?.last_advanced_generated_at,
    })) || [];

    return NextResponse.json({
      tweets: formattedTweets,
      total: formattedTweets.length,
    });

  } catch (error) {
    advancedReplyLogger.error('Error in GET /api/ai/generate-reply-advanced', { error });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
