/**
 * API Route: /api/ai/analyze-tweet
 *
 * Handles tweet analysis requests, both for single tweets and batches.
 */

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { analyzeTweet, batchAnalyzeTweets } from '@/lib/ai/tweet-analysis-service';
import { ensureProfile } from '@/lib/utils/ensure-profile';
import { withErrorHandling } from '@/lib/utils/error-handler';
import { AuthenticationError } from '@/lib/utils/errors';
import { createLogger } from '@/lib/utils/logger';
import { validateData, schemas } from '@/lib/utils/validation';
import { ValidationError } from '@/lib/utils/errors';

// Create a route-specific logger
const routeLogger = createLogger({ component: 'api-analyze-tweet' });

/**
 * POST handler to analyze a single tweet
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  // Create a request-specific logger with a unique request ID
  const requestId = request.headers.get('x-request-id') || `req-${Date.now()}`;
  const logger = routeLogger.child({
    requestId,
    method: 'POST',
    path: '/api/ai/analyze-tweet'
  });

  logger.info('Received request to analyze tweet');

  const supabase = await createSupabaseServerClient();

  // Get the authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    logger.warn('Authentication failed', { error: userError });
    throw new AuthenticationError('You must be logged in to analyze tweets');
  }

  // Ensure profile exists
  await ensureProfile(supabase, user.id);

  // Get and validate request body
  const body = await request.json();
  const { tweet_id } = validateData(
    schemas.tweetId,
    body,
    'Invalid tweet analysis request'
  );

  logger.info('Analyzing tweet', { tweet_id, userId: user.id });

  // Convert tweet_id to number and analyze the tweet
  const tweetIdNumber = parseInt(tweet_id, 10);
  if (isNaN(tweetIdNumber)) {
    throw new ValidationError('Invalid tweet ID format', 'INVALID_TWEET_ID');
  }

  const result = await analyzeTweet(tweetIdNumber, user.id);

  logger.info('Tweet analysis completed', {
    tweet_id,
    worthReplying: result.isWorthReplying,
    score: result.relevanceScore
  });

  return NextResponse.json(result);
});

/**
 * PUT handler to batch analyze multiple tweets
 */
export const PUT = withErrorHandling(async (request: NextRequest) => {
  // Create a request-specific logger with a unique request ID
  const requestId = request.headers.get('x-request-id') || `req-${Date.now()}`;
  const logger = routeLogger.child({
    requestId,
    method: 'PUT',
    path: '/api/ai/analyze-tweet'
  });

  logger.info('Received request to batch analyze tweets');

  const supabase = await createSupabaseServerClient();

  // Get the authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    logger.warn('Authentication failed', { error: userError });
    throw new AuthenticationError('You must be logged in to analyze tweets');
  }

  // Ensure profile exists
  await ensureProfile(supabase, user.id);

  // Get and validate request body
  const body = await request.json();
  const { tweet_ids, analyze_all = false } = validateData(
    schemas.tweetIds,
    body,
    'Invalid batch analysis request'
  );

  // Limit the number of tweets to analyze in a single request
  const MAX_BATCH_SIZE = 10;
  const limitedIds = tweet_ids.slice(0, MAX_BATCH_SIZE);

  if (tweet_ids.length > MAX_BATCH_SIZE) {
    logger.info(`Batch size limited from ${tweet_ids.length} to ${MAX_BATCH_SIZE}`);
  }

  logger.info('Batch analyzing tweets', {
    count: limitedIds.length,
    totalRequested: tweet_ids.length,
    userId: user.id,
    analyzeAll: analyze_all
  });

  // Convert tweet IDs to numbers (handle both string and number inputs)
  const tweetIdNumbers = limitedIds.map(id => {
    // If it's already a number, use it directly
    if (typeof id === 'number') {
      return id;
    }
    // If it's a string, parse it
    const num = parseInt(id, 10);
    if (isNaN(num)) {
      throw new ValidationError(`Invalid tweet ID format: ${id}`, 'INVALID_TWEET_ID');
    }
    return num;
  });

  // Batch analyze the tweets
  const results = await batchAnalyzeTweets(tweetIdNumbers, user.id);

  logger.info('Batch analysis completed', {
    processed: results.length,
    worthReplyingCount: results.filter(r => r.isWorthReplying).length
  });

  return NextResponse.json({
    processed: results.length,
    total_requested: tweet_ids.length,
    results,
    has_more: tweet_ids.length > MAX_BATCH_SIZE
  });
});
