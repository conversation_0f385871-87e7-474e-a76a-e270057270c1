/**
 * API Route: /api/ai/generate-image
 *
 * Handles image generation requests using OpenAI's new Responses API with gpt-image-1 model.
 * Supports both standalone image generation and context-aware generation
 * for tweets, conversations, and other content.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { generateImage, generateImageWithContext, ImageGenerationOptions } from '@/lib/ai/image-generation';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';

const imageApiLogger = logger.child({ component: 'ImageGenerationAPI' });

// Request validation schema
const imageGenerationRequestSchema = z.object({
  prompt: z.string().min(1, 'Prompt is required').max(1000, 'Prompt too long'),
  context: z.object({
    type: z.enum(['general', 'tweet', 'conversation', 'copy-ai', 'reply']),
    content: z.string().optional(),
    userQuery: z.string().optional(),
    tweetId: z.string().optional(),
    sessionId: z.string().optional(),
  }).optional(),
  options: z.object({
    size: z.enum(['1024x1024', '1024x1536', '1536x1024', 'auto']).optional(),
    quality: z.enum(['low', 'medium', 'high', 'auto']).optional(),
    format: z.enum(['png', 'jpeg', 'webp']).optional(),
    compression: z.number().min(0).max(100).optional(),
    background: z.enum(['transparent', 'opaque', 'auto']).optional(),
    previousResponseId: z.string().optional(),
    previousImageId: z.string().optional(),
    enhancePrompt: z.boolean().optional(),
    enhancementOptions: z.object({
      contextType: z.enum(['general', 'tweet', 'conversation', 'copy-ai', 'reply']).optional(),
      contextContent: z.string().optional(),
      style: z.enum(['photorealistic', 'artistic', 'cartoon', 'minimalist', 'vibrant', 'professional', 'creative']).optional(),
      mood: z.enum(['energetic', 'calm', 'dramatic', 'playful', 'serious', 'inspiring']).optional(),
      targetAudience: z.enum(['general', 'professional', 'social_media', 'marketing', 'personal']).optional(),
    }).optional(),
  }).optional(),
  saveToDatabase: z.boolean().default(false), // Never save to database by default
});

export async function POST(_request: NextRequest) {
  const requestId = `img-api-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  const routeLogger = imageApiLogger.child({ requestId });

  try {
    routeLogger.info('Image generation API request received');

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      routeLogger.warn('Unauthorized image generation request');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = imageGenerationRequestSchema.safeParse(body);

    if (!validationResult.success) {
      routeLogger.warn('Invalid image generation request', {
        errors: validationResult.error.errors,
        body
      });
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const {
      prompt,
      context,
      options = {},
      saveToDatabase
    } = validationResult.data;

    routeLogger.info('Processing image generation request', {
      userId: user.id,
      prompt: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : ''),
      contextType: context?.type,
      hasContext: !!context?.content,
      saveToDatabase
    });

    // Check rate limiting (optional - implement based on your needs)
    const { data: recentImages, error: imageError } = await supabase
      .from('buddychip_search_history')
      .select('created_at')
      .eq('user_id', user.id)
      .eq('analysis->>type', 'image_generation')
      .gte('created_at', new Date(Date.now() - 300000).toISOString()) // Last 5 minutes
      .order('created_at', { ascending: false });

    if (imageError) {
      routeLogger.warn('Failed to check image generation rate limit', { userId: user.id, imageError });
    } else if (recentImages && recentImages.length >= 10) {
      routeLogger.warn('Image generation rate limit exceeded', { userId: user.id, recentImageCount: recentImages.length });
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please wait before generating another image.' },
        { status: 429 }
      );
    }

    // Prepare generation options
    const generationOptions: ImageGenerationOptions = {
      size: options.size || 'auto',
      quality: options.quality || 'auto',
      format: options.format,
      compression: options.compression,
      background: options.background || 'auto',
      previousResponseId: options.previousResponseId,
      previousImageId: options.previousImageId,
      enhancePrompt: options.enhancePrompt !== false, // Default to true
      enhancementOptions: options.enhancementOptions,
    };

    // Generate the image
    const startTime = Date.now();
    let result;

    if (context && context.type !== 'general' && context.content) {
      result = await generateImageWithContext(
        context.userQuery || prompt,
        {
          type: context.type as 'tweet' | 'conversation' | 'copy-ai' | 'reply',
          content: context.content,
          metadata: {
            tweetId: context.tweetId,
            sessionId: context.sessionId,
          },
        },
        generationOptions
      );
    } else {
      result = await generateImage(prompt, generationOptions);
    }

    const executionTime = Date.now() - startTime;

    if (!result.success) {
      routeLogger.error('Image generation failed', {
        userId: user.id,
        prompt: prompt.substring(0, 100) + (prompt.length > 100 ? '...' : ''),
        error: result.error,
        executionTime
      });

      return NextResponse.json(
        {
          error: 'Image generation failed',
          details: result.error,
          requestId
        },
        { status: 500 }
      );
    }

    // Note: Images are not saved to database by default for privacy and storage reasons
    // Users should save images to clipboard or download them directly
    if (saveToDatabase) {
      routeLogger.warn('Database saving requested but not recommended for images', {
        userId: user.id,
        imageId: result.imageId
      });

      try {
        const { error: historyError } = await supabase
          .from('buddychip_search_history')
          .insert({
            user_id: user.id,
            query: prompt,
            response: `Image generated: ${result.revisedPrompt || prompt}`,
            sources_used: ['openai_gpt_image_1'],
            citations: [],
            analysis: {
              type: 'image_generation',
              imageId: result.imageId,
              originalPrompt: prompt,
              revisedPrompt: result.revisedPrompt,
              context: context || null,
              options: generationOptions,
            },
            execution_time_ms: executionTime,
            tokens_used: 0, // Images don't use tokens in the traditional sense
            request_id: requestId,
            created_at: new Date().toISOString(),
          });

        if (historyError) {
          routeLogger.error('Failed to save image generation history', {
            userId: user.id,
            historyError
          });
          // Don't fail the request if history saving fails
        }
      } catch (historyError) {
        routeLogger.error('Exception saving image generation history', {
          userId: user.id,
          historyError
        });
      }
    } else {
      routeLogger.info('Image generated successfully - not saved to database (recommended)', {
        userId: user.id,
        imageId: result.imageId
      });
    }

    routeLogger.info('Image generation completed successfully', {
      userId: user.id,
      imageId: result.imageId,
      executionTime,
      revisedPrompt: result.revisedPrompt?.substring(0, 100) +
        (result.revisedPrompt && result.revisedPrompt.length > 100 ? '...' : ''),
      savedToDatabase: saveToDatabase
    });

    return NextResponse.json({
      success: true,
      imageId: result.imageId,
      imageBase64: result.imageBase64,
      originalPrompt: prompt,
      revisedPrompt: result.revisedPrompt,
      metadata: {
        requestId,
        executionTime,
        generatedAt: result.metadata?.generatedAt,
        model: result.metadata?.model,
        options: generationOptions,
        context: context || null,
      }
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;
    routeLogger.error('Unexpected error in image generation API', {
      error: errorMessage,
      stack: errorStack
    });

    return NextResponse.json(
      {
        error: 'Internal server error',
        requestId
      },
      { status: 500 }
    );
  }
}

export async function GET(_request: NextRequest) {
  const requestId = `img-history-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  const routeLogger = imageApiLogger.child({ requestId });

  try {
    routeLogger.info('Image generation history API request received');

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      routeLogger.warn('Unauthorized image history request');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = Math.max(parseInt(searchParams.get('offset') || '0'), 0);

    // Fetch recent image generations
    const { data: imageHistory, error: historyError } = await supabase
      .from('buddychip_search_history')
      .select(`
        id,
        query,
        response,
        analysis,
        execution_time_ms,
        created_at
      `)
      .eq('user_id', user.id)
      .eq('analysis->>type', 'image_generation')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (historyError) {
      routeLogger.error('Failed to fetch image generation history', {
        userId: user.id,
        historyError
      });
      return NextResponse.json(
        { error: 'Failed to fetch image history' },
        { status: 500 }
      );
    }

    const formattedHistory = imageHistory?.map(item => ({
      id: item.id,
      originalPrompt: item.query,
      revisedPrompt: item.analysis?.revisedPrompt,
      imageId: item.analysis?.imageId,
      context: item.analysis?.context,
      options: item.analysis?.options,
      executionTime: item.execution_time_ms,
      createdAt: item.created_at,
    })) || [];

    routeLogger.info('Image generation history fetched successfully', {
      userId: user.id,
      historyCount: formattedHistory.length,
      limit,
      offset
    });

    return NextResponse.json({
      success: true,
      history: formattedHistory,
      pagination: {
        limit,
        offset,
        hasMore: (imageHistory?.length || 0) === limit,
      },
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
      }
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;
    routeLogger.error('Unexpected error in image history API', {
      error: errorMessage,
      stack: errorStack
    });

    return NextResponse.json(
      {
        error: 'Internal server error',
        requestId
      },
      { status: 500 }
    );
  }
}
