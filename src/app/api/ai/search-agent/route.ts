/**
 * API Route: /api/ai/search-agent
 *
 * Handles search requests using the AI agent with Perplexity and xAI Live Search tools.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { executeSearchAgent } from '@/lib/ai/agents/search-agent';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';

const searchAgentLogger = logger.child({ component: 'SearchAgentAPI' });

// Request validation schema
const searchRequestSchema = z.object({
  query: z.string().min(1, 'Query is required').max(1000, 'Query too long'),
  save_to_history: z.boolean().default(true),
  user_context: z.object({
    preferences: z.array(z.string()).optional(),
    previous_searches: z.array(z.string()).optional(),
  }).optional(),
});

export async function POST(request: NextRequest) {
  const requestId = `search-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const routeLogger = searchAgentLogger.child({ requestId });

  try {
    routeLogger.info('Search agent API request received');

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      routeLogger.warn('Unauthorized search agent request');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = searchRequestSchema.safeParse(body);

    if (!validationResult.success) {
      routeLogger.warn('Invalid search agent request', {
        errors: validationResult.error.errors,
        body
      });
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const { query, save_to_history } = validationResult.data;

    routeLogger.info('Processing search agent request', {
      userId: user.id,
      query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
      saveToHistory: save_to_history
    });

    // Check rate limiting (optional - implement based on your needs)
    if (save_to_history) {
      const { data: recentSearches, error: searchError } = await supabase
        .from('buddychip_search_history')
        .select('created_at')
        .eq('user_id', user.id)
        .gte('created_at', new Date(Date.now() - 60000).toISOString()) // Last minute
        .order('created_at', { ascending: false });

      if (searchError) {
        routeLogger.warn('Failed to check rate limit', { userId: user.id, searchError });
      } else if (recentSearches && recentSearches.length >= 10) {
        routeLogger.warn('Rate limit exceeded', { userId: user.id, recentSearchCount: recentSearches.length });
        return NextResponse.json(
          { error: 'Rate limit exceeded. Please wait before making another search.' },
          { status: 429 }
        );
      }
    }

    // Execute the search agent
    const startTime = Date.now();
    const result = await executeSearchAgent(query, requestId);
    const executionTime = Date.now() - startTime;

    if (!result.success) {
      routeLogger.error('Search agent execution failed', {
        userId: user.id,
        query,
        error: result.error,
        executionTime
      });

      return NextResponse.json(
        {
          error: 'Search failed',
          details: result.error,
          requestId
        },
        { status: 500 }
      );
    }

    // Save to search history if requested
    if (save_to_history && result.response) {
      try {
        const { error: historyError } = await supabase
          .from('buddychip_search_history')
          .insert({
            user_id: user.id,
            query,
            response: result.response.content || '',
            sources_used: result.response.toolsUsed || (result.response.source ? [result.response.source] : []),
            citations: result.response.citations || [],
            analysis: result.analysis,
            execution_time_ms: executionTime,
            tokens_used: result.response.usage?.total_tokens || 0,
            request_id: requestId,
            created_at: new Date().toISOString(),
          });

        if (historyError) {
          routeLogger.error('Failed to save search history', {
            userId: user.id,
            historyError
          });
          // Don't fail the request if history saving fails
        }
      } catch (historyError) {
        routeLogger.error('Exception saving search history', {
          userId: user.id,
          historyError
        });
      }
    }

    routeLogger.info('Search agent request completed successfully', {
      userId: user.id,
      query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
      executionTime,
      toolsUsed: result.response?.toolsUsed || [result.response?.source],
      citationsCount: result.response?.citations?.length || 0,
      responseLength: result.response?.content?.length || 0,
      tokensUsed: result.response?.usage?.total_tokens || 0
    });

    return NextResponse.json({
      success: true,
      query,
      response: result.response?.content || '',
      sources: result.response?.toolsUsed || [result.response?.source],
      citations: result.response?.citations || [],
      analysis: result.analysis,
      metadata: {
        requestId,
        executionTime,
        tokensUsed: result.response?.usage?.total_tokens || 0,
        timestamp: new Date().toISOString(),
      }
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;
    routeLogger.error('Unexpected error in search agent API', {
      error: errorMessage,
      stack: errorStack
    });

    return NextResponse.json(
      {
        error: 'Internal server error',
        requestId
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const requestId = `search-history-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const routeLogger = searchAgentLogger.child({ requestId });

  try {
    routeLogger.info('Search history API request received');

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      routeLogger.warn('Unauthorized search history request');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = Math.max(parseInt(searchParams.get('offset') || '0'), 0);

    // Fetch search history
    const { data: searchHistory, error: historyError } = await supabase
      .from('buddychip_search_history')
      .select(`
        id,
        query,
        response,
        sources_used,
        citations,
        analysis,
        execution_time_ms,
        tokens_used,
        created_at
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (historyError) {
      routeLogger.error('Failed to fetch search history', {
        userId: user.id,
        historyError
      });
      return NextResponse.json(
        { error: 'Failed to fetch search history' },
        { status: 500 }
      );
    }

    routeLogger.info('Search history fetched successfully', {
      userId: user.id,
      count: searchHistory?.length || 0,
      limit,
      offset
    });

    return NextResponse.json({
      success: true,
      searches: searchHistory || [],
      pagination: {
        limit,
        offset,
        hasMore: (searchHistory?.length || 0) === limit,
      },
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
      }
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;
    routeLogger.error('Unexpected error in search history API', {
      error: errorMessage,
      stack: errorStack
    });

    return NextResponse.json(
      {
        error: 'Internal server error',
        requestId
      },
      { status: 500 }
    );
  }
}
