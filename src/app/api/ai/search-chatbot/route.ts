/**
 * API Route: /api/ai/search-chatbot
 *
 * Handles conversational interactions with the AI search chatbot that has
 * persistent memory using Mem0 and search capabilities.
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { executeChatbotConversation, generateSessionId } from '@/lib/ai/agents/search-chatbot';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';

const chatbotLogger = logger.child({ component: 'SearchChatbotAPI' });

// Request validation schema
const chatRequestSchema = z.object({
  message: z.string().min(1, 'Message is required').max(2000, 'Message too long'),
  sessionId: z.string().optional(),
  conversationHistory: z.array(z.object({
    role: z.enum(['user', 'assistant']),
    content: z.string(),
  })).optional(),
  includeSearch: z.boolean().default(true),
  generateNewSession: z.boolean().default(false),
});



export async function POST(request: NextRequest) {
  const requestId = `chatbot-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const routeLogger = chatbotLogger.child({ requestId });

  try {
    routeLogger.info('Search chatbot API request received');

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      routeLogger.warn('Unauthorized chatbot request');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validationResult = chatRequestSchema.safeParse(body);

    if (!validationResult.success) {
      routeLogger.warn('Invalid chatbot request', {
        errors: validationResult.error.errors,
        body
      });
      return NextResponse.json(
        {
          error: 'Invalid request data',
          details: validationResult.error.errors
        },
        { status: 400 }
      );
    }

    const {
      message,
      sessionId: providedSessionId,
      conversationHistory = [],
      includeSearch,
      generateNewSession
    } = validationResult.data;

    // Generate or use provided session ID
    const sessionId = generateNewSession || !providedSessionId
      ? generateSessionId()
      : providedSessionId;

    routeLogger.info('Processing chatbot request', {
      userId: user.id,
      sessionId,
      messageLength: message.length,
      historyLength: conversationHistory.length,
      includeSearch,
      isNewSession: generateNewSession || !providedSessionId
    });

    // Check rate limiting (optional - implement based on your needs)
    const { data: recentChats, error: chatError } = await supabase
      .from('buddychip_search_history')
      .select('created_at')
      .eq('user_id', user.id)
      .gte('created_at', new Date(Date.now() - 60000).toISOString()) // Last minute
      .order('created_at', { ascending: false });

    if (chatError) {
      routeLogger.warn('Failed to check rate limit', { userId: user.id, chatError });
    } else if (recentChats && recentChats.length >= 20) {
      routeLogger.warn('Rate limit exceeded', { userId: user.id, recentChatCount: recentChats.length });
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please wait before sending another message.' },
        { status: 429 }
      );
    }

    // Execute the chatbot conversation
    const startTime = Date.now();
    const result = await executeChatbotConversation(message, {
      userId: user.id,
      sessionId,
      conversationHistory,
      includeSearch,
    });
    const executionTime = Date.now() - startTime;

    if (!result.success) {
      routeLogger.error('Chatbot conversation failed', {
        userId: user.id,
        sessionId,
        message: message.substring(0, 100) + (message.length > 100 ? '...' : ''),
        error: result.error,
        executionTime
      });

      return NextResponse.json(
        {
          error: 'Conversation failed',
          details: result.error,
          sessionId,
          requestId
        },
        { status: 500 }
      );
    }

    // Log the conversation in search history for tracking
    try {
      const { error: historyError } = await supabase
        .from('buddychip_search_history')
        .insert({
          user_id: user.id,
          query: message,
          response: result.response || '',
          sources_used: (result.metadata.searchResults as unknown as { sources: string[] })?.sources || ['chatbot'],
          citations: (result.metadata.searchResults as unknown as { citations: string[] })?.citations || [],
          analysis: {
            type: 'chatbot_conversation',
            sessionId,
            toolsUsed: result.metadata.toolsUsed,
            memoryContextUsed: result.metadata.memoryContext?.length || 0,
          },
          execution_time_ms: executionTime,
          tokens_used: 0, // TODO: Add token counting
          request_id: requestId,
          created_at: new Date().toISOString(),
        });

      if (historyError) {
        routeLogger.error('Failed to save conversation history', {
          userId: user.id,
          historyError
        });
        // Don't fail the request if history saving fails
      }
    } catch (historyError) {
      routeLogger.error('Exception saving conversation history', {
        userId: user.id,
        historyError
      });
    }

    routeLogger.info('Chatbot conversation completed successfully', {
      userId: user.id,
      sessionId,
      executionTime,
      toolsUsed: result.metadata.toolsUsed,
      searchExecuted: !!result.metadata.searchResults,
      memoryContextUsed: result.metadata.memoryContext?.length || 0,
      responseLength: result.response?.length || 0
    });

    return NextResponse.json({
      success: true,
      response: result.response,
      sessionId,
      metadata: {
        requestId,
        executionTime,
        toolsUsed: result.metadata.toolsUsed,
        searchResults: result.metadata.searchResults,
        memoryContextUsed: result.metadata.memoryContext?.length || 0,
        conversationStored: result.metadata.conversationStored,
        timestamp: new Date().toISOString(),
      }
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;
    routeLogger.error('Unexpected error in chatbot API', {
      error: errorMessage,
      stack: errorStack
    });

    return NextResponse.json(
      {
        error: 'Internal server error',
        requestId
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  const requestId = `chatbot-sessions-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const routeLogger = chatbotLogger.child({ requestId });

  try {
    routeLogger.info('Chatbot sessions API request received');

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      routeLogger.warn('Unauthorized chatbot sessions request');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = Math.max(parseInt(searchParams.get('offset') || '0'), 0);

    // Fetch recent chatbot conversations grouped by session
    const { data: conversations, error: conversationError } = await supabase
      .from('buddychip_search_history')
      .select(`
        id,
        query,
        response,
        analysis,
        created_at
      `)
      .eq('user_id', user.id)
      .eq('analysis->>type', 'chatbot_conversation')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (conversationError) {
      routeLogger.error('Failed to fetch chatbot conversations', {
        userId: user.id,
        conversationError
      });
      return NextResponse.json(
        { error: 'Failed to fetch conversations' },
        { status: 500 }
      );
    }

    // Group conversations by session
    const sessionMap = new Map();
    conversations?.forEach(conv => {
      const sessionId = conv.analysis?.sessionId || 'unknown';
      if (!sessionMap.has(sessionId)) {
        sessionMap.set(sessionId, {
          sessionId,
          messages: [],
          lastActivity: conv.created_at,
          toolsUsed: new Set(),
        });
      }

      const session = sessionMap.get(sessionId);
      session.messages.push({
        id: conv.id,
        query: conv.query,
        response: conv.response,
        timestamp: conv.created_at,
      });

      if (conv.analysis?.toolsUsed) {
        conv.analysis.toolsUsed.forEach((tool: string) => session.toolsUsed.add(tool));
      }
    });

    const sessions = Array.from(sessionMap.values()).map(session => ({
      ...session,
      toolsUsed: Array.from(session.toolsUsed),
      messageCount: session.messages.length,
    }));

    routeLogger.info('Chatbot sessions fetched successfully', {
      userId: user.id,
      sessionsCount: sessions.length,
      limit,
      offset
    });

    return NextResponse.json({
      success: true,
      sessions,
      pagination: {
        limit,
        offset,
        hasMore: (conversations?.length || 0) === limit,
      },
      metadata: {
        requestId,
        timestamp: new Date().toISOString(),
      }
    });

  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    const errorStack = error instanceof Error ? error.stack : undefined;
    routeLogger.error('Unexpected error in chatbot sessions API', {
      error: errorMessage,
      stack: errorStack
    });

    return NextResponse.json(
      {
        error: 'Internal server error',
        requestId
      },
      { status: 500 }
    );
  }
}
