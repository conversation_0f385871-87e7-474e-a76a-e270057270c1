import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { ensureProfile } from '@/lib/utils/ensure-profile';

/**
 * GET handler to fetch user preferences
 */
export async function GET() {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure profile exists
    await ensureProfile(supabase, user.id);

    // Fetch user preferences
    const { data: preferences, error: preferencesError } = await supabase
      .from('buddychip_user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (preferencesError && preferencesError.code !== 'PGRST116') {
      // PGRST116 is "no rows returned" which is fine - user just hasn't set preferences yet
      return NextResponse.json(
        { error: preferencesError.message },
        { status: 500 }
      );
    }

    // Return preferences or default values
    return NextResponse.json(preferences || {
      user_id: user.id,
      reply_style: 'Professional and friendly',
      interests: 'General business and technology',
      tone: 'Helpful and informative',
      system_prompt: ''
    });
  } catch (error) {
    console.error('Error fetching user preferences:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

/**
 * POST handler to update user preferences
 */
export async function POST(_request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure profile exists
    await ensureProfile(supabase, user.id);

    // Get request body
    const preferences = await request.json();

    // Validate required fields
    if (!preferences) {
      return NextResponse.json(
        { error: 'Preferences are required' },
        { status: 400 }
      );
    }

    // Check if preferences already exist
    const { data: existingPreferences } = await supabase
      .from('buddychip_user_preferences')
      .select('id')
      .eq('user_id', user.id)
      .single();

    let result;

    if (existingPreferences) {
      // Update existing preferences
      const { data, error } = await supabase
        .from('buddychip_user_preferences')
        .update({
          reply_style: preferences.reply_style,
          interests: preferences.interests,
          tone: preferences.tone,
          system_prompt: preferences.system_prompt,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', user.id)
        .select()
        .single();

      if (error) {
        return NextResponse.json(
          { error: error.message },
          { status: 500 }
        );
      }

      result = data;
    } else {
      // Insert new preferences
      const { data, error } = await supabase
        .from('buddychip_user_preferences')
        .insert({
          user_id: user.id,
          reply_style: preferences.reply_style,
          interests: preferences.interests,
          tone: preferences.tone,
          system_prompt: preferences.system_prompt
        })
        .select()
        .single();

      if (error) {
        return NextResponse.json(
          { error: error.message },
          { status: 500 }
        );
      }

      result = data;
    }

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error updating user preferences:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
