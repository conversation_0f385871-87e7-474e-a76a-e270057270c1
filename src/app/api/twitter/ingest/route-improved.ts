/**
 * API Route: /api/twitter/ingest
 *
 * <PERSON>les Twitter tweet ingestion for followed accounts.
 */

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { ensureProfile } from '@/lib/utils/ensure-profile';
import { analyzeTweet } from '@/lib/ai/tweet-analysis-service';
import { withErrorHandling } from '@/lib/utils/error-handler';
import { AuthenticationError, NotFoundError, ExternalServiceError } from '@/lib/utils/errors';
import { createLogger } from '@/lib/utils/logger';
import { validateQueryParams } from '@/lib/utils/validation';
import { z } from 'zod';

// Create a route-specific logger
const routeLogger = createLogger({ component: 'api-twitter-ingest' });

// Twitter API v2 endpoint for user timeline
const TWITTER_API_BASE = 'https://api.twitter.com/2';

// Rate limiting configuration
// Limiting to 10 tweets per account
const RATE_LIMIT = {
  MAX_REQUESTS_PER_WINDOW: 1,    // Only make one request per account
  WINDOW_MS: 15 * 60 * 1000,     // Window size in milliseconds (15 minutes)
  TWEETS_PER_REQUEST: 10,        // Number of tweets to fetch per request (limited to 10)
  DELAY_BETWEEN_REQUESTS_MS: 100 // Delay between requests in milliseconds
};

// Schema for account ID in query params
const accountIdQuerySchema = z.object({
  account_id: z.string().min(1, 'Account ID is required')
});

/**
 * GET handler to manually trigger tweet ingestion for a specific account
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Create a request-specific logger with a unique request ID
  const requestId = request.headers.get('x-request-id') || `req-${Date.now()}`;
  const logger = routeLogger.child({
    requestId,
    method: 'GET',
    path: '/api/twitter/ingest'
  });

  logger.info('Received request to ingest tweets');

  const supabase = await createSupabaseServerClient();

  // Get the authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    logger.warn('Authentication failed', { error: userError });
    throw new AuthenticationError('You must be logged in to ingest tweets');
  }

  // Ensure profile exists
  await ensureProfile(supabase, user.id);

  // Get and validate the account ID from the URL
  const { account_id } = validateQueryParams(
    request,
    accountIdQuerySchema,
    'Invalid account ID'
  );

  logger.info('Ingesting tweets for account', {
    userId: user.id,
    accountId: account_id
  });

  // Fetch the account
  const { data: account, error: accountError } = await supabase
    .from('buddychip_followed_accounts')
    .select('*')
    .eq('id', account_id)
    .eq('user_id', user.id)
    .single();

  if (accountError || !account) {
    logger.warn('Account not found or not authorized', { accountId: account_id });
    throw new NotFoundError(
      'Account not found or not authorized',
      'ACCOUNT_NOT_FOUND',
      { accountId: account_id }
    );
  }

  // Check if we have a Twitter API key
  if (!process.env.TWITTER_BEARER_TOKEN) {
    logger.error('Twitter API key not configured');
    throw new ExternalServiceError(
      'Twitter API key not configured',
      'TWITTER_API_KEY_MISSING'
    );
  }

  try {
    // Fetch tweets from Twitter API
    const twitterUrl = `${TWITTER_API_BASE}/users/by/username/${account.twitter_handle}/tweets?max_results=${RATE_LIMIT.TWEETS_PER_REQUEST}&exclude=retweets,replies&tweet.fields=created_at,public_metrics,entities`;

    logger.debug('Fetching tweets from Twitter API', { url: twitterUrl });

    const response = await fetch(twitterUrl, {
      headers: {
        'Authorization': `Bearer ${process.env.TWITTER_BEARER_TOKEN}`
      }
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      logger.error('Twitter API error', {
        status: response.status,
        statusText: response.statusText,
        errorData
      });

      throw new ExternalServiceError(
        `Twitter API error: ${response.statusText}`,
        'TWITTER_API_ERROR',
        { status: response.status, errorData }
      );
    }

    const data = await response.json();
    const tweets = data.data || [];

    logger.info('Tweets fetched from Twitter API', {
      accountId: account_id,
      tweetCount: tweets.length
    });

    // Process and store tweets
    const storedTweets = [];

    for (const tweet of tweets) {
      // Check if tweet already exists
      const { data: existingTweet } = await supabase
        .from('buddychip_tweets')
        .select('id')
        .eq('tweet_id', tweet.id)
        .eq('user_id', user.id)
        .maybeSingle();

      if (existingTweet) {
        logger.debug('Tweet already exists', { tweetId: tweet.id });
        continue;
      }

      // Store the tweet
      const { data: newTweet, error: insertError } = await supabase
        .from('buddychip_tweets')
        .insert({
          user_id: user.id,
          account_id: account.id,
          tweet_id: tweet.id,
          author_handle: account.twitter_handle,
          content: tweet.text,
          created_at: tweet.created_at,
          metrics: {
            likes: tweet.public_metrics?.like_count || 0,
            retweets: tweet.public_metrics?.retweet_count || 0,
            replies: tweet.public_metrics?.reply_count || 0,
            quotes: tweet.public_metrics?.quote_count || 0
          },
          entities: tweet.entities || {},
          is_analyzed: false
        })
        .select()
        .single();

      if (insertError) {
        logger.error('Error storing tweet', {
          tweetId: tweet.id,
          error: insertError
        });
        continue;
      }

      logger.debug('Tweet stored successfully', {
        tweetId: tweet.id,
        newTweetId: newTweet.id
      });

      storedTweets.push(newTweet);

      // Analyze the tweet
      try {
        await analyzeTweet(newTweet.id, user.id);
        logger.debug('Tweet analyzed successfully', { tweetId: newTweet.id });
      } catch (analyzeError) {
        logger.error('Error analyzing tweet', {
          tweetId: newTweet.id,
          error: analyzeError
        });
      }
    }

    logger.info('Tweet ingestion completed', {
      accountId: account_id,
      storedTweetCount: storedTweets.length
    });

    // Update the last_fetched timestamp for the account
    await supabase
      .from('buddychip_followed_accounts')
      .update({ last_fetched: new Date().toISOString() })
      .eq('id', account.id)
      .eq('user_id', user.id);

    return NextResponse.json({
      message: 'Tweets ingested successfully',
      account_id: account.id,
      tweets_fetched: tweets.length,
      tweets_stored: storedTweets.length
    });
  } catch (error: unknown) {
    if (error instanceof ExternalServiceError) {
      throw error;
    }

    logger.error('Error ingesting tweets', error);
    throw new ExternalServiceError(
      'Failed to ingest tweets',
      'TWEET_INGESTION_FAILED',
      { originalError: error }
    );
  }
});
