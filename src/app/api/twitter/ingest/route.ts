import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { ensureProfile } from '@/lib/utils/ensure-profile';
import { analyzeTweet } from '@/lib/ai/tweet-analysis-service';

// Twitter API v2 endpoint for user timeline
const TWITTER_API_BASE = 'https://api.twitter.com/2';

// Rate limiting configuration
// Limiting to 10 tweets per account
const RATE_LIMIT = {
  MAX_REQUESTS_PER_WINDOW: 1,    // Only make one request per account
  WINDOW_MS: 15 * 60 * 1000,     // Window size in milliseconds (15 minutes)
  TWEETS_PER_REQUEST: 10,        // Number of tweets to fetch per request (limited to 10)
  DELAY_BETWEEN_REQUESTS_MS: 100 // Delay between requests in milliseconds
};

// GET handler to manually trigger tweet ingestion for a specific account
export async function GET(_request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure profile exists
    await ensureProfile(supabase, user.id);

    // Get the account ID from the URL
    const url = new URL(request.url);
    const accountId = url.searchParams.get('account_id');

    if (!accountId) {
      return NextResponse.json(
        { error: 'Account ID is required' },
        { status: 400 }
      );
    }

    // Fetch the account details
    const { data: account, error: accountError } = await supabase
      .from('buddychip_followed_accounts')
      .select('*')
      .eq('id', accountId)
      .eq('user_id', user.id) // Ensure the account belongs to the user
      .single();

    if (accountError || !account) {
      return NextResponse.json(
        { error: 'Account not found or not authorized' },
        { status: 404 }
      );
    }

    // Fetch tweets for this account
    const result = await fetchTweetsForAccount(account, user.id);

    return NextResponse.json(result);
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// POST handler to trigger tweet ingestion for all followed accounts
export async function POST() {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure profile exists
    await ensureProfile(supabase, user.id);

    // Fetch all followed accounts for the user
    const { data: accounts, error: accountsError } = await supabase
      .from('buddychip_followed_accounts')
      .select('*')
      .eq('user_id', user.id);

    if (accountsError) {
      return NextResponse.json(
        { error: 'Failed to fetch accounts' },
        { status: 500 }
      );
    }

    if (!accounts || accounts.length === 0) {
      return NextResponse.json(
        { message: 'No accounts to fetch tweets for' },
        { status: 200 }
      );
    }

    // Fetch tweets for each account
    const results = await Promise.all(
      accounts.map(account => fetchTweetsForAccount(account, user.id))
    );

    return NextResponse.json({
      message: `Processed ${accounts.length} accounts`,
      results
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// Helper function to fetch tweets for a specific account
async function fetchTweetsForAccount(account: { id: string; twitter_handle: string; twitter_user_id?: string }, userId: string) {
  try {
    // Check if we have Twitter API credentials
    if (!process.env.TWITTER_BEARER_TOKEN) {
      return {
        account: account.twitter_handle,
        success: false,
        error: 'Twitter API credentials not configured'
      };
    }

    // Fetch tweets from Twitter API
    let tweets;
    let usedMockData = false;

    try {
      tweets = await fetchTweetsFromTwitter(account.twitter_handle);
    } catch (error) {
      console.warn(`Error fetching tweets from Twitter API: ${error instanceof Error ? error.message : String(error)}`);
      console.warn('Falling back to mock tweets for development');
      tweets = generateMockTweets(account.twitter_handle);
      usedMockData = true;
    }

    // If no tweets were found, return early
    if (!tweets || tweets.length === 0) {
      return {
        account: account.twitter_handle,
        success: true,
        saved: 0,
        errors: 0,
        message: 'No tweets found for this account',
        usedMockData
      };
    }

    // Store the tweets in the database
    const supabase = await createSupabaseServerClient();

    let savedCount = 0;
    let errorCount = 0;

    for (const tweet of tweets) {
      // Check if the tweet already exists for this user
      const { data: existingTweet } = await supabase
        .from('buddychip_tweets')
        .select('id')
        .eq('user_id', userId)
        .eq('tweet_id', tweet.id)
        .maybeSingle();

      if (existingTweet) {
        // Skip existing tweets
        continue;
      }

      // Parse the created_at date if it exists
      let tweetCreatedAt = null;
      if (tweet.created_at) {
        tweetCreatedAt = new Date(tweet.created_at).toISOString();
      }

      // Insert the new tweet
      const { data: insertedTweet, error: insertError } = await supabase
        .from('buddychip_tweets')
        .insert({
          user_id: userId,
          tweet_id: tweet.id,
          author_id: tweet.author_id,
          author_handle: account.twitter_handle,
          content: tweet.text,
          source_account_id: account.id,
          tweet_created_at: tweetCreatedAt,
          raw_data: tweet
        })
        .select()
        .single();

      if (insertError) {
        console.error('Error inserting tweet:', insertError);
        errorCount++;
      } else {
        savedCount++;

        // Automatically analyze the tweet
        try {
          if (insertedTweet) {
            // Run analysis in the background without waiting for it to complete
            analyzeTweet(insertedTweet.id, userId).catch(analyzeError => {
              console.error(`Error analyzing tweet ${insertedTweet.id}:`, analyzeError);
            });
          }
        } catch (analyzeError) {
          console.error('Error starting tweet analysis:', analyzeError);
          // Don't count this as an error for the tweet ingestion process
        }
      }
    }

    // Update the twitter_user_id in the followed_accounts table if it's not set
    if (!account.twitter_user_id && tweets.length > 0 && tweets[0].author_id) {
      const { error: updateError } = await supabase
        .from('buddychip_followed_accounts')
        .update({ twitter_user_id: tweets[0].author_id })
        .eq('id', account.id)
        .eq('user_id', userId);

      if (updateError) {
        console.error('Error updating account with Twitter user ID:', updateError);
      }
    }

    return {
      account: account.twitter_handle,
      success: true,
      saved: savedCount,
      errors: errorCount,
      usedMockData,
      message: 'Only the 10 most recent tweets are fetched per account'
    };
  } catch (error) {
    console.error(`Error fetching tweets for ${account.twitter_handle}:`, error);
    return {
      account: account.twitter_handle,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Helper function to generate mock tweets for testing
function generateMockTweets(handle: string) {
  // Generate between 1-10 mock tweets to simulate the 10 tweet limit
  const count = Math.min(10, Math.floor(Math.random() * 10) + 1);
  console.log(`Generating ${count} mock tweets for ${handle} (limited to 10 max)`);
  const tweets = [];

  for (let i = 0; i < count; i++) {
    const id = `mock-${handle}-${Date.now()}-${i}`;
    const created_at = new Date().toISOString();

    tweets.push({
      id,
      author_id: `author-${handle}`,
      text: `This is a mock tweet #${i + 1} from @${handle}. #mockdata #testing`,
      created_at,
      // Add other fields that would be in a real Twitter API response
      public_metrics: {
        retweet_count: Math.floor(Math.random() * 100),
        reply_count: Math.floor(Math.random() * 20),
        like_count: Math.floor(Math.random() * 500),
        quote_count: Math.floor(Math.random() * 10)
      }
    });
  }

  return tweets;
}

/**
 * Fetches tweets from Twitter API for a given handle
 * Includes rate limiting and pagination for comprehensive tweet collection
 */
async function fetchTweetsFromTwitter(handle: string) {
  try {
    // First, get the Twitter user ID from the handle
    const userResponse = await fetch(
      `${TWITTER_API_BASE}/users/by/username/${handle}?user.fields=id,username,description,profile_image_url`,
      {
        headers: {
          Authorization: `Bearer ${process.env.TWITTER_BEARER_TOKEN}`
        }
      }
    );

    if (!userResponse.ok) {
      // Check for rate limiting
      if (userResponse.status === 429) {
        const resetTime = userResponse.headers.get('x-rate-limit-reset');
        throw new Error(`Twitter API rate limit exceeded. Reset at ${new Date(parseInt(resetTime || '0') * 1000).toISOString()}`);
      }
      throw new Error(`Failed to fetch user: ${userResponse.statusText}`);
    }

    const userData = await userResponse.json();

    if (!userData.data) {
      throw new Error(`User not found: ${handle}`);
    }

    const userId = userData.data.id;

    // Store all tweets here
    let allTweets: Array<{
      id: string;
      author_id: string;
      text: string;
      created_at: string;
      public_metrics?: {
        retweet_count: number;
        reply_count: number;
        like_count: number;
        quote_count: number;
      };
    }> = [];

    // Fetch only the 10 most recent tweets (no pagination)
    console.log(`Fetching the 10 most recent tweets for ${handle}`);

    // Build the URL to fetch only 10 tweets
    const url = `${TWITTER_API_BASE}/users/${userId}/tweets?max_results=${RATE_LIMIT.TWEETS_PER_REQUEST}&tweet.fields=created_at,public_metrics,entities,context_annotations&expansions=author_id,referenced_tweets.id`;

    const tweetsResponse = await fetch(url, {
      headers: {
        Authorization: `Bearer ${process.env.TWITTER_BEARER_TOKEN}`
      }
    });

    if (!tweetsResponse.ok) {
      // Check for rate limiting
      if (tweetsResponse.status === 429) {
        const resetTime = tweetsResponse.headers.get('x-rate-limit-reset');
        throw new Error(`Twitter API rate limit exceeded. Reset at ${new Date(parseInt(resetTime || '0') * 1000).toISOString()}`);
      }
      throw new Error(`Failed to fetch tweets: ${tweetsResponse.statusText}`);
    }

    const tweetsData = await tweetsResponse.json();

    // Add the tweets to our collection
    if (tweetsData.data && tweetsData.data.length > 0) {
      allTweets = [...allTweets, ...tweetsData.data];
    }

    console.log(`Fetched ${allTweets.length} of the 10 most recent tweets for ${handle}`);

    return allTweets;
  } catch (error) {
    console.error(`Error fetching tweets for ${handle}:`, error);
    throw error;
  }
}

