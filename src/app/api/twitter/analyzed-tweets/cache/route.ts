/**
 * Cache Management API for Analyzed Tweets
 * 
 * Provides endpoints for cache invalidation, statistics, and health checks
 */

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { ensureProfile } from '@/lib/utils/ensure-profile';
import { logger } from '@/lib/utils/logger';
import { AnalyzedTweetsCache } from '@/lib/cache/analyzed-tweets-cache';
import { z } from 'zod';

const cacheLogger = logger.child({ component: 'analyzed-tweets-cache-api' });

// Request validation schemas
const invalidateSchema = z.object({
  type: z.enum(['user', 'account', 'all']),
  account_id: z.string().optional(),
});

// GET handler - Cache statistics and health check
export async function GET(_request: NextRequest) {
  const requestId = `cache_get_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  
  try {
    const supabase = await createSupabaseServerClient();

    // Authenticate user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureProfile(supabase, user.id);

    // Get cache statistics
    const stats = AnalyzedTweetsCache.getStats();
    const healthCheck = AnalyzedTweetsCache.healthCheck();

    cacheLogger.info('Cache statistics requested', {
      requestId,
      userId: user.id,
      stats
    });

    return NextResponse.json({
      stats,
      health: healthCheck,
      timestamp: new Date().toISOString(),
      requestId
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    cacheLogger.error('Cache statistics request failed', {
      requestId,
      error: errorMessage
    });

    return NextResponse.json(
      { error: 'Failed to get cache statistics' },
      { status: 500 }
    );
  }
}

// POST handler - Cache invalidation
export async function POST(_request: NextRequest) {
  const requestId = `cache_post_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  
  try {
    const supabase = await createSupabaseServerClient();

    // Authenticate user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureProfile(supabase, user.id);

    // Parse and validate request body
    const body = await request.json();
    const { type, account_id } = invalidateSchema.parse(body);

    let deletedCount = 0;
    let message = '';

    switch (type) {
      case 'user':
        deletedCount = AnalyzedTweetsCache.invalidateUser(user.id);
        message = `Invalidated ${deletedCount} cache entries for user`;
        break;

      case 'account':
        if (!account_id) {
          return NextResponse.json(
            { error: 'account_id is required for account invalidation' },
            { status: 400 }
          );
        }
        deletedCount = AnalyzedTweetsCache.invalidateAccount(user.id, account_id);
        message = `Invalidated ${deletedCount} cache entries for account ${account_id}`;
        break;

      case 'all':
        AnalyzedTweetsCache.clear();
        message = 'Cleared entire cache';
        deletedCount = -1; // Indicate full clear
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid invalidation type' },
          { status: 400 }
        );
    }

    cacheLogger.info('Cache invalidation completed', {
      requestId,
      userId: user.id,
      type,
      accountId: account_id,
      deletedCount
    });

    return NextResponse.json({
      success: true,
      message,
      deletedCount,
      type,
      timestamp: new Date().toISOString(),
      requestId
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    cacheLogger.error('Cache invalidation failed', {
      requestId,
      error: errorMessage
    });

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: `Invalid request: ${error.errors.map(e => e.message).join(', ')}` },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to invalidate cache' },
      { status: 500 }
    );
  }
}

// DELETE handler - Clear all cache (admin operation)
export async function DELETE(_request: NextRequest) {
  const requestId = `cache_delete_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  
  try {
    const supabase = await createSupabaseServerClient();

    // Authenticate user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureProfile(supabase, user.id);

    // Clear all cache
    AnalyzedTweetsCache.clear();

    cacheLogger.warn('Full cache clear performed', {
      requestId,
      userId: user.id
    });

    return NextResponse.json({
      success: true,
      message: 'All cache cleared',
      timestamp: new Date().toISOString(),
      requestId
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    cacheLogger.error('Cache clear failed', {
      requestId,
      error: errorMessage
    });

    return NextResponse.json(
      { error: 'Failed to clear cache' },
      { status: 500 }
    );
  }
}

// PATCH handler - Cache warm-up
export async function PATCH(_request: NextRequest) {
  const requestId = `cache_patch_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  
  try {
    const supabase = await createSupabaseServerClient();

    // Authenticate user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureProfile(supabase, user.id);

    // Common query patterns for warm-up
    const commonParams = [
      { filter: 'all', sort_by: 'latest', limit: 20, offset: 0 },
      { filter: 'worth_replying', sort_by: 'relevance', limit: 20, offset: 0 },
      { filter: 'unanalyzed', sort_by: 'latest', limit: 20, offset: 0 },
    ];

    // Warm up cache
    await AnalyzedTweetsCache.warmUp(user.id, commonParams);

    cacheLogger.info('Cache warm-up completed', {
      requestId,
      userId: user.id,
      paramsCount: commonParams.length
    });

    return NextResponse.json({
      success: true,
      message: `Cache warmed up with ${commonParams.length} common query patterns`,
      patterns: commonParams,
      timestamp: new Date().toISOString(),
      requestId
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    cacheLogger.error('Cache warm-up failed', {
      requestId,
      error: errorMessage
    });

    return NextResponse.json(
      { error: 'Failed to warm up cache' },
      { status: 500 }
    );
  }
}
