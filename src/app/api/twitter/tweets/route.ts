import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { ensureProfile } from '@/lib/utils/ensure-profile';

// GET handler to fetch tweets for the authenticated user
export async function GET(_request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure profile exists
    await ensureProfile(supabase, user.id);

    // Get query parameters
    const url = new URL(request.url);
    const category = url.searchParams.get('category');
    const sentiment = url.searchParams.get('sentiment');
    const importance = url.searchParams.get('importance');
    const irrelevant = url.searchParams.get('irrelevant') === 'true';
    const accountId = url.searchParams.get('account_id');
    const sortBy = url.searchParams.get('sort_by') || 'latest'; // Default to latest

    // Start building the query
    let query = supabase
      .from('buddychip_tweets')
      .select('*, source_account:buddychip_followed_accounts(twitter_handle)')
      .eq('user_id', user.id);

    // Apply filters
    if (category) {
      query = query.eq('buddychip_tweet_categories.category_id', category);
    }

    if (sentiment === 'positive') {
      query = query.gt('sentiment_score', 0.3);
    } else if (sentiment === 'negative') {
      query = query.lt('sentiment_score', -0.3);
    } else if (sentiment === 'neutral') {
      query = query.gte('sentiment_score', -0.3).lte('sentiment_score', 0.3);
    }

    if (importance === 'high') {
      query = query.gt('importance_score', 7);
    } else if (importance === 'medium') {
      query = query.gte('importance_score', 4).lte('importance_score', 7);
    } else if (importance === 'low') {
      query = query.lt('importance_score', 4);
    }

    // Filter by account if specified
    if (accountId) {
      query = query.eq('source_account_id', accountId);
    }

    // Filter by irrelevant flag
    query = query.eq('is_marked_irrelevant', irrelevant);

    // Order by specified sort method
    if (sortBy === 'engagement') {
      // We'll sort by engagement in memory after fetching the data
      // since it requires calculating a composite score from raw_data
      query = query.order('tweet_created_at', { ascending: false });
    } else {
      // Default sort by latest
      query = query.order('tweet_created_at', { ascending: false });
    }

    // Execute the query
    const { data: tweets, error: tweetsError } = await query;

    // If sorting by engagement, we need to do it in memory
    if (tweets && sortBy === 'engagement') {
      // Calculate engagement score for each tweet and sort
      tweets.sort((a, b) => {
        const aMetrics = a.raw_data?.public_metrics || {};
        const bMetrics = b.raw_data?.public_metrics || {};

        const aEngagement = (aMetrics.retweet_count || 0) +
                           (aMetrics.reply_count || 0) +
                           (aMetrics.like_count || 0) +
                           (aMetrics.quote_count || 0);

        const bEngagement = (bMetrics.retweet_count || 0) +
                           (bMetrics.reply_count || 0) +
                           (bMetrics.like_count || 0) +
                           (bMetrics.quote_count || 0);

        return bEngagement - aEngagement; // Sort in descending order
      });
    }

    if (tweetsError) {
      console.error('Error fetching tweets:', tweetsError);
      return NextResponse.json(
        { error: tweetsError.message || 'Failed to fetch tweets' },
        { status: 500 }
      );
    }

    return NextResponse.json({ tweets });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// POST handler to mark a tweet as irrelevant
export async function POST(_request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure profile exists
    await ensureProfile(supabase, user.id);

    // Get request body
    const body = await request.json();
    const { tweet_id, is_irrelevant } = body;

    if (!tweet_id) {
      return NextResponse.json(
        { error: 'Tweet ID is required' },
        { status: 400 }
      );
    }

    // Update the tweet
    const { data: updatedTweet, error: updateError } = await supabase
      .from('buddychip_tweets')
      .update({ is_marked_irrelevant: is_irrelevant !== false })
      .eq('id', tweet_id)
      .eq('user_id', user.id)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating tweet:', updateError);
      return NextResponse.json(
        { error: updateError.message || 'Failed to update tweet' },
        { status: 500 }
      );
    }

    return NextResponse.json({ tweet: updatedTweet });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
