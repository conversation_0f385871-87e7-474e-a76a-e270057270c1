/**
 * API Route: /api/twitter/accounts
 *
 * Handles Twitter account management (listing, adding, removing).
 */

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { ensureProfile } from '@/lib/utils/ensure-profile';
import { withErrorHandling } from '@/lib/utils/error-handler';
import { AuthenticationError, NotFoundError, DatabaseError } from '@/lib/utils/errors';
import { createLogger } from '@/lib/utils/logger';
import { validateData, validateQueryParams } from '@/lib/utils/validation';
import { z } from 'zod';

// Create a route-specific logger
const routeLogger = createLogger({ component: 'api-twitter-accounts' });

// Schema for Twitter handle
const twitterHandleSchema = z.object({
  twitter_handle: z.string().min(1, 'Twitter handle is required')
});

// Schema for account ID in query params
const accountIdQuerySchema = z.object({
  id: z.string().min(1, 'Account ID is required')
});

/**
 * GET handler to fetch all followed accounts for the authenticated user
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Create a request-specific logger with a unique request ID
  const requestId = request.headers.get('x-request-id') || `req-${Date.now()}`;
  const logger = routeLogger.child({
    requestId,
    method: 'GET',
    path: '/api/twitter/accounts'
  });

  logger.info('Received request to fetch followed accounts');

  const supabase = await createSupabaseServerClient();

  // Get the authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    logger.warn('Authentication failed', { error: userError });
    throw new AuthenticationError('You must be logged in to view followed accounts');
  }

  // Ensure profile exists
  await ensureProfile(supabase, user.id);

  logger.info('Fetching followed accounts', { userId: user.id });

  // Fetch followed accounts for the user
  const { data: accounts, error: accountsError } = await supabase
    .from('buddychip_followed_accounts')
    .select('*')
    .eq('user_id', user.id)
    .order('created_at', { ascending: false });

  if (accountsError) {
    logger.error('Error fetching followed accounts', accountsError);
    throw new DatabaseError(
      'Failed to fetch followed accounts',
      'FETCH_ACCOUNTS_FAILED',
      accountsError
    );
  }

  logger.info('Followed accounts retrieved', {
    userId: user.id,
    accountCount: accounts?.length || 0
  });

  return NextResponse.json({ accounts: accounts || [] });
});

/**
 * POST handler to add a new followed account
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  // Create a request-specific logger with a unique request ID
  const requestId = request.headers.get('x-request-id') || `req-${Date.now()}`;
  const logger = routeLogger.child({
    requestId,
    method: 'POST',
    path: '/api/twitter/accounts'
  });

  logger.info('Received request to add followed account');

  const supabase = await createSupabaseServerClient();

  // Get the authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    logger.warn('Authentication failed', { error: userError });
    throw new AuthenticationError('You must be logged in to add followed accounts');
  }

  // Get and validate request body
  const body = await request.json();
  const { twitter_handle } = validateData(
    twitterHandleSchema,
    body,
    'Invalid Twitter handle'
  );

  // Clean the handle (remove @ if present)
  const cleanHandle = twitter_handle.startsWith('@')
    ? twitter_handle.substring(1)
    : twitter_handle;

  logger.info('Adding followed account', {
    userId: user.id,
    twitterHandle: cleanHandle
  });

  // Check if the account already exists
  const { data: existingAccount, error: checkError } = await supabase
    .from('buddychip_followed_accounts')
    .select('id')
    .eq('user_id', user.id)
    .eq('twitter_handle', cleanHandle)
    .maybeSingle();

  if (checkError) {
    logger.error('Error checking for existing account', checkError);
    throw new DatabaseError(
      'Failed to check for existing account',
      'CHECK_ACCOUNT_FAILED',
      checkError
    );
  }

  if (existingAccount) {
    logger.info('Account already exists', {
      userId: user.id,
      twitterHandle: cleanHandle
    });

    return NextResponse.json({
      message: 'Account already exists',
      account: existingAccount
    });
  }

  // Add the new account
  const { data: newAccount, error: insertError } = await supabase
    .from('buddychip_followed_accounts')
    .insert({
      user_id: user.id,
      twitter_handle: cleanHandle,
      display_name: cleanHandle, // Default to handle, can be updated later
      is_active: true
    })
    .select()
    .single();

  if (insertError) {
    logger.error('Error adding followed account', insertError);
    throw new DatabaseError(
      'Failed to add followed account',
      'ADD_ACCOUNT_FAILED',
      insertError
    );
  }

  logger.info('Followed account added successfully', {
    userId: user.id,
    accountId: newAccount.id,
    twitterHandle: cleanHandle
  });

  return NextResponse.json({
    message: 'Account added successfully',
    account: newAccount
  });
});

/**
 * DELETE handler to remove a followed account
 */
export const DELETE = withErrorHandling(async (request: NextRequest) => {
  // Create a request-specific logger with a unique request ID
  const requestId = request.headers.get('x-request-id') || `req-${Date.now()}`;
  const logger = routeLogger.child({
    requestId,
    method: 'DELETE',
    path: '/api/twitter/accounts'
  });

  logger.info('Received request to remove followed account');

  const supabase = await createSupabaseServerClient();

  // Get the authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    logger.warn('Authentication failed', { error: userError });
    throw new AuthenticationError('You must be logged in to remove followed accounts');
  }

  // Get and validate the account ID from the URL
  const { id } = validateQueryParams(
    request,
    accountIdQuerySchema,
    'Invalid account ID'
  );

  logger.info('Removing followed account', {
    userId: user.id,
    accountId: id
  });

  // Check if the account exists and belongs to the user
  const { data: existingAccount, error: checkError } = await supabase
    .from('buddychip_followed_accounts')
    .select('id, twitter_handle')
    .eq('id', id)
    .eq('user_id', user.id)
    .maybeSingle();

  if (checkError) {
    logger.error('Error checking for existing account', checkError);
    throw new DatabaseError(
      'Failed to check for existing account',
      'CHECK_ACCOUNT_FAILED',
      checkError
    );
  }

  if (!existingAccount) {
    logger.warn('Account not found or not authorized', { accountId: id });
    throw new NotFoundError(
      'Account not found or not authorized',
      'ACCOUNT_NOT_FOUND',
      { accountId: id }
    );
  }

  // Remove the account
  const { error: deleteError } = await supabase
    .from('buddychip_followed_accounts')
    .delete()
    .eq('id', id)
    .eq('user_id', user.id);

  if (deleteError) {
    logger.error('Error removing followed account', deleteError);
    throw new DatabaseError(
      'Failed to remove followed account',
      'REMOVE_ACCOUNT_FAILED',
      deleteError
    );
  }

  logger.info('Followed account removed successfully', {
    userId: user.id,
    accountId: id,
    twitterHandle: existingAccount.twitter_handle
  });

  return NextResponse.json({
    message: 'Account removed successfully',
    accountId: id
  });
});
