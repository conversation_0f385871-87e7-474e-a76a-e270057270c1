import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { ensureProfile } from '@/lib/utils/ensure-profile';

// GET handler to fetch all followed accounts for the authenticated user
export async function GET() {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure profile exists
    await ensureProfile(supabase, user.id);

    // Fetch followed accounts for the user
    const { data: accounts, error: accountsError } = await supabase
      .from('buddychip_followed_accounts')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (accountsError) {
      console.error('Error fetching accounts:', accountsError);
      return NextResponse.json(
        { error: 'Failed to fetch accounts' },
        { status: 500 }
      );
    }

    return NextResponse.json({ accounts });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// POST handler to add a new followed account
export async function POST(_request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { twitter_handle } = body;

    if (!twitter_handle) {
      return NextResponse.json(
        { error: 'Twitter handle is required' },
        { status: 400 }
      );
    }

    // Clean the handle (remove @ if present)
    const cleanHandle = twitter_handle.startsWith('@')
      ? twitter_handle.substring(1)
      : twitter_handle;

    // Ensure profile exists
    await ensureProfile(supabase, user.id);

    // Check if the account is already followed
    const { data: existingAccount, error: checkError } = await supabase
      .from('buddychip_followed_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('twitter_handle', cleanHandle)
      .maybeSingle();

    if (checkError) {
      console.error('Error checking existing account:', checkError);
      return NextResponse.json(
        { error: 'Failed to check existing account' },
        { status: 500 }
      );
    }

    if (existingAccount) {
      return NextResponse.json(
        { error: 'You are already following this account' },
        { status: 400 }
      );
    }

    // Insert the new followed account
    // Note: In a production app, you might want to verify the Twitter handle exists
    // by making a call to the Twitter API before inserting
    const { data: newAccount, error: insertError } = await supabase
      .from('buddychip_followed_accounts')
      .insert({
        user_id: user.id,
        twitter_handle: cleanHandle,
        // twitter_user_id will be populated later when we fetch tweets
      })
      .select()
      .single();

    if (insertError) {
      console.error('Error inserting account:', insertError);
      return NextResponse.json(
        { error: 'Failed to add account' },
        { status: 500 }
      );
    }

    return NextResponse.json({ account: newAccount }, { status: 201 });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// DELETE handler to remove a followed account
export async function DELETE(_request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the account ID from the URL
    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Account ID is required' },
        { status: 400 }
      );
    }

    // Delete the account
    const { error: deleteError } = await supabase
      .from('buddychip_followed_accounts')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id); // Ensure the account belongs to the user

    if (deleteError) {
      console.error('Error deleting account:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete account' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}