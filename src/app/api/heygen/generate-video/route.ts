import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { heygenService } from '@/lib/heygen/heygen-service';
import { logger } from '@/lib/utils/logger';
import { z } from 'zod';

const heygenLogger = logger.child({ component: 'HeyGenAPI' });

// Request validation schema
const generateVideoSchema = z.object({
  tweet_id: z.string().min(1).optional(),
  script: z.string().min(1).optional(),
  avatar_id: z.string().optional(),
  voice_id: z.string().optional(),
  style: z.enum(['professional', 'casual', 'energetic']).optional(),
  background: z.string().optional(),
  ratio: z.enum(['16:9', '9:16', '1:1']).optional(),
}).refine(data => data.tweet_id || data.script, {
  message: "Either tweet_id or script must be provided",
});



// POST /api/heygen/generate-video - Generate video
export async function POST(_request: NextRequest) {
  const requestId = `heygen-generate-${Date.now()}`;
  heygenLogger.info('HeyGen video generation request received', { requestId });

  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = generateVideoSchema.parse(body);

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      heygenLogger.warn('Unauthorized HeyGen video generation attempt', { requestId });
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    heygenLogger.info('Processing HeyGen video generation request', {
      userId: user.id,
      tweetId: validatedData.tweet_id,
      hasScript: !!validatedData.script,
      requestId
    });

    let script = validatedData.script;

    // If tweet_id is provided, fetch the tweet content
    if (validatedData.tweet_id) {
      const { data: tweet, error: tweetError } = await supabase
        .from('buddychip_tweets')
        .select('content, author_handle')
        .eq('id', validatedData.tweet_id)
        .eq('user_id', user.id)
        .single();

      if (tweetError || !tweet) {
        heygenLogger.warn('Tweet not found for video generation', {
          userId: user.id,
          tweetId: validatedData.tweet_id,
          requestId
        });
        return NextResponse.json(
          { error: 'Tweet not found' },
          { status: 404 }
        );
      }

      script = tweet.content;
    }

    if (!script) {
      return NextResponse.json(
        { error: 'No script content available' },
        { status: 400 }
      );
    }

    // Generate video using HeyGen service
    const videoResult = await heygenService.generateViralVideo(script, {
      avatarId: validatedData.avatar_id,
      voiceId: validatedData.voice_id,
      style: validatedData.style || 'casual',
      background: validatedData.background,
      ratio: validatedData.ratio || '16:9',
    });

    // Store video generation record in database
    const { error: insertError } = await supabase
      .from('buddychip_generated_videos')
      .insert({
        user_id: user.id,
        tweet_id: validatedData.tweet_id,
        video_id: videoResult.video_id,
        script,
        avatar_id: validatedData.avatar_id,
        voice_id: validatedData.voice_id,
        style: validatedData.style || 'casual',
        status: videoResult.status,
        generation_params: {
          background: validatedData.background,
          ratio: validatedData.ratio || '16:9',
          generated_at: new Date().toISOString(),
        },
      })
      .select()
      .single();

    if (insertError) {
      heygenLogger.error('Error storing video generation record', {
        userId: user.id,
        videoId: videoResult.video_id,
        insertError,
        requestId
      });
      // Continue anyway, as the video generation was successful
    }

    heygenLogger.info('HeyGen video generation initiated successfully', {
      userId: user.id,
      videoId: videoResult.video_id,
      status: videoResult.status,
      requestId
    });

    return NextResponse.json({
      success: true,
      video_id: videoResult.video_id,
      status: videoResult.status,
      script,
      estimated_duration: Math.ceil(script.length / 10), // Rough estimate: 10 chars per second
    });

  } catch (error) {
    if (error instanceof z.ZodError) {
      heygenLogger.warn('Invalid request data', { error: error.errors, requestId });
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      );
    }

    heygenLogger.error('Unexpected error in HeyGen video generation', { error, requestId });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/heygen/generate-video?video_id=xxx - Check video status
export async function GET(_request: NextRequest) {
  const requestId = `heygen-status-${Date.now()}`;
  const { searchParams } = new URL(request.url);
  const videoId = searchParams.get('video_id');

  heygenLogger.info('HeyGen video status request received', { requestId, videoId });

  try {
    if (!videoId) {
      return NextResponse.json(
        { error: 'video_id parameter is required' },
        { status: 400 }
      );
    }

    // Get authenticated user
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      heygenLogger.warn('Unauthorized HeyGen video status attempt', { requestId });
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Verify user owns this video
    const { data: videoRecord, error: videoError } = await supabase
      .from('buddychip_generated_videos')
      .select('*')
      .eq('video_id', videoId)
      .eq('user_id', user.id)
      .single();

    if (videoError || !videoRecord) {
      heygenLogger.warn('Video not found or unauthorized', {
        userId: user.id,
        videoId,
        requestId
      });
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    // Get video status from HeyGen
    const videoStatus = await heygenService.getVideoStatus(videoId);

    // Update database with latest status
    if (videoStatus.status !== videoRecord.status) {
      const updateData: Record<string, unknown> = {
        status: videoStatus.status,
        updated_at: new Date().toISOString(),
      };

      if (videoStatus.video_url) {
        updateData.video_url = videoStatus.video_url;
      }
      if (videoStatus.thumbnail_url) {
        updateData.thumbnail_url = videoStatus.thumbnail_url;
      }
      if (videoStatus.duration) {
        updateData.duration = videoStatus.duration;
      }
      if (videoStatus.error) {
        updateData.error_message = videoStatus.error;
      }

      await supabase
        .from('buddychip_generated_videos')
        .update(updateData)
        .eq('video_id', videoId)
        .eq('user_id', user.id);
    }

    heygenLogger.info('HeyGen video status retrieved', {
      userId: user.id,
      videoId,
      status: videoStatus.status,
      requestId
    });

    return NextResponse.json({
      video_id: videoId,
      status: videoStatus.status,
      video_url: videoStatus.video_url,
      thumbnail_url: videoStatus.thumbnail_url,
      duration: videoStatus.duration,
      error: videoStatus.error,
      script: videoRecord.script,
      created_at: videoRecord.created_at,
    });

  } catch (error) {
    heygenLogger.error('Unexpected error in HeyGen video status', { error, requestId });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
