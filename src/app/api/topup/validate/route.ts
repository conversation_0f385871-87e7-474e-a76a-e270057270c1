import { NextRequest, NextResponse } from 'next/server';
import { PublicKey } from '@solana/web3.js';
import { validateTopUpRequest, getTopUpLimits, getRecipientAddress } from '@/lib/solana/topup-service';
import { createSupabaseServerClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  console.log('🔍 Top-up validation request received');

  try {
    // Check authentication
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('❌ Authentication failed:', authError);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { walletAddress, amount } = body;

    console.log('📝 Validation request data:', {
      userId: user.id,
      walletAddress,
      amount,
    });

    // Validate input
    if (!walletAddress || typeof amount !== 'number') {
      return NextResponse.json(
        { error: 'Wallet address and amount are required' },
        { status: 400 }
      );
    }

    // Validate wallet address format
    let userWallet: PublicKey;
    try {
      userWallet = new PublicKey(walletAddress);
    } catch (error) {
      console.error('❌ Invalid wallet address:', error);
      return NextResponse.json(
        { error: 'Invalid wallet address format' },
        { status: 400 }
      );
    }

    // Validate the top-up request
    const validation = await validateTopUpRequest(userWallet, amount);

    console.log('✅ Validation result:', validation);

    // Get additional info
    const limits = getTopUpLimits();
    const recipientAddress = getRecipientAddress();

    return NextResponse.json({
      validation,
      limits,
      recipientAddress,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('❌ Top-up validation error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  console.log('📊 Top-up info request received');

  try {
    // Check authentication
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('❌ Authentication failed:', authError);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get top-up configuration
    const limits = getTopUpLimits();
    const recipientAddress = getRecipientAddress();

    console.log('📊 Top-up configuration:', {
      limits,
      recipientAddress: `${recipientAddress.slice(0, 8)}...${recipientAddress.slice(-8)}`,
    });

    return NextResponse.json({
      limits,
      recipientAddress,
      predefinedAmounts: [10, 50, 100, 500, 1000],
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('❌ Top-up info error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
