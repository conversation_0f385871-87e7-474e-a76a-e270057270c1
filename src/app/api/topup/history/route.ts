import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';

export async function GET(_request: NextRequest) {
  console.log('📊 Top-up history request received');

  try {
    // Check authentication
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('❌ Authentication failed:', authError);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    console.log('📊 Fetching top-up history:', {
      userId: user.id,
      limit,
      offset,
    });

    // Fetch top-up transactions
    const { data: transactions, error: transactionError } = await supabase
      .from('buddychip_token_transactions')
      .select(`
        id,
        amount,
        description,
        action_type,
        action_metadata,
        created_at,
        updated_at
      `)
      .eq('user_id', user.id)
      .eq('action_type', 'topup_deposit')
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (transactionError) {
      console.error('❌ Error fetching transactions:', transactionError);
      return NextResponse.json(
        { error: 'Failed to fetch transaction history' },
        { status: 500 }
      );
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('buddychip_token_transactions')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq('action_type', 'topup_deposit');

    if (countError) {
      console.error('❌ Error fetching transaction count:', countError);
      return NextResponse.json(
        { error: 'Failed to fetch transaction count' },
        { status: 500 }
      );
    }

    // Format transactions for frontend
    const formattedTransactions = transactions?.map(transaction => {
      const metadata = transaction.action_metadata?.topup || {};
      
      return {
        id: transaction.id,
        amount: parseFloat(transaction.amount || '0'),
        description: transaction.description,
        transactionSignature: metadata.transaction_signature,
        recipientAddress: metadata.recipient_address,
        network: metadata.network || 'devnet',
        confirmationStatus: metadata.confirmation_status || 'confirmed',
        grossAmount: metadata.gross_amount,
        feeAmount: metadata.fee_amount,
        netAmount: metadata.net_amount,
        createdAt: transaction.created_at,
        updatedAt: transaction.updated_at,
      };
    }) || [];

    // Get user's current balance
    const { data: balanceData, error: balanceError } = await supabase
      .from('buddychip_copy_tokens')
      .select('balance, total_earned, total_spent')
      .eq('user_id', user.id)
      .single();

    if (balanceError && balanceError.code !== 'PGRST116') {
      console.error('❌ Error fetching balance:', balanceError);
    }

    const currentBalance = balanceData ? {
      balance: parseFloat(balanceData.balance || '0'),
      totalEarned: parseFloat(balanceData.total_earned || '0'),
      totalSpent: parseFloat(balanceData.total_spent || '0'),
    } : {
      balance: 0,
      totalEarned: 0,
      totalSpent: 0,
    };

    console.log('✅ Top-up history fetched successfully:', {
      userId: user.id,
      transactionCount: formattedTransactions.length,
      totalCount: count,
    });

    return NextResponse.json({
      transactions: formattedTransactions,
      pagination: {
        limit,
        offset,
        total: count || 0,
        hasMore: (offset + limit) < (count || 0),
      },
      balance: currentBalance,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('❌ Top-up history error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
