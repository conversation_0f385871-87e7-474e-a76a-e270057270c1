import { NextRequest, NextResponse } from 'next/server';
import { PublicKey } from '@solana/web3.js';
import { verifyTopUpTransaction, getRecipientAddress, calculateTopUpFee } from '@/lib/solana/topup-service';
import { createSupabaseServerClient } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  console.log('📝 Top-up transaction recording request received');

  try {
    // Check authentication
    const supabase = await createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.error('❌ Authentication failed:', authError);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { 
      transactionSignature, 
      amount, 
      walletAddress, 
      network = 'devnet' 
    } = body;

    console.log('📝 Recording transaction:', {
      userId: user.id,
      transactionSignature,
      amount,
      walletAddress,
      network,
    });

    // Validate input
    if (!transactionSignature || !amount || !walletAddress) {
      return NextResponse.json(
        { error: 'Transaction signature, amount, and wallet address are required' },
        { status: 400 }
      );
    }

    // Validate wallet address format
    let userWallet: PublicKey;
    try {
      userWallet = new PublicKey(walletAddress);
    } catch (error) {
      console.error('❌ Invalid wallet address:', error);
      return NextResponse.json(
        { error: 'Invalid wallet address format' },
        { status: 400 }
      );
    }

    // Verify the transaction
    const isVerified = await verifyTopUpTransaction(
      transactionSignature,
      amount,
      userWallet
    );

    if (!isVerified) {
      console.error('❌ Transaction verification failed');
      return NextResponse.json(
        { error: 'Transaction verification failed' },
        { status: 400 }
      );
    }

    // Calculate fee
    const fee = calculateTopUpFee(amount);
    const netAmount = amount - fee;

    // Record the transaction in database
    const { error: transactionError } = await supabase
      .from('buddychip_token_transactions')
      .insert({
        user_id: user.id,
        transaction_type: 'topup',
        amount: netAmount,
        description: `Top-up deposit of ${amount} $COPY (fee: ${fee} $COPY)`,
        action_type: 'topup_deposit',
        action_metadata: {
          topup: {
            recipient_address: getRecipientAddress(),
            transaction_signature: transactionSignature,
            network: network,
            confirmation_status: 'confirmed',
            gross_amount: amount,
            fee_amount: fee,
            net_amount: netAmount,
          }
        }
      });

    if (transactionError) {
      console.error('❌ Error recording transaction:', transactionError);
      return NextResponse.json(
        { error: 'Failed to record transaction' },
        { status: 500 }
      );
    }

    // Update user's token balance
    const { data: currentBalance, error: balanceError } = await supabase
      .from('buddychip_copy_tokens')
      .select('balance, total_earned')
      .eq('user_id', user.id)
      .single();

    if (balanceError && balanceError.code !== 'PGRST116') {
      console.error('❌ Error fetching current balance:', balanceError);
      return NextResponse.json(
        { error: 'Failed to update balance' },
        { status: 500 }
      );
    }

    // Calculate new balance
    const currentBalanceAmount = currentBalance ? parseFloat(currentBalance.balance || '0') : 0;
    const currentEarned = currentBalance ? parseFloat(currentBalance.total_earned || '0') : 0;
    const newBalance = currentBalanceAmount + netAmount;
    const newEarned = currentEarned + netAmount;

    // Update or insert balance record
    const { error: updateError } = await supabase
      .from('buddychip_copy_tokens')
      .upsert({
        user_id: user.id,
        balance: newBalance,
        total_earned: newEarned,
        wallet_address: walletAddress,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id'
      });

    if (updateError) {
      console.error('❌ Error updating balance:', updateError);
      return NextResponse.json(
        { error: 'Failed to update balance' },
        { status: 500 }
      );
    }

    console.log('✅ Top-up transaction recorded successfully:', {
      userId: user.id,
      transactionSignature,
      netAmount,
      newBalance,
    });

    return NextResponse.json({
      success: true,
      transactionSignature,
      amount: netAmount,
      fee,
      newBalance,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('❌ Top-up recording error:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
