import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import Sentiment from 'sentiment';
import { ensureProfile } from '@/lib/utils/ensure-profile';

// Initialize sentiment analyzer
const sentiment = new Sentiment();

// POST handler to filter and categorize tweets
export async function POST(_request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient();

    // Get the authenticated user
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Ensure profile exists
    await ensureProfile(supabase, user.id);

    // Get request body
    const body = await request.json();
    const { tweet_ids } = body;

    if (!tweet_ids || !Array.isArray(tweet_ids) || tweet_ids.length === 0) {
      return NextResponse.json(
        { error: 'Tweet IDs are required' },
        { status: 400 }
      );
    }

    // Fetch the tweets to filter
    const { data: tweets, error: tweetsError } = await supabase
      .from('buddychip_tweets')
      .select('*')
      .eq('user_id', user.id)
      .in('id', tweet_ids);

    if (tweetsError) {
      return NextResponse.json(
        { error: 'Failed to fetch tweets' },
        { status: 500 }
      );
    }

    if (!tweets || tweets.length === 0) {
      return NextResponse.json(
        { message: 'No tweets found to filter' },
        { status: 200 }
      );
    }

    // Fetch user's categories and keywords
    const { data: categories, error: categoriesError } = await supabase
      .from('buddychip_categories')
      .select('id, name, buddychip_category_keywords(keyword)')
      .eq('user_id', user.id);

    if (categoriesError) {
      return NextResponse.json(
        { error: 'Failed to fetch categories' },
        { status: 500 }
      );
    }

    // Process each tweet
    const results = await Promise.all(
      tweets.map(async (tweet) => {
        // Calculate sentiment score
        const sentimentResult = sentiment.analyze(tweet.content);
        const sentimentScore = sentimentResult.comparative; // Normalized score

        // Calculate importance score (simple algorithm)
        // In a real app, this would be more sophisticated
        const importanceScore = calculateImportanceScore(tweet, sentimentResult);

        // Find matching categories based on keywords
        const matchingCategories = findMatchingCategories(tweet.content, categories);

        // Update the tweet with sentiment and importance scores
        const { error: updateError } = await supabase
          .from('buddychip_tweets')
          .update({
            sentiment_score: sentimentScore,
            importance_score: importanceScore
          })
          .eq('id', tweet.id)
          .eq('user_id', user.id);

        if (updateError) {
          console.error('Error updating tweet:', updateError);
          return {
            tweet_id: tweet.id,
            success: false,
            error: 'Failed to update tweet'
          };
        }

        // Add tweet to matching categories
        if (matchingCategories.length > 0) {
          const categoryAssignments = matchingCategories.map(categoryId => ({
            tweet_db_id: tweet.id,
            category_id: categoryId,
            user_id: user.id
          }));

          const { error: assignError } = await supabase
            .from('buddychip_tweet_categories')
            .upsert(categoryAssignments, { onConflict: 'user_id,tweet_db_id,category_id' });

          if (assignError) {
            console.error('Error assigning categories:', assignError);
          }
        }

        return {
          tweet_id: tweet.id,
          success: true,
          sentiment_score: sentimentScore,
          importance_score: importanceScore,
          categories: matchingCategories
        };
      })
    );

    return NextResponse.json({
      message: `Processed ${results.length} tweets`,
      results
    });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

// Helper function to calculate importance score
function calculateImportanceScore(tweet: { content: string; raw_data?: { public_metrics?: { like_count?: number; retweet_count?: number; reply_count?: number; quote_count?: number } } }, sentimentResult: { comparative: number }) {
  // This is a simple algorithm that could be improved
  // Factors that might contribute to importance:
  // 1. Absolute sentiment (very positive or very negative tweets might be important)
  // 2. Presence of question marks (questions might need responses)
  // 3. Mentions of the user
  // 4. Presence of important keywords
  // 5. Engagement metrics (likes, retweets, etc.)

  let score = 5; // Base score out of 10

  // Factor 1: Sentiment intensity
  const sentimentIntensity = Math.abs(sentimentResult.comparative);
  score += sentimentIntensity * 2; // Add up to 2 points for strong sentiment

  // Factor 2: Questions
  if (tweet.content.includes('?')) {
    score += 1;
  }

  // Factor 3: Engagement metrics
  if (tweet.raw_data?.public_metrics) {
    const metrics = tweet.raw_data.public_metrics;
    const engagement = (
      (metrics.like_count || 0) * 0.5 +
      (metrics.retweet_count || 0) * 1.0 +
      (metrics.reply_count || 0) * 1.5 +
      (metrics.quote_count || 0) * 1.0
    ) / 100; // Normalize

    score += Math.min(engagement, 2); // Add up to 2 points for engagement
  }

  // Ensure score is between 0 and 10
  return Math.max(0, Math.min(10, score));
}

// Helper function to find matching categories based on keywords
function findMatchingCategories(content: string, categories: { id: number; category_keywords?: { keyword: string }[] }[]) {
  const lowerContent = content.toLowerCase();
  const matchingCategoryIds: number[] = [];

  categories.forEach(category => {
    const keywords = category.category_keywords || [];

    // Check if any keyword matches
    const hasMatch = keywords.some((keywordObj: { keyword: string }) => {
      const keyword = keywordObj.keyword.toLowerCase();
      return lowerContent.includes(keyword);
    });

    if (hasMatch) {
      matchingCategoryIds.push(category.id);
    }
  });

  return matchingCategoryIds;
}