/**
 * API Route: /api/filtering
 *
 * Handles filtering and categorizing tweets.
 */

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import Sentiment from 'sentiment';
import { ensureProfile } from '@/lib/utils/ensure-profile';
import { withErrorHandling } from '@/lib/utils/error-handler';
import { AuthenticationError, DatabaseError } from '@/lib/utils/errors';
import { createLogger } from '@/lib/utils/logger';
import { validateData } from '@/lib/utils/validation';
import { z } from 'zod';

// Create a route-specific logger
const routeLogger = createLogger({ component: 'api-filtering' });

// Initialize sentiment analyzer
const sentiment = new Sentiment();

// Schema for tweet IDs
const tweetIdsSchema = z.object({
  tweet_ids: z.array(z.string()).min(1, 'At least one tweet ID is required')
});

/**
 * POST handler to filter and categorize tweets
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  // Create a request-specific logger with a unique request ID
  const requestId = request.headers.get('x-request-id') || `req-${Date.now()}`;
  const logger = routeLogger.child({
    requestId,
    method: 'POST',
    path: '/api/filtering'
  });

  logger.info('Received request to filter and categorize tweets');

  const supabase = await createSupabaseServerClient();

  // Get the authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    logger.warn('Authentication failed', { error: userError });
    throw new AuthenticationError('You must be logged in to filter tweets');
  }

  // Ensure profile exists
  await ensureProfile(supabase, user.id);

  // Get and validate request body
  const body = await request.json();
  const { tweet_ids } = validateData(
    tweetIdsSchema,
    body,
    'Invalid filtering request'
  );

  logger.info('Filtering tweets', {
    userId: user.id,
    tweetCount: tweet_ids.length
  });

  // Fetch the tweets to filter
  const { data: tweets, error: tweetsError } = await supabase
    .from('buddychip_tweets')
    .select('*')
    .eq('user_id', user.id)
    .in('id', tweet_ids);

  if (tweetsError) {
    logger.error('Error fetching tweets', tweetsError);
    throw new DatabaseError(
      'Failed to fetch tweets',
      'FETCH_TWEETS_FAILED',
      tweetsError
    );
  }

  if (!tweets || tweets.length === 0) {
    logger.info('No tweets found to filter', {
      userId: user.id,
      requestedIds: tweet_ids
    });

    return NextResponse.json({
      message: 'No tweets found to filter',
      results: []
    });
  }

  // Fetch user's categories and keywords
  const { data: categories, error: categoriesError } = await supabase
    .from('buddychip_categories')
    .select('id, name, buddychip_category_keywords(keyword)')
    .eq('user_id', user.id);

  if (categoriesError) {
    logger.error('Error fetching categories', categoriesError);
    throw new DatabaseError(
      'Failed to fetch categories',
      'FETCH_CATEGORIES_FAILED',
      categoriesError
    );
  }

  logger.debug('Processing tweets', {
    tweetCount: tweets.length,
    categoryCount: categories?.length || 0
  });

  // Process each tweet
  const results = await Promise.all(
    tweets.map(async (tweet) => {
      // Calculate sentiment score
      const sentimentResult = sentiment.analyze(tweet.content);
      const sentimentScore = sentimentResult.comparative; // Normalized score

      // Calculate importance score
      const importanceScore = calculateImportanceScore(tweet, sentimentResult);

      // Find matching categories based on keywords
      const matchingCategories = findMatchingCategories(tweet.content, categories || []);

      logger.debug('Tweet processed', {
        tweetId: tweet.id,
        sentimentScore,
        importanceScore,
        matchingCategoryCount: matchingCategories.length
      });

      // Update the tweet with sentiment and importance scores
      const { error: updateError } = await supabase
        .from('buddychip_tweets')
        .update({
          sentiment_score: sentimentScore,
          importance_score: importanceScore
        })
        .eq('id', tweet.id)
        .eq('user_id', user.id);

      if (updateError) {
        logger.error('Error updating tweet', {
          tweetId: tweet.id,
          error: updateError
        });

        return {
          tweet_id: tweet.id,
          success: false,
          error: 'Failed to update tweet'
        };
      }

      // Add tweet to matching categories
      if (matchingCategories.length > 0) {
        const categoryAssignments = matchingCategories.map(categoryId => ({
          tweet_db_id: tweet.id,
          category_id: categoryId,
          user_id: user.id
        }));

        const { error: assignError } = await supabase
          .from('buddychip_tweet_categories')
          .upsert(categoryAssignments, { onConflict: 'user_id,tweet_db_id,category_id' });

        if (assignError) {
          logger.error('Error assigning categories', {
            tweetId: tweet.id,
            error: assignError
          });
        }
      }

      return {
        tweet_id: tweet.id,
        success: true,
        sentiment_score: sentimentScore,
        importance_score: importanceScore,
        categories: matchingCategories
      };
    })
  );

  logger.info('Tweet filtering completed', {
    userId: user.id,
    processedCount: results.length,
    successCount: results.filter(r => r.success).length
  });

  return NextResponse.json({
    message: `Processed ${results.length} tweets`,
    results
  });
});

/**
 * Helper function to calculate importance score
 */
function calculateImportanceScore(tweet: { content: string; metrics?: { likes?: number; retweets?: number; replies?: number; quotes?: number } }, sentimentResult: { comparative: number }) {
  // This is a simple algorithm that could be improved
  // Factors that might contribute to importance:
  // 1. Absolute sentiment (very positive or very negative tweets might be important)
  // 2. Presence of question marks (questions might need responses)
  // 3. Mentions of the user
  // 4. Presence of important keywords
  // 5. Engagement metrics (likes, retweets, etc.)

  let score = 5; // Base score out of 10

  // Factor 1: Sentiment intensity
  const sentimentIntensity = Math.abs(sentimentResult.comparative);
  score += sentimentIntensity * 2; // Add up to 2 points for strong sentiment

  // Factor 2: Questions
  if (tweet.content.includes('?')) {
    score += 1;
  }

  // Factor 3: Engagement metrics
  if (tweet.metrics) {
    const metrics = tweet.metrics;
    const engagement = (
      (metrics.likes || 0) * 0.5 +
      (metrics.retweets || 0) * 1.0 +
      (metrics.replies || 0) * 1.5 +
      (metrics.quotes || 0) * 1.0
    ) / 100; // Normalize

    score += Math.min(engagement, 2); // Add up to 2 points for engagement
  }

  // Ensure score is between 0 and 10
  return Math.max(0, Math.min(10, score));
}

/**
 * Helper function to find matching categories based on keywords
 */
function findMatchingCategories(content: string, categories: { id: number; buddychip_category_keywords?: { keyword: string }[] }[]) {
  const lowerContent = content.toLowerCase();
  const matchingCategoryIds: number[] = [];

  categories.forEach(category => {
    const keywords = category.buddychip_category_keywords || [];

    // Check if any keyword matches
    const hasMatch = keywords.some((keywordObj: { keyword: string }) => {
      const keyword = keywordObj.keyword.toLowerCase();
      return lowerContent.includes(keyword);
    });

    if (hasMatch) {
      matchingCategoryIds.push(category.id);
    }
  });

  return matchingCategoryIds;
}
