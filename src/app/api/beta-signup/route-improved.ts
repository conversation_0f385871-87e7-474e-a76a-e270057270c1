/**
 * API Route: /api/beta-signup
 *
 * Handles beta signup requests.
 */

import { createSupabaseRouteHandlerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { withErrorHandling } from '@/lib/utils/error-handler';
import { DatabaseError } from '@/lib/utils/errors';
import { createLogger } from '@/lib/utils/logger';
import { validateData } from '@/lib/utils/validation';
import { z } from 'zod';

// Create a route-specific logger
const routeLogger = createLogger({ component: 'api-beta-signup' });

// Schema for email validation
const emailSchema = z.object({
  email: z.string().email('Valid email is required')
});

/**
 * POST handler to sign up for beta access
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  // Create a request-specific logger with a unique request ID
  const requestId = request.headers.get('x-request-id') || `req-${Date.now()}`;
  const logger = routeLogger.child({
    requestId,
    method: 'POST',
    path: '/api/beta-signup'
  });

  logger.info('Received beta signup request');

  const supabase = await createSupabaseRouteHandlerClient();

  // Get and validate request body
  const body = await request.json();
  const { email } = validateData(
    emailSchema,
    body,
    'Invalid email address'
  );

  logger.info('Processing beta signup', {
    email: email.substring(0, email.indexOf('@')) + '@...' // Log partial email for privacy
  });

  try {
    // Insert the email into the beta signups table
    const { data, error } = await supabase
      .from('buddychip_beta_signups')
      .insert([{ email: email }])
      .select();

    if (error) {
      logger.error('Error inserting beta signup', error);

      // Check for unique constraint violation (email already exists)
      if (error.code === '23505') { // PostgreSQL unique_violation error code
        logger.info('Email already signed up', {
          email: email.substring(0, email.indexOf('@')) + '@...'
        });

        return NextResponse.json(
          { message: 'This email is already signed up.' },
          { status: 409 }
        );
      }

      throw new DatabaseError(
        'Error signing up for beta',
        'BETA_SIGNUP_FAILED',
        error
      );
    }

    logger.info('Beta signup successful', {
      email: email.substring(0, email.indexOf('@')) + '@...'
    });

    return NextResponse.json(
      { message: `Thank you, ${email}! We'll be in touch.`, data },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof DatabaseError) {
      throw error;
    }

    logger.error('Unexpected error handling beta signup', error);
    throw new DatabaseError(
      'An unexpected error occurred during beta signup',
      'BETA_SIGNUP_UNEXPECTED_ERROR',
      error
    );
  }
});
