import { createSupabaseRouteHandlerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(_request: NextRequest) {
  const supabase = await createSupabaseRouteHandlerClient();
  let email: string;

  try {
    const body = await request.json();
    email = body.email;

    if (!email || !email.includes('@')) {
      return NextResponse.json({ message: 'Valid email is required.' }, { status: 400 });
    }
  } catch {
    return NextResponse.json({ message: 'Invalid request body. Email is required.' }, { status: 400 });
  }

  try {
    const { data, error } = await supabase
      .from('buddychip_beta_signups')
      .insert([{ email: email }])
      .select();

    if (error) {
      console.error('Supabase error inserting beta signup:', error);
      // Check for unique constraint violation (email already exists)
      if (error.code === '23505') { // PostgreSQL unique_violation error code
        return NextResponse.json({ message: 'This email is already signed up.' }, { status: 409 });
      }
      return NextResponse.json({ message: 'Error signing up for beta. Please try again.' }, { status: 500 });
    }

    return NextResponse.json({ message: `Thank you, ${email}! We'll be in touch.`, data }, { status: 201 });
  } catch (error) {
    console.error('Unexpected error handling beta signup:', error);
    return NextResponse.json({ message: 'An unexpected error occurred. Please try again.' }, { status: 500 });
  }
}