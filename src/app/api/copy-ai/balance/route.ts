import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { getOnChainTokenBalance, getUserTokenBalance } from '@/lib/web3/token-service';
import { logger } from '@/lib/utils/logger';

const balanceLogger = logger.child({ component: 'CopyAIBalance' });

export async function GET(_request: NextRequest) {
  try {
    console.log('🪙 GET /api/copy-ai/balance - Fetching token balance');

    const supabase = await createSupabaseServerClient();

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      console.log('❌ Authentication failed');
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get network from query params
    const { searchParams } = new URL(request.url);
    const network = searchParams.get('network') || 'devnet';
    const walletAddress = searchParams.get('walletAddress');

    console.log('🪙 Balance request:', {
      userId: user.id,
      network,
      walletAddress,
    });

    // Get database balance
    const dbBalance = await getUserTokenBalance(user.id);

    let onChainBalance = null;

    // If wallet address is provided, get on-chain balance
    if (walletAddress) {
      try {
        console.log('🪙 Fetching on-chain balance for wallet:', walletAddress, 'on network:', network);

        // Also fetch all token accounts to see what tokens the wallet has
        const { getTokenAccounts } = await import('@/lib/solana/token-utils');
        const { PublicKey } = await import('@solana/web3.js');

        try {
          const publicKey = new PublicKey(walletAddress);
          const allTokenAccounts = await getTokenAccounts(publicKey);
          console.log('🪙 All token accounts for wallet:', allTokenAccounts);
        } catch (tokenAccountsError) {
          console.error('❌ Error fetching all token accounts:', tokenAccountsError);
        }

        onChainBalance = await getOnChainTokenBalance(walletAddress, network);
        console.log('🪙 On-chain balance fetched:', onChainBalance);

        // If no tokens found on current network and it's testnet, also check mainnet
        if (network === 'testnet' && onChainBalance?.copyTokenBalance === 0) {
          console.log('🪙 No tokens found on testnet, checking mainnet...');
          try {
            const mainnetBalance = await getOnChainTokenBalance(walletAddress, 'mainnet-beta');
            console.log('🪙 Mainnet balance:', mainnetBalance);
            if (mainnetBalance?.copyTokenBalance > 0) {
              console.log('✅ Found tokens on mainnet! Consider switching networks.');
            }
          } catch (mainnetError) {
            console.log('❌ Error checking mainnet:', mainnetError);
          }
        }
      } catch (error) {
        console.error('❌ Error fetching on-chain balance:', error);
        balanceLogger.error('Error fetching on-chain balance', {
          userId: user.id,
          walletAddress,
          network,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
        // Don't fail the request if on-chain balance fails
        onChainBalance = null;
      }
    } else {
      console.log('🪙 No wallet address provided, skipping on-chain balance fetch');
    }

    const response = {
      database: dbBalance,
      onChain: onChainBalance,
      network,
      walletAddress,
    };

    console.log('🪙 Balance response:', response);

    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ Error in GET /api/copy-ai/balance:', error);
    balanceLogger.error('Error in GET /api/copy-ai/balance', { error });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
