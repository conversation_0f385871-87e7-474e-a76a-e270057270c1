@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;

  /* Palette from docs/palette.md */
  --background: #ffeffe; /* Background: #ffeffe */
  --foreground: #094067; /* Headline: #094067 */

  --card: #ffeffe; /* Main background color */
  --card-foreground: #094067; /* Headline color */

  --popover: #ffeffe; /* Main background color */
  --popover-foreground: #094067; /* Headline color */

  --primary: #3da9fc; /* Button: #3da9fc */
  --primary-foreground: #ffeffe; /* Button text: #ffeffe */

  --secondary: #90b4ce; /* Illustration Secondary: #90b4ce */
  --secondary-foreground: #094067; /* Headline color for contrast */

  --muted: #f0f4f8; /* Lighter version of background */
  --muted-foreground: #5f6c7b; /* Paragraph: #5f6c7b */

  --accent: #3da9fc; /* Illustration Highlight: #3da9fc */
  --accent-foreground: #ffeffe; /* Button text color for contrast */

  --destructive: #ef4565; /* Illustration Tertiary: #ef4565 */
  --destructive-foreground: #ffeffe; /* Light text on destructive background */

  --border: #094067; /* Illustration Stroke: #094067 */
  --input: #90b4ce; /* Illustration Secondary for input borders */
  --ring: #3da9fc; /* Illustration Highlight for focus rings */
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  /* Dark mode palette that complements the light mode palette */
  --background: #0f172a; /* Dark blue-gray background */
  --foreground: #f8fafc; /* Light text for dark background */

  --card: #1e293b; /* Slightly lighter than background */
  --card-foreground: #f8fafc; /* Light text for dark background */

  --popover: #1e293b; /* Same as card */
  --popover-foreground: #f8fafc; /* Light text for dark background */

  --primary: #60cdff; /* Brighter blue for primary elements */
  --primary-foreground: #0f172a; /* Dark text on bright primary */

  --secondary: #64748b; /* Muted blue-gray */
  --secondary-foreground: #f8fafc; /* Light text on secondary */

  --muted: #334155; /* Muted background */
  --muted-foreground: #94a3b8; /* Muted text */

  --accent: #60cdff; /* Same as primary for consistency */
  --accent-foreground: #0f172a; /* Dark text on accent */

  --destructive: #f43f5e; /* Bright red, similar to light mode */
  --destructive-foreground: #f8fafc; /* Light text on destructive */

  --border: #475569; /* Subtle border color */
  --input: #475569; /* Same as border */
  --ring: #60cdff; /* Same as primary for focus rings */

  /* Chart colors */
  --chart-1: #60cdff; /* Brighter primary blue */
  --chart-2: #a3e635; /* Lime */
  --chart-3: #fb7185; /* Pink */
  --chart-4: #fbbf24; /* Amber */
  --chart-5: #a78bfa; /* Purple */

  /* Sidebar colors */
  --sidebar: #1e293b; /* Same as card */
  --sidebar-foreground: #f8fafc; /* Light text */
  --sidebar-primary: #60cdff; /* Primary blue */
  --sidebar-primary-foreground: #0f172a; /* Dark text on primary */
  --sidebar-accent: #334155; /* Muted background */
  --sidebar-accent-foreground: #f8fafc; /* Light text */
  --sidebar-border: #475569; /* Same as border */
  --sidebar-ring: #60cdff; /* Same as primary */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom glow effect for highlighted text */
.glow {
  text-shadow: 0 0 10px rgba(127, 221, 255, 0.7), 0 0 20px rgba(127, 221, 255, 0.5);
  animation: pulse 3s infinite alternate;
}

@keyframes pulse {
  0% {
    text-shadow: 0 0 10px rgba(127, 221, 255, 0.7), 0 0 20px rgba(127, 221, 255, 0.5);
  }
  100% {
    text-shadow: 0 0 15px rgba(127, 221, 255, 0.9), 0 0 30px rgba(127, 221, 255, 0.7);
  }
}

/* Custom scrollbar styles for chatbot */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.8);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.8);
}

/* Prose styling for markdown in chatbot */
.prose {
  color: inherit;
}

.prose p {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.prose p:last-child {
  margin-bottom: 0;
}

.prose ul, .prose ol {
  margin-top: 0;
  margin-bottom: 0.5rem;
  padding-left: 1rem;
}

.prose li {
  margin-top: 0;
  margin-bottom: 0.25rem;
}

.prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.prose code {
  font-size: 0.75rem;
  font-weight: 500;
}

.prose pre {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  overflow-x: auto;
}

.prose blockquote {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  font-style: italic;
}
