import { createSupabaseServerClient } from '@/lib/supabase/server';
import { redirect } from 'next/navigation';
import { ensureProfile } from '@/lib/utils/ensure-profile';
import UserPreferencesForm from '@/components/user-preferences-form';

export default async function PreferencesPage() {
  const supabase = await createSupabaseServerClient();

  // Get the authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  if (userError || !user) {
    redirect('/login');
  }

  // Ensure profile exists
  await ensureProfile(supabase, user.id);

  return (
    <div className="container max-w-4xl py-8">
      <h1 className="text-3xl font-bold mb-8">AI Reply Preferences</h1>

      <div className="grid gap-8">
        <UserPreferencesForm />

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">How This Works</h2>
          <p className="mb-4">
            Buddy<PERSON><PERSON> uses AI to analyze tweets and generate personalized reply suggestions based on your preferences.
            The AI will consider:
          </p>
          <ul className="list-disc pl-6 space-y-2 mb-4">
            <li><strong>Reply Style:</strong> The overall style and approach you prefer for your replies</li>
            <li><strong>Interests & Topics:</strong> What you&apos;re knowledgeable about and interested in discussing</li>
            <li><strong>Preferred Tone:</strong> How formal, casual, or authoritative you want to sound</li>
            <li><strong>Custom System Prompt:</strong> Advanced users can provide specific instructions to the AI</li>
          </ul>
          <p>
            These preferences help the AI determine which tweets are worth replying to and how to craft replies that sound like you.
          </p>
        </div>
      </div>
    </div>
  );
}
