'use client';

import { useEffect, useRef } from 'react';

interface PerformanceMetrics {
  componentName: string;
  renderTime: number;
  mountTime: number;
  updateCount: number;
}

export function usePerformanceMonitor(componentName: string) {
  const renderStartTime = useRef<number>(0);
  const mountStartTime = useRef<number>(0);
  const updateCount = useRef<number>(0);
  const isMounted = useRef<boolean>(false);

  // Track render start time
  renderStartTime.current = performance.now();

  useEffect(() => {
    if (!isMounted.current) {
      // First mount
      mountStartTime.current = performance.now();
      isMounted.current = true;
      
      console.log(`🚀 ${componentName} mounted in ${(mountStartTime.current - renderStartTime.current).toFixed(2)}ms`);
    } else {
      // Update
      updateCount.current += 1;
      const updateTime = performance.now() - renderStartTime.current;
      
      if (updateTime > 16) { // Warn if update takes longer than 1 frame (16ms)
        console.warn(`⚠️ ${componentName} slow update #${updateCount.current}: ${updateTime.toFixed(2)}ms`);
      }
    }
  });

  useEffect(() => {
    return () => {
      console.log(`🔄 ${componentName} unmounted after ${updateCount.current} updates`);
    };
  }, [componentName]);

  return {
    logMetrics: () => {
      const metrics: PerformanceMetrics = {
        componentName,
        renderTime: performance.now() - renderStartTime.current,
        mountTime: mountStartTime.current - renderStartTime.current,
        updateCount: updateCount.current,
      };
      
      console.table(metrics);
      return metrics;
    }
  };
}

// Hook for measuring async operations
export function useAsyncPerformance() {
  const measureAsync = async <T>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<T> => {
    const startTime = performance.now();
    
    try {
      console.log(`⏱️ Starting ${operationName}...`);
      const result = await operation();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (duration > 1000) {
        console.warn(`🐌 Slow operation ${operationName}: ${duration.toFixed(2)}ms`);
      } else {
        console.log(`✅ ${operationName} completed in ${duration.toFixed(2)}ms`);
      }
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      console.error(`❌ ${operationName} failed after ${duration.toFixed(2)}ms:`, error);
      throw error;
    }
  };

  return { measureAsync };
}

// Hook for monitoring bundle loading
export function useBundlePerformance() {
  useEffect(() => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        
        entries.forEach((entry) => {
          if (entry.entryType === 'navigation') {
            const navEntry = entry as PerformanceNavigationTiming;
            console.log('📊 Navigation Performance:', {
              'DNS Lookup': `${(navEntry.domainLookupEnd - navEntry.domainLookupStart).toFixed(2)}ms`,
              'TCP Connection': `${(navEntry.connectEnd - navEntry.connectStart).toFixed(2)}ms`,
              'Request': `${(navEntry.responseStart - navEntry.requestStart).toFixed(2)}ms`,
              'Response': `${(navEntry.responseEnd - navEntry.responseStart).toFixed(2)}ms`,
              'DOM Processing': `${(navEntry.domContentLoadedEventEnd - navEntry.responseEnd).toFixed(2)}ms`,
              'Total Load Time': `${(navEntry.loadEventEnd - navEntry.navigationStart).toFixed(2)}ms`,
            });
          }
          
          if (entry.entryType === 'resource' && entry.name.includes('.js')) {
            const resourceEntry = entry as PerformanceResourceTiming;
            const loadTime = resourceEntry.responseEnd - resourceEntry.startTime;
            
            if (loadTime > 500) {
              console.warn(`🐌 Slow JS bundle: ${entry.name.split('/').pop()} - ${loadTime.toFixed(2)}ms`);
            }
          }
        });
      });

      observer.observe({ entryTypes: ['navigation', 'resource'] });

      return () => observer.disconnect();
    }
  }, []);
}
