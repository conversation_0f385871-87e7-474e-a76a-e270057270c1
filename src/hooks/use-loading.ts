/**
 * Loading State Hook
 *
 * A custom hook for managing loading states with support for multiple concurrent operations.
 */

"use client";

import { useState, useCallback } from "react";

export interface LoadingState {
  /** Whether any operation is currently loading */
  isLoading: boolean;
  /** Map of loading states by operation key */
  loadingStates: Record<string, boolean>;
  /** Start loading for a specific operation */
  startLoading: (key?: string) => void;
  /** Stop loading for a specific operation */
  stopLoading: (key?: string) => void;
  /** Check if a specific operation is loading */
  isLoadingKey: (key: string) => boolean;
  /** Reset all loading states */
  resetLoading: () => void;
}

/**
 * Hook for managing loading states
 *
 * @param initialState Initial loading state (default: false)
 * @returns LoadingState object with loading state and control functions
 */
export function useLoading(initialState = false): LoadingState {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>(
    initialState ? { default: true } : {}
  );

  const startLoading = useCallback((key = "default") => {
    setLoadingStates(prev => ({ ...prev, [key]: true }));
  }, []);

  const stopLoading = useCallback((key = "default") => {
    setLoadingStates(prev => {
      const newState = { ...prev };
      delete newState[key];
      return newState;
    });
  }, []);

  const isLoadingKey = useCallback((key: string) => {
    return Boolean(loadingStates[key]);
  }, [loadingStates]);

  const resetLoading = useCallback(() => {
    setLoadingStates({});
  }, []);

  const isLoading = Object.keys(loadingStates).length > 0;

  return {
    isLoading,
    loadingStates,
    startLoading,
    stopLoading,
    isLoadingKey,
    resetLoading,
  };
}

/**
 * Hook for managing async operations with loading states
 *
 * @param operation The async operation to execute
 * @param key Optional key for the loading state
 * @returns Object with execute function and loading state
 */
export function useAsyncOperation<T = unknown>(
  operation: () => Promise<T>,
  key = "default"
) {
  const { isLoading, startLoading, stopLoading, isLoadingKey } = useLoading();

  const execute = useCallback(async (): Promise<T> => {
    try {
      startLoading(key);
      const result = await operation();
      return result;
    } finally {
      stopLoading(key);
    }
  }, [operation, key, startLoading, stopLoading]);

  return {
    execute,
    isLoading: isLoadingKey(key),
    isAnyLoading: isLoading,
  };
}

/**
 * Hook for managing multiple async operations with loading states and error handling
 *
 * @returns Object with utilities for managing async operations
 */
export function useAsyncOperations() {
  const { isLoading, loadingStates, startLoading, stopLoading, isLoadingKey, resetLoading } = useLoading();
  const [errors, setErrors] = useState<Record<string, string>>({});

  const executeOperation = useCallback(async <T>(
    operation: () => Promise<T>,
    key: string,
    onSuccess?: (result: T) => void,
    onError?: (error: unknown) => void
  ): Promise<T | null> => {
    try {
      startLoading(key);
      // Clear any previous error for this operation
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[key];
        return newErrors;
      });

      const result = await operation();

      if (onSuccess) {
        onSuccess(result);
      }

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An error occurred';

      setErrors(prev => ({ ...prev, [key]: errorMessage }));

      if (onError) {
        onError(error);
      }

      return null;
    } finally {
      stopLoading(key);
    }
  }, [startLoading, stopLoading]);

  const clearError = useCallback((key: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[key];
      return newErrors;
    });
  }, []);

  const clearAllErrors = useCallback(() => {
    setErrors({});
  }, []);

  return {
    isLoading,
    loadingStates,
    errors,
    executeOperation,
    isLoadingKey,
    clearError,
    clearAllErrors,
    resetLoading,
  };
}
