-- Initialize User Token Balances
-- Run this after setting up the main schema to give users some starting tokens

-- Function to initialize a user's token balance
CREATE OR REPLACE FUNCTION initialize_user_tokens(p_user_id UUID, p_initial_balance DECIMAL(10,4) DEFAULT 10.0)
R<PERSON><PERSON>NS VOID AS $$
BEGIN
    -- Insert or update user's token balance
    INSERT INTO buddychip_copy_tokens (user_id, balance, total_earned)
    VALUES (p_user_id, p_initial_balance, p_initial_balance)
    ON CONFLICT (user_id) 
    DO UPDATE SET 
        balance = GREATEST(buddychip_copy_tokens.balance, p_initial_balance),
        total_earned = buddychip_copy_tokens.total_earned + p_initial_balance,
        updated_at = NOW();
        
    -- Record the initial grant transaction
    INSERT INTO buddychip_token_transactions (
        user_id, 
        transaction_type, 
        amount, 
        description,
        action_type
    ) VALUES (
        p_user_id,
        'initial_grant',
        p_initial_balance,
        'Initial token grant for new user',
        'initial_grant'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION initialize_user_tokens(UUID, DECIMAL) TO authenticated;

-- Initialize tokens for existing users (optional)
-- Uncomment the lines below if you want to give existing users some starting tokens

/*
-- Give all existing users 10 $COPY tokens to start
INSERT INTO buddychip_copy_tokens (user_id, balance, total_earned)
SELECT 
    id as user_id,
    10.0 as balance,
    10.0 as total_earned
FROM auth.users
WHERE id NOT IN (SELECT user_id FROM buddychip_copy_tokens)
ON CONFLICT (user_id) DO NOTHING;

-- Record initial grant transactions for existing users
INSERT INTO buddychip_token_transactions (user_id, transaction_type, amount, description, action_type)
SELECT 
    id as user_id,
    'initial_grant' as transaction_type,
    10.0 as amount,
    'Initial token grant for existing user' as description,
    'initial_grant' as action_type
FROM auth.users
WHERE id NOT IN (
    SELECT user_id 
    FROM buddychip_token_transactions 
    WHERE transaction_type = 'initial_grant'
);
*/

-- Function to add tokens to a user (for admin use)
CREATE OR REPLACE FUNCTION add_user_tokens(p_user_id UUID, p_amount DECIMAL(10,4), p_description TEXT DEFAULT 'Admin token grant')
RETURNS VOID AS $$
BEGIN
    -- Update user's balance
    UPDATE buddychip_copy_tokens 
    SET 
        balance = balance + p_amount,
        total_earned = total_earned + p_amount,
        updated_at = NOW()
    WHERE user_id = p_user_id;
    
    -- If user doesn't exist, create them
    IF NOT FOUND THEN
        INSERT INTO buddychip_copy_tokens (user_id, balance, total_earned)
        VALUES (p_user_id, p_amount, p_amount);
    END IF;
    
    -- Record the transaction
    INSERT INTO buddychip_token_transactions (
        user_id, 
        transaction_type, 
        amount, 
        description,
        action_type
    ) VALUES (
        p_user_id,
        'admin_grant',
        p_amount,
        p_description,
        'admin_grant'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to service role only (admin function)
GRANT EXECUTE ON FUNCTION add_user_tokens(UUID, DECIMAL, TEXT) TO service_role;

-- Trigger to automatically initialize tokens for new users
CREATE OR REPLACE FUNCTION auto_initialize_user_tokens()
RETURNS TRIGGER AS $$
BEGIN
    -- Initialize with 10 $COPY tokens for new users
    PERFORM initialize_user_tokens(NEW.id, 10.0);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger on user creation
DROP TRIGGER IF EXISTS trigger_auto_initialize_tokens ON auth.users;
CREATE TRIGGER trigger_auto_initialize_tokens
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION auto_initialize_user_tokens();

-- Example: Give yourself some tokens for testing
-- Replace 'your-user-id-here' with your actual user ID from auth.users
-- SELECT add_user_tokens('your-user-id-here'::UUID, 100.0, 'Testing tokens');
