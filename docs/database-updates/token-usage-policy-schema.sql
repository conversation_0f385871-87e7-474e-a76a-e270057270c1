-- Token Usage Policy System Database Schema
-- Execute this in your Supabase SQL editor

-- 1. Create token usage policies table
CREATE TABLE IF NOT EXISTS buddychip_token_usage_policies (
  id SERIAL PRIMARY KEY,
  action_type VARCHAR(50) NOT NULL UNIQUE,
  cost_amount DECIMAL(10,4) NOT NULL DEFAULT 0,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 2. Create token transactions table if it doesn't exist
CREATE TABLE IF NOT EXISTS buddychip_token_transactions (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  transaction_type VARCHAR(50) NOT NULL DEFAULT 'manual',
  amount DECIMAL(10,4) NOT NULL,
  description TEXT,
  related_tweet_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 3. Create copy tokens balance table if it doesn't exist
CREATE TABLE IF NOT EXISTS buddychip_copy_tokens (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
  balance DECIMAL(10,4) NOT NULL DEFAULT 0,
  staked_amount DECIMAL(10,4) NOT NULL DEFAULT 0,
  total_earned DECIMAL(10,4) NOT NULL DEFAULT 0,
  total_spent DECIMAL(10,4) NOT NULL DEFAULT 0,
  wallet_address VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- 4. Add action tracking columns to transactions table
ALTER TABLE buddychip_token_transactions
ADD COLUMN IF NOT EXISTS action_type VARCHAR(50),
ADD COLUMN IF NOT EXISTS action_metadata JSONB;

-- 3. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_token_transactions_action_type
ON buddychip_token_transactions(action_type);

CREATE INDEX IF NOT EXISTS idx_token_transactions_user_action
ON buddychip_token_transactions(user_id, action_type);

CREATE INDEX IF NOT EXISTS idx_token_usage_policies_action_type
ON buddychip_token_usage_policies(action_type);

CREATE INDEX IF NOT EXISTS idx_token_usage_policies_active
ON buddychip_token_usage_policies(is_active);

-- 4. Insert default token usage policies
INSERT INTO buddychip_token_usage_policies (action_type, cost_amount, description) VALUES
('tweet_generation', 0.5, 'Generate a viral tweet with AI'),
('context_fetch', 0.05, 'Fetch context from Twitter post or handle'),
('tweet_analysis', 0.1, 'Analyze tweet relevance and engagement'),
('image_generation', 1.0, 'Generate AI image for social media'),
('bulk_tweet_analysis', 0.5, 'Analyze multiple tweets in batch (10 tweets)'),
('premium_theme_access', 2.0, 'Access to premium tweet themes'),
('viral_score_calculation', 0.2, 'Calculate viral potential score'),
('hashtag_suggestions', 0.1, 'Generate relevant hashtags'),
('engagement_prediction', 0.3, 'Predict engagement metrics'),
('content_optimization', 0.4, 'Optimize content for virality'),
('topup_deposit', 0, 'User top-up deposit transaction'),
('topup_fee', 0.01, 'Top-up transaction fee')
ON CONFLICT (action_type) DO NOTHING;

-- 5. Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 6. Create trigger for auto-updating updated_at
CREATE TRIGGER update_buddychip_token_usage_policies_updated_at
    BEFORE UPDATE ON buddychip_token_usage_policies
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 7. Enable RLS (Row Level Security) for the new table
ALTER TABLE buddychip_token_usage_policies ENABLE ROW LEVEL SECURITY;

-- 8. Enable RLS for all tables
ALTER TABLE buddychip_copy_tokens ENABLE ROW LEVEL SECURITY;
ALTER TABLE buddychip_token_transactions ENABLE ROW LEVEL SECURITY;

-- 9. Create RLS policies for token usage policies
-- Allow all authenticated users to read policies
CREATE POLICY "Allow authenticated users to read token usage policies"
ON buddychip_token_usage_policies FOR SELECT
TO authenticated
USING (true);

-- Only allow service role to modify policies (admin only)
CREATE POLICY "Allow service role to manage token usage policies"
ON buddychip_token_usage_policies FOR ALL
TO service_role
USING (true);

-- 10. Create RLS policies for copy tokens
-- Users can only access their own token balance
CREATE POLICY "Users can view own token balance"
ON buddychip_copy_tokens FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can update own token balance"
ON buddychip_copy_tokens FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own token balance"
ON buddychip_copy_tokens FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- 11. Create RLS policies for token transactions
-- Users can only access their own transactions
CREATE POLICY "Users can view own transactions"
ON buddychip_token_transactions FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own transactions"
ON buddychip_token_transactions FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- 12. Grant necessary permissions
GRANT SELECT ON buddychip_token_usage_policies TO authenticated;
GRANT ALL ON buddychip_token_usage_policies TO service_role;

GRANT SELECT, INSERT, UPDATE ON buddychip_copy_tokens TO authenticated;
GRANT ALL ON buddychip_copy_tokens TO service_role;

GRANT SELECT, INSERT ON buddychip_token_transactions TO authenticated;
GRANT ALL ON buddychip_token_transactions TO service_role;

-- 10. Create view for active policies only
CREATE OR REPLACE VIEW buddychip_active_token_policies AS
SELECT
  action_type,
  cost_amount,
  description,
  created_at,
  updated_at
FROM buddychip_token_usage_policies
WHERE is_active = true;

-- Grant access to the view
GRANT SELECT ON buddychip_active_token_policies TO authenticated;

-- 11. Create function to get action cost
CREATE OR REPLACE FUNCTION get_action_cost(p_action_type VARCHAR(50))
RETURNS DECIMAL(10,4) AS $$
DECLARE
    action_cost DECIMAL(10,4);
BEGIN
    SELECT cost_amount INTO action_cost
    FROM buddychip_token_usage_policies
    WHERE action_type = p_action_type AND is_active = true;

    -- Return 0 if action not found
    RETURN COALESCE(action_cost, 0);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION get_action_cost(VARCHAR(50)) TO authenticated;

-- 12. Create function to check if user can afford action
CREATE OR REPLACE FUNCTION can_afford_action(p_user_id UUID, p_action_type VARCHAR(50))
RETURNS BOOLEAN AS $$
DECLARE
    user_balance DECIMAL(10,4);
    action_cost DECIMAL(10,4);
BEGIN
    -- Get user's current balance
    SELECT balance INTO user_balance
    FROM buddychip_copy_tokens
    WHERE user_id = p_user_id;

    -- Get action cost
    SELECT get_action_cost(p_action_type) INTO action_cost;

    -- Return true if user has sufficient balance
    RETURN COALESCE(user_balance, 0) >= COALESCE(action_cost, 0);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION can_afford_action(UUID, VARCHAR(50)) TO authenticated;
