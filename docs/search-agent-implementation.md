# AI Search Agent & Chatbot Implementation

## Overview

The AI Search Agent is a sophisticated system that combines the power of Perplexity AI and xAI Live Search to provide comprehensive research capabilities. The system includes both a direct search interface and a conversational chatbot with persistent memory using Mem0. The agent intelligently selects the best tools based on query analysis and provides real-time information with proper citations.

## Architecture

### Components

1. **Search Agent Core** (`src/lib/ai/agents/search-agent.ts`)
   - Main orchestration logic
   - Tool selection and execution
   - Query analysis
   - Result synthesis

2. **API Route** (`src/app/api/ai/search-agent/route.ts`)
   - RESTful endpoint for search requests
   - Authentication and rate limiting
   - Search history management
   - Error handling

3. **React Component** (`src/components/ai/search-agent.tsx`)
   - User interface for search interactions
   - Real-time search results display
   - Search history management
   - Citation and source tracking

4. **Search Chatbot** (`src/lib/ai/agents/search-chatbot.ts`)
   - Conversational AI with memory integration
   - Context-aware responses using Mem0
   - Automatic search tool invocation
   - Session management and conversation history

5. **Chatbot API Route** (`src/app/api/ai/search-chatbot/route.ts`)
   - RESTful endpoint for chat interactions
   - Session management and conversation persistence
   - Memory integration and context retrieval

6. **Chatbot Component** (`src/components/ai/search-chatbot.tsx`)
   - Real-time chat interface
   - Session management and message history
   - Tool usage visualization
   - Memory context indicators

7. **Database Schema** (`docs/sql/search-agent-schema.sql`)
   - Search history storage
   - Analytics tracking
   - User data isolation with RLS
   - Integration with existing Mem0 schema

## Tools

### 1. Perplexity Search Tool

**Best for:**
- General knowledge queries
- Research and analysis
- Detailed explanations
- Academic and scientific information

**Features:**
- Multiple model options (small, large, huge)
- Comprehensive web search
- Built-in citation handling
- Temperature control for response style

**Configuration:**
```typescript
{
  query: string,
  model: 'llama-3.1-sonar-small-128k-online' | 'llama-3.1-sonar-large-128k-online' | 'llama-3.1-sonar-huge-128k-online',
  temperature: number (0-2)
}
```

### 2. xAI Live Search Tool

**Best for:**
- Real-time information
- Current events and news
- Social media content (X/Twitter)
- Live data and recent updates

**Features:**
- Multiple data sources (web, X, news, RSS)
- Date range filtering
- Handle-specific X searches
- Website exclusion
- Country-specific results
- Safe search filtering

**Configuration:**
```typescript
{
  query: string,
  mode: 'auto' | 'on' | 'off',
  sources: ['web', 'x', 'news', 'rss'],
  maxResults: number (1-20),
  fromDate?: string (YYYY-MM-DD),
  toDate?: string (YYYY-MM-DD),
  xHandles?: string[],
  excludeWebsites?: string[],
  country?: string (ISO alpha-2),
  safeSearch: boolean
}
```

### 3. Query Analysis Tool

**Purpose:**
- Analyzes user queries to determine optimal search strategy using Google Gemini 2.5 Flash Preview via OpenRouter
- Recommends appropriate tools and sources
- Provides reasoning for tool selection

**Output:**
```typescript
{
  queryType: 'realtime' | 'research' | 'mixed',
  recommendedTool: 'perplexity' | 'xai' | 'both',
  reasoning: string,
  suggestedSources: string[],
  timeframe: 'recent' | 'historical' | 'any',
  priority: 'speed' | 'depth' | 'balanced'
}
```

## API Endpoints

### POST /api/ai/search-agent

Execute a search query using the AI agent.

**Request:**
```json
{
  "query": "What are the latest developments in AI?",
  "save_to_history": true,
  "user_context": {
    "preferences": ["technology", "research"],
    "previous_searches": ["AI trends", "machine learning"]
  }
}
```

**Response:**
```json
{
  "success": true,
  "query": "What are the latest developments in AI?",
  "response": "Based on recent information...",
  "sources": ["xai_live_search", "perplexity"],
  "citations": ["https://example.com/article1", "https://example.com/article2"],
  "analysis": {
    "queryType": "realtime",
    "recommendedTool": "both",
    "reasoning": "Query asks for latest developments, requiring real-time data",
    "suggestedSources": ["web", "x", "news"],
    "timeframe": "recent",
    "priority": "balanced"
  },
  "metadata": {
    "requestId": "search-1234567890-abc123",
    "executionTime": 2500,
    "tokensUsed": 1250,
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

### GET /api/ai/search-agent

Retrieve search history.

**Query Parameters:**
- `limit`: Number of results (max 100, default 20)
- `offset`: Pagination offset (default 0)

**Response:**
```json
{
  "success": true,
  "searches": [
    {
      "id": "uuid",
      "query": "AI developments",
      "response": "Response text...",
      "sources_used": ["xai_live_search"],
      "citations": ["https://example.com"],
      "analysis": {...},
      "execution_time_ms": 2500,
      "tokens_used": 1250,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ],
  "pagination": {
    "limit": 20,
    "offset": 0,
    "hasMore": false
  }
}
```

## Environment Variables

Required environment variables:

```bash
# Perplexity API
PERPLEXITY_API_KEY=your_perplexity_api_key

# xAI API
XAI_API_KEY=your_xai_api_key

# OpenRouter (for query analysis with Gemini 2.5 Flash Preview)
OPENROUTER_API_KEY=your_openrouter_api_key

# Supabase (for data storage)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## Database Setup

1. Run the schema creation script:
```sql
-- Execute the contents of docs/sql/search-agent-schema.sql
```

2. Verify tables are created:
- `buddychip_search_history`
- `buddychip_search_analytics`

3. Ensure RLS policies are active for data security.

## Usage Examples

### Basic Search
```typescript
const result = await executeSearchAgent("What's happening in AI today?");
```

### Research Query
```typescript
const result = await executeSearchAgent("Explain quantum computing principles");
```

### Real-time News
```typescript
const result = await executeSearchAgent("Latest news about climate change");
```

### Social Media Specific
```typescript
const result = await executeSearchAgent("What are people saying about the new iPhone on Twitter?");
```

## Features

### Intelligent Tool Selection
- Automatically analyzes queries to determine the best search approach
- Combines multiple sources when beneficial
- Provides reasoning for tool selection

### Real-time Data Access
- xAI Live Search provides up-to-date information
- Access to X/Twitter posts and real-time web data
- Date range filtering for temporal queries

### Comprehensive Research
- Perplexity provides in-depth analysis and research
- Academic and scientific information access
- Detailed explanations with proper citations

### User Experience
- Clean, intuitive interface
- Real-time search progress
- Search history with easy re-execution
- Citation tracking and source verification

### Performance & Monitoring
- Execution time tracking
- Token usage monitoring
- Rate limiting protection
- Comprehensive logging

### Security & Privacy
- Row-level security for user data isolation
- Authentication required for all operations
- Secure API key management
- Data retention policies

## Rate Limiting

- Maximum 10 searches per minute per user
- Configurable limits based on user tier
- Graceful error handling for rate limit exceeded

## Error Handling

- Graceful fallback when one tool fails
- Detailed error logging for debugging
- User-friendly error messages
- Retry mechanisms for transient failures

## Analytics

- Daily usage statistics per user
- Tool usage patterns
- Performance metrics
- Token consumption tracking

## Future Enhancements

1. **Additional Search Sources**
   - Google Scholar integration
   - Academic database access
   - Specialized domain searches

2. **Advanced Features**
   - Search result summarization
   - Multi-language support
   - Voice search interface
   - Export capabilities

3. **Performance Optimizations**
   - Result caching
   - Parallel tool execution
   - Response streaming

4. **User Experience**
   - Search suggestions
   - Query auto-completion
   - Result filtering and sorting
   - Collaborative search sharing
