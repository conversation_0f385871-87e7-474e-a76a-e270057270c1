# BuddyChip Database Setup Guide

## Quick Setup

### 1. Check Current Database Status
Run this in your Supabase SQL Editor:
```sql
-- Copy and paste the contents of scripts/check-database-tables.sql
```

### 2. Set Up Missing Tables

If you see "MISSING" tables, you need to run the setup scripts:

#### Option A: Manual Setup (Recommended)
1. Go to your Supabase project dashboard
2. Navigate to SQL Editor
3. Run each schema file in order:

```sql
-- 1. Core tables (if missing)
-- Copy and paste contents of docs/sql/database.sql

-- 2. Copy.AI features (if missing)  
-- Copy and paste contents of docs/sql/copy-ai-schema.sql

-- 3. Personality analysis (if missing)
-- Copy and paste contents of docs/sql/personality_analysis_schema.sql

-- 4. Memory/AI features (if missing)
-- Copy and paste contents of docs/sql/mem0-schema.sql

-- 5. Search agent (if missing)
-- Copy and paste contents of docs/sql/search-agent-schema.sql
```

#### Option B: All-in-One Setup
If you need to set up everything from scratch:
```sql
-- Copy and paste contents of scripts/setup-database.sql
```

### 3. Verify Setup
After running the setup, run the check script again to verify all tables exist.

## Common Issues

### RLS Policy Errors
If you get "row-level security policy" errors:
1. Make sure you're logged in to your app
2. Check that the user exists in `buddychip_profiles` table
3. Verify RLS policies are correctly set up

### Missing Environment Variables
Make sure your `.env` file has:
```
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
OPENAI_API_KEY=your_openai_key
```

## Troubleshooting

### Copy.AI Token Errors
If you get "new row violates row-level security policy for table buddychip_copy_tokens":
1. Ensure the `buddychip_copy_tokens` table exists
2. Check that RLS policies allow INSERT for authenticated users
3. Verify your user profile exists in `buddychip_profiles`

### OpenAI API Errors
If you get "OPENAI_API_KEY environment variable is missing":
1. Check the spelling in your `.env` file (should be `OPENAI_API_KEY`, not `OPNEAI_API_KEY`)
2. Restart your development server after changing `.env`
3. Verify the API key is valid

## Next Steps

After database setup:
1. Restart your development server: `bun run dev`
2. Test the application features
3. Check browser console for any remaining errors
