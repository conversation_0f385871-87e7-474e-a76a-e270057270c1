# Database Setup Guide for BuddyChip Search Agent & Chatbot

## Overview

This guide will help you set up all the necessary database tables, indexes, RLS policies, and functions for the BuddyChip Search Agent and Chatbot with Mem0 memory integration.

## Prerequisites

- Supabase project created
- Access to Supabase SQL Editor
- `pgvector` extension available (should be enabled by default in Supabase)

## Step-by-Step Setup

### 1. Access Supabase SQL Editor

1. Go to your Supabase dashboard
2. Navigate to **SQL Editor**
3. Create a new query

### 2. Run the Complete Setup Script

Copy and paste the entire contents of `docs/sql/complete-database-setup.sql` into the SQL Editor and execute it.

**Or follow the manual steps below:**

### 3. Manual Setup (Alternative)

If you prefer to run sections individually:

#### Step 3.1: Enable Required Extensions

```sql
-- Enable pgvector extension for embeddings
CREATE EXTENSION IF NOT EXISTS vector;
```

#### Step 3.2: Create Tables

```sql
-- Search History Table
CREATE TABLE IF NOT EXISTS buddychip_search_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    query TEXT NOT NULL,
    response TEXT NOT NULL,
    sources_used TEXT[] NOT NULL DEFAULT '{}',
    citations TEXT[] NOT NULL DEFAULT '{}',
    analysis JSONB,
    execution_time_ms INTEGER,
    tokens_used INTEGER DEFAULT 0,
    request_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Search Analytics Table
CREATE TABLE IF NOT EXISTS buddychip_search_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    total_searches INTEGER DEFAULT 0,
    perplexity_searches INTEGER DEFAULT 0,
    xai_searches INTEGER DEFAULT 0,
    combined_searches INTEGER DEFAULT 0,
    chatbot_conversations INTEGER DEFAULT 0,
    total_tokens_used INTEGER DEFAULT 0,
    avg_execution_time_ms FLOAT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- Memories Table (for Mem0)
CREATE TABLE IF NOT EXISTS buddychip_memories (
    id TEXT PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    embedding vector(1536), -- OpenAI text-embedding-3-small dimension
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### Step 3.3: Create Indexes

```sql
-- Search History Indexes
CREATE INDEX IF NOT EXISTS idx_buddychip_search_history_user_id ON buddychip_search_history(user_id);
CREATE INDEX IF NOT EXISTS idx_buddychip_search_history_created_at ON buddychip_search_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_buddychip_search_history_request_id ON buddychip_search_history(request_id);
CREATE INDEX IF NOT EXISTS idx_buddychip_search_history_sources ON buddychip_search_history USING GIN(sources_used);
CREATE INDEX IF NOT EXISTS idx_buddychip_search_history_analysis ON buddychip_search_history USING GIN(analysis);
CREATE INDEX IF NOT EXISTS idx_buddychip_search_history_search ON buddychip_search_history USING GIN(to_tsvector('english', query || ' ' || response));

-- Search Analytics Indexes
CREATE INDEX IF NOT EXISTS idx_buddychip_search_analytics_user_date ON buddychip_search_analytics(user_id, date);
CREATE INDEX IF NOT EXISTS idx_buddychip_search_analytics_date ON buddychip_search_analytics(date DESC);

-- Memory Indexes
CREATE INDEX IF NOT EXISTS idx_buddychip_memories_user_id ON buddychip_memories(user_id);
CREATE INDEX IF NOT EXISTS idx_buddychip_memories_created_at ON buddychip_memories(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_buddychip_memories_metadata ON buddychip_memories USING GIN(metadata);
CREATE INDEX IF NOT EXISTS idx_buddychip_memories_embedding ON buddychip_memories USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
```

#### Step 3.4: Enable Row Level Security

```sql
-- Enable RLS on all tables
ALTER TABLE buddychip_search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE buddychip_search_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE buddychip_memories ENABLE ROW LEVEL SECURITY;
```

#### Step 3.5: Create RLS Policies

```sql
-- Search History Policies
CREATE POLICY "Users can view their own search history" ON buddychip_search_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own search history" ON buddychip_search_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own search history" ON buddychip_search_history
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own search history" ON buddychip_search_history
    FOR DELETE USING (auth.uid() = user_id);

-- Search Analytics Policies
CREATE POLICY "Users can view their own search analytics" ON buddychip_search_analytics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service can manage search analytics" ON buddychip_search_analytics
    FOR ALL USING (auth.role() = 'service_role');

-- Memory Policies
CREATE POLICY "Users can manage their own memories" ON buddychip_memories
    FOR ALL USING (auth.uid() = user_id);
```

### 4. Verification

After running the setup, verify everything was created correctly:

```sql
-- Check tables
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'buddychip_%'
ORDER BY table_name;

-- Check RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename LIKE 'buddychip_%';

-- Check indexes
SELECT indexname, tablename 
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename LIKE 'buddychip_%'
ORDER BY tablename, indexname;
```

Expected results:
- 3 tables: `buddychip_search_history`, `buddychip_search_analytics`, `buddychip_memories`
- All tables should have `rowsecurity = true`
- Multiple indexes for performance optimization

## What Each Table Does

### `buddychip_search_history`
- Stores all search queries and chatbot conversations
- Tracks sources used (Perplexity, xAI, chatbot)
- Includes citations and analysis metadata
- Used for both search agent and chatbot history

### `buddychip_search_analytics`
- Daily aggregated statistics per user
- Tracks usage patterns and performance metrics
- Automatically updated via triggers
- Useful for monitoring and analytics

### `buddychip_memories`
- Mem0 memory storage with vector embeddings
- Stores conversation context and user preferences
- Enables semantic search for relevant memories
- Powers the chatbot's persistent memory

## Security Features

### Row Level Security (RLS)
- **User Isolation**: Users can only access their own data
- **Service Access**: Service role can manage analytics
- **Automatic Enforcement**: Policies are automatically applied

### Data Protection
- **Cascade Deletes**: User data is cleaned up when user is deleted
- **Audit Trail**: Created/updated timestamps on all records
- **Request Tracking**: Unique request IDs for debugging

## Performance Optimizations

### Indexes
- **User-based queries**: Fast lookups by user_id
- **Time-based queries**: Efficient date range searches
- **Full-text search**: Search within query and response content
- **Vector similarity**: Fast semantic search for memories
- **JSON queries**: Efficient metadata and analysis searches

### Functions
- **Automated analytics**: Real-time statistics updates
- **Vector search**: Optimized memory retrieval
- **Cleanup utilities**: Data retention management

## Troubleshooting

### Common Issues

1. **pgvector extension not found**
   ```sql
   CREATE EXTENSION IF NOT EXISTS vector;
   ```

2. **Permission denied errors**
   - Ensure you're running as a superuser or have proper permissions
   - Check that RLS policies are correctly configured

3. **Index creation fails**
   - Ensure tables exist before creating indexes
   - Check for sufficient disk space

4. **Function creation fails**
   - Ensure all referenced tables exist
   - Check for syntax errors in function definitions

### Verification Queries

```sql
-- Test memory search function
SELECT * FROM match_memories(
    (SELECT embedding FROM buddychip_memories LIMIT 1),
    5,
    '{}'::jsonb
);

-- Test analytics function
SELECT * FROM get_memory_stats();

-- Check recent activity
SELECT COUNT(*) as total_searches,
       COUNT(DISTINCT user_id) as unique_users,
       MAX(created_at) as latest_search
FROM buddychip_search_history
WHERE created_at > NOW() - INTERVAL '24 hours';
```

## Next Steps

After completing the database setup:

1. **Set Environment Variables**: Ensure all API keys are configured
2. **Test the Search Agent**: Navigate to `/search` and try a query
3. **Test the Chatbot**: Navigate to `/chat` and start a conversation
4. **Monitor Usage**: Check the analytics tables for usage data
5. **Verify Memory**: Ensure conversations are being stored and retrieved

The database is now ready to support both the search agent and chatbot with full memory capabilities!
