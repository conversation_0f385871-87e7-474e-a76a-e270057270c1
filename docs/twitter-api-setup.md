# Twitter API Integration Setup

This guide explains how to set up the Twitter API integration for BuddyChip.

## Prerequisites

1. A Twitter Developer Account
2. A Twitter Developer Project and App
3. Bearer Token for API access

## Setting Up Twitter Developer Account

1. Go to [Twitter Developer Portal](https://developer.twitter.com/en/portal/dashboard)
2. Sign in with your Twitter account
3. Apply for a developer account if you don't have one
4. Once approved, create a new project and app

## Getting Your Bearer Token

1. In the Twitter Developer Portal, navigate to your project
2. Go to the "Keys and Tokens" tab
3. Generate or copy your Bearer Token
4. Keep this token secure - it provides access to the Twitter API

## Configuring BuddyChip

1. Add your Bearer Token to the `.env` file:
   ```
   TWITTER_BEARER_TOKEN=your_bearer_token_here
   ```

2. Restart the application for the changes to take effect

## Tweet Limits and Rate Limiting

The application is configured to fetch only the 10 most recent tweets per account:

```typescript
// Rate limiting configuration
// Limiting to 10 tweets per account
const RATE_LIMIT = {
  MAX_REQUESTS_PER_WINDOW: 1,    // Only make one request per account
  WINDOW_MS: 15 * 60 * 1000,     // Window size in milliseconds (15 minutes)
  TWEETS_PER_REQUEST: 10,        // Number of tweets to fetch per request (limited to 10)
  DELAY_BETWEEN_REQUESTS_MS: 100 // Delay between requests in milliseconds
};
```

This 10-tweet limit per account helps:
1. Keep the application focused on the most recent and relevant content
2. Reduce API usage and stay within Twitter's rate limits
3. Improve performance by limiting the amount of data processed

For different limits, you can adjust the `TWEETS_PER_REQUEST` value, but be mindful of Twitter's rate limiting policies.

## Twitter API Endpoints Used

The application uses the following Twitter API v2 endpoints:

1. **User Lookup**: `/users/by/username/:username`
   - Used to get the Twitter user ID from a username

2. **User Tweets**: `/users/:id/tweets`
   - Used to get tweets from a specific user
   - Includes pagination to fetch more than the default limit

## Scheduling Tweet Ingestion

For regular tweet ingestion, you can set up a scheduled job using:

1. **Vercel Cron Jobs** (if deployed on Vercel)
2. **AWS Lambda** with EventBridge
3. **Node.js** with a package like `node-cron`

Example with `node-cron`:

```javascript
import cron from 'node-cron';
import { scheduleTweetIngestion } from './lib/utils/schedule-tweet-ingestion';

// Run tweet ingestion every hour
cron.schedule('0 * * * *', async () => {
  try {
    await scheduleTweetIngestion('https://yourdomain.com', 'your-auth-token');
    console.log('Tweet ingestion completed successfully');
  } catch (error) {
    console.error('Error in tweet ingestion:', error);
  }
});
```

## Fallback to Mock Data

If the Twitter API is not available or you don't have API credentials, the application will fall back to using mock data for development purposes. This is controlled in the `fetchTweetsForAccount` function:

```typescript
try {
  tweets = await fetchTweetsFromTwitter(account.twitter_handle);
} catch (error) {
  console.warn(`Error fetching tweets from Twitter API: ${error.message}`);
  console.warn('Falling back to mock tweets for development');
  tweets = generateMockTweets(account.twitter_handle);
  usedMockData = true;
}
```

## Manual Tweet Ingestion

The application includes a script to manually trigger tweet ingestion for testing purposes:

### Using npm/bun Scripts

```bash
# Using npm scripts (requires SUPABASE_SERVICE_ROLE_KEY in environment)
bun run ingest-tweets:all

# For a specific account
bun run ingest-tweets -- --url=http://localhost:3000 --token=your-auth-token --account=123
```

### Using Node Directly

```bash
# Ingest tweets for all followed accounts
node scripts/ingest-tweets.js --url=http://localhost:3000 --token=your-auth-token

# Ingest tweets for a specific account
node scripts/ingest-tweets.js --url=http://localhost:3000 --token=your-auth-token --account=123
```

These scripts are useful for:
- Testing the Twitter API integration
- Manually refreshing tweets without waiting for scheduled jobs
- Debugging issues with tweet ingestion

## Troubleshooting

1. **Rate Limit Errors**: If you see rate limit errors, adjust the `RATE_LIMIT` configuration
2. **Authentication Errors**: Ensure your Bearer Token is correct and has not expired
3. **User Not Found**: Verify the Twitter handle exists and is spelled correctly
4. **No Tweets Returned**: The user may have a private account or no tweets

For more information, refer to the [Twitter API Documentation](https://developer.twitter.com/en/docs/twitter-api).
