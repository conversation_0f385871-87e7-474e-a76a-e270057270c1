# Supabase Setup Guide

This guide will help you set up your Supabase project for BuddyChip.

## 1. Create a Supabase Project

1. Go to [Supabase](https://supabase.com/) and sign in or create an account.
2. Click "New Project" and fill in the details:
   - Name: BuddyChip (or any name you prefer)
   - Database Password: Create a strong password
   - Region: Choose the region closest to you
3. Click "Create new project" and wait for it to be created.

## 2. Set Up Database Tables

Once your project is created, you need to set up the database tables. You can do this by running the SQL script in the SQL Editor.

1. In your Supabase project dashboard, go to the "SQL Editor" section.
2. Create a new query.
3. Copy and paste the SQL from the `database.sql` file in this docs folder.
4. Run the query to create all the necessary tables.

> **Note**: All tables in the SQL script have been prefixed with `buddychip_` to avoid conflicts with other tables in your Supabase database. This ensures that the BuddyChip application's tables are isolated from any other applications you might be hosting in the same Supabase project.

### Important Note on User Profiles

The SQL script includes a trigger function that should automatically create a profile for each new user who signs up. However, if you encounter any issues with this trigger (such as the "Key is not present in table buddychip_profiles" error), the application includes a fallback mechanism that will create a profile for the user when they access certain features.

If you're experiencing issues with the automatic profile creation, you can manually create a profile for a user by running the following SQL in the SQL Editor (replace `YOUR_USER_ID` with the actual user ID):

```sql
INSERT INTO public.buddychip_profiles (id, username)
VALUES ('YOUR_USER_ID', 'default_username');
```

## 3. Configure Authentication

1. In your Supabase project dashboard, go to the "Authentication" section.
2. Under "Settings", make sure "Email auth" is enabled.
3. Optionally, you can enable other authentication providers like Google, GitHub, etc.

## 4. Set Up Environment Variables

1. In your Supabase project dashboard, go to the "Settings" section, then "API".
2. Copy the "Project URL" and "anon public" key.
3. Create or update the `.env` file in the root of your project with the following variables:

```
NEXT_PUBLIC_SUPABASE_URL=your-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
BUDDYCHIP_TABLE_PREFIX=buddychip_
```

For the `SUPABASE_SERVICE_ROLE_KEY`, go to the "API" section in the Supabase dashboard and copy the "service_role secret" key.

The `BUDDYCHIP_TABLE_PREFIX` variable is set to "buddychip_" to match the table names in the database schema. This ensures that the application knows to look for tables with this prefix.

**Important**: Never expose your service role key in client-side code. It should only be used in server-side code.

## 5. Set Up Twitter API (Optional)

If you want to use real Twitter data instead of mock data, you'll need to set up a Twitter Developer account and get API keys.

1. Go to [Twitter Developer Portal](https://developer.twitter.com/en/portal/dashboard) and sign in or create an account.
2. Create a new project and app.
3. Get your API keys and add them to your `.env` file:

```
TWITTER_BEARER_TOKEN=your-bearer-token
```

## 6. Restart Your Application

After setting up all the above, restart your Next.js application to apply the changes:

```bash
bun dev
```

## Troubleshooting

If you encounter any issues:

1. Check that your Supabase URL and API keys are correct in the `.env` file.
2. Make sure all the tables were created successfully in the Supabase database.
3. Check the browser console and server logs for any error messages.
4. If you're having authentication issues, make sure the authentication settings are properly configured in Supabase.

For more help, refer to the [Supabase documentation](https://supabase.com/docs) or [Next.js documentation](https://nextjs.org/docs).
