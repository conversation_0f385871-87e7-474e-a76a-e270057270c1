-- Complete Database Setup for BuddyChip Search Agent & Chatbot
-- Run this SQL in your Supabase SQL Editor

-- ============================================================================
-- 1. SEARCH AGENT TABLES
-- ============================================================================

-- Search History Table
CREATE TABLE IF NOT EXISTS buddychip_search_history (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    query TEXT NOT NULL,
    response TEXT NOT NULL,
    sources_used TEXT[] NOT NULL DEFAULT '{}',
    citations TEXT[] NOT NULL DEFAULT '{}',
    analysis JSONB,
    execution_time_ms INTEGER,
    tokens_used INTEGER DEFAULT 0,
    request_id TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Search Analytics Table
CREATE TABLE IF NOT EXISTS buddychip_search_analytics (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    date DATE NOT NULL DEFAULT CURRENT_DATE,
    total_searches INTEGER DEFAULT 0,
    perplexity_searches INTEGER DEFAULT 0,
    xai_searches INTEGER DEFAULT 0,
    combined_searches INTEGER DEFAULT 0,
    chatbot_conversations INTEGER DEFAULT 0,
    total_tokens_used INTEGER DEFAULT 0,
    avg_execution_time_ms FLOAT DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- ============================================================================
-- 2. MEM0 MEMORY TABLES (if not already created)
-- ============================================================================

-- Enable pgvector extension for embeddings
CREATE EXTENSION IF NOT EXISTS vector;

-- Memories table for Mem0 integration
CREATE TABLE IF NOT EXISTS buddychip_memories (
    id TEXT PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    embedding vector(1536), -- OpenAI text-embedding-3-small dimension
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 3. INDEXES FOR PERFORMANCE
-- ============================================================================

-- Search History Indexes
CREATE INDEX IF NOT EXISTS idx_buddychip_search_history_user_id ON buddychip_search_history(user_id);
CREATE INDEX IF NOT EXISTS idx_buddychip_search_history_created_at ON buddychip_search_history(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_buddychip_search_history_request_id ON buddychip_search_history(request_id);
CREATE INDEX IF NOT EXISTS idx_buddychip_search_history_sources ON buddychip_search_history USING GIN(sources_used);
CREATE INDEX IF NOT EXISTS idx_buddychip_search_history_analysis ON buddychip_search_history USING GIN(analysis);

-- Full-text search index
CREATE INDEX IF NOT EXISTS idx_buddychip_search_history_search ON buddychip_search_history USING GIN(to_tsvector('english', query || ' ' || response));

-- Search Analytics Indexes
CREATE INDEX IF NOT EXISTS idx_buddychip_search_analytics_user_date ON buddychip_search_analytics(user_id, date);
CREATE INDEX IF NOT EXISTS idx_buddychip_search_analytics_date ON buddychip_search_analytics(date DESC);

-- Memory Indexes
CREATE INDEX IF NOT EXISTS idx_buddychip_memories_user_id ON buddychip_memories(user_id);
CREATE INDEX IF NOT EXISTS idx_buddychip_memories_created_at ON buddychip_memories(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_buddychip_memories_metadata ON buddychip_memories USING GIN(metadata);

-- Vector similarity index for embeddings
CREATE INDEX IF NOT EXISTS idx_buddychip_memories_embedding ON buddychip_memories USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- ============================================================================
-- 4. ROW LEVEL SECURITY (RLS) POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE buddychip_search_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE buddychip_search_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE buddychip_memories ENABLE ROW LEVEL SECURITY;

-- Search History RLS Policies
CREATE POLICY "Users can view their own search history" ON buddychip_search_history
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own search history" ON buddychip_search_history
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own search history" ON buddychip_search_history
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own search history" ON buddychip_search_history
    FOR DELETE USING (auth.uid() = user_id);

-- Search Analytics RLS Policies
CREATE POLICY "Users can view their own search analytics" ON buddychip_search_analytics
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Service can manage search analytics" ON buddychip_search_analytics
    FOR ALL USING (auth.role() = 'service_role');

-- Memory RLS Policies
CREATE POLICY "Users can manage their own memories" ON buddychip_memories
    FOR ALL USING (auth.uid() = user_id);

-- ============================================================================
-- 5. FUNCTIONS AND TRIGGERS
-- ============================================================================

-- Function to update search analytics
CREATE OR REPLACE FUNCTION update_search_analytics()
RETURNS TRIGGER AS $$
BEGIN
    -- Update daily analytics when a new search is added
    INSERT INTO buddychip_search_analytics (
        user_id,
        date,
        total_searches,
        perplexity_searches,
        xai_searches,
        combined_searches,
        chatbot_conversations,
        total_tokens_used,
        avg_execution_time_ms
    )
    VALUES (
        NEW.user_id,
        CURRENT_DATE,
        1,
        CASE WHEN 'perplexity' = ANY(NEW.sources_used) THEN 1 ELSE 0 END,
        CASE WHEN 'xai_live_search' = ANY(NEW.sources_used) THEN 1 ELSE 0 END,
        CASE WHEN 'combined' = ANY(NEW.sources_used) THEN 1 ELSE 0 END,
        CASE WHEN NEW.analysis->>'type' = 'chatbot_conversation' THEN 1 ELSE 0 END,
        COALESCE(NEW.tokens_used, 0),
        COALESCE(NEW.execution_time_ms, 0)
    )
    ON CONFLICT (user_id, date)
    DO UPDATE SET
        total_searches = buddychip_search_analytics.total_searches + 1,
        perplexity_searches = buddychip_search_analytics.perplexity_searches + 
            CASE WHEN 'perplexity' = ANY(NEW.sources_used) THEN 1 ELSE 0 END,
        xai_searches = buddychip_search_analytics.xai_searches + 
            CASE WHEN 'xai_live_search' = ANY(NEW.sources_used) THEN 1 ELSE 0 END,
        combined_searches = buddychip_search_analytics.combined_searches + 
            CASE WHEN 'combined' = ANY(NEW.sources_used) THEN 1 ELSE 0 END,
        chatbot_conversations = buddychip_search_analytics.chatbot_conversations +
            CASE WHEN NEW.analysis->>'type' = 'chatbot_conversation' THEN 1 ELSE 0 END,
        total_tokens_used = buddychip_search_analytics.total_tokens_used + COALESCE(NEW.tokens_used, 0),
        avg_execution_time_ms = (
            (buddychip_search_analytics.avg_execution_time_ms * buddychip_search_analytics.total_searches) + 
            COALESCE(NEW.execution_time_ms, 0)
        ) / (buddychip_search_analytics.total_searches + 1),
        updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update analytics
DROP TRIGGER IF EXISTS trigger_update_search_analytics ON buddychip_search_history;
CREATE TRIGGER trigger_update_search_analytics
    AFTER INSERT ON buddychip_search_history
    FOR EACH ROW
    EXECUTE FUNCTION update_search_analytics();

-- Function for vector similarity search (used by Mem0)
CREATE OR REPLACE FUNCTION match_memories(
    query_embedding vector(1536),
    match_count int DEFAULT 10,
    filter jsonb DEFAULT '{}'
)
RETURNS TABLE (
    id text,
    content text,
    metadata jsonb,
    similarity float,
    user_id uuid,
    created_at timestamptz
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        buddychip_memories.id,
        buddychip_memories.content,
        buddychip_memories.metadata,
        1 - (buddychip_memories.embedding <=> query_embedding) as similarity,
        buddychip_memories.user_id,
        buddychip_memories.created_at
    FROM buddychip_memories
    WHERE buddychip_memories.user_id = auth.uid()
    AND (
        filter = '{}'::jsonb OR
        buddychip_memories.metadata @> filter
    )
    ORDER BY buddychip_memories.embedding <=> query_embedding
    LIMIT match_count;
END;
$$;

-- Function to get memory statistics
CREATE OR REPLACE FUNCTION get_memory_stats(user_filter uuid DEFAULT auth.uid())
RETURNS TABLE (
    total_memories bigint,
    memory_types jsonb,
    oldest_memory timestamptz,
    newest_memory timestamptz,
    avg_content_length numeric
)
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    SELECT
        COUNT(*) as total_memories,
        jsonb_object_agg(
            COALESCE(metadata->>'type', 'unknown'), 
            type_count
        ) as memory_types,
        MIN(created_at) as oldest_memory,
        MAX(created_at) as newest_memory,
        AVG(length(content)) as avg_content_length
    FROM (
        SELECT 
            metadata,
            created_at,
            content,
            COUNT(*) as type_count
        FROM buddychip_memories
        WHERE user_id = user_filter
        GROUP BY metadata->>'type', metadata, created_at, content
    ) subquery;
END;
$$;

-- Function to clean up old search history (optional)
CREATE OR REPLACE FUNCTION cleanup_old_search_history()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete search history older than 90 days
    DELETE FROM buddychip_search_history 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- 6. COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE buddychip_search_history IS 'Stores search queries and results from the AI search agent and chatbot';
COMMENT ON COLUMN buddychip_search_history.query IS 'The original search query or chat message from the user';
COMMENT ON COLUMN buddychip_search_history.response IS 'The AI-generated response';
COMMENT ON COLUMN buddychip_search_history.sources_used IS 'Array of sources used (perplexity, xai_live_search, combined, chatbot)';
COMMENT ON COLUMN buddychip_search_history.citations IS 'Array of citation URLs';
COMMENT ON COLUMN buddychip_search_history.analysis IS 'JSON object containing query analysis results and metadata';

COMMENT ON TABLE buddychip_search_analytics IS 'Daily aggregated analytics for search and chat usage';
COMMENT ON TABLE buddychip_memories IS 'Mem0 memory storage with vector embeddings for semantic search';

COMMENT ON FUNCTION update_search_analytics() IS 'Automatically updates daily search analytics when new searches are added';
COMMENT ON FUNCTION match_memories(vector, int, jsonb) IS 'Vector similarity search for memories using cosine similarity';
COMMENT ON FUNCTION get_memory_stats(uuid) IS 'Returns memory statistics for a user';
COMMENT ON FUNCTION cleanup_old_search_history() IS 'Removes search history older than 90 days for data retention';

-- ============================================================================
-- 7. VERIFICATION QUERIES
-- ============================================================================

-- Verify tables were created
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE 'buddychip_%'
ORDER BY table_name;

-- Verify RLS is enabled
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename LIKE 'buddychip_%';

-- Verify indexes were created
SELECT indexname, tablename 
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename LIKE 'buddychip_%'
ORDER BY tablename, indexname;
