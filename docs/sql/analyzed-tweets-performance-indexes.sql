-- High-Performance Indexes for Analyzed Tweets API
-- Run this SQL in your Supabase SQL Editor to optimize the analyzed tweets endpoint

-- ============================================================================
-- 1. CORE PERFORMANCE INDEXES
-- ============================================================================

-- Composite index for user + analysis status + created_at (most common query pattern)
CREATE INDEX IF NOT EXISTS idx_buddychip_tweets_user_analysis_created 
ON buddychip_tweets(user_id, (ai_analysis IS NOT NULL), tweet_created_at DESC)
WHERE is_marked_irrelevant = false;

-- Index for user + account filtering + created_at
CREATE INDEX IF NOT EXISTS idx_buddychip_tweets_user_account_created 
ON buddychip_tweets(user_id, source_account_id, tweet_created_at DESC)
WHERE is_marked_irrelevant = false;

-- Index for relevance score filtering (JSONB optimization)
CREATE INDEX IF NOT EXISTS idx_buddychip_tweets_relevance_score 
ON buddychip_tweets USING GIN((ai_analysis->'relevance_score'))
WHERE ai_analysis IS NOT NULL AND is_marked_irrelevant = false;

-- Index for worth_replying filtering (JSONB optimization)
CREATE INDEX IF NOT EXISTS idx_buddychip_tweets_worth_replying 
ON buddychip_tweets USING GIN((ai_analysis->'worth_replying'))
WHERE ai_analysis IS NOT NULL AND is_marked_irrelevant = false;

-- ============================================================================
-- 2. SPECIALIZED PERFORMANCE INDEXES
-- ============================================================================

-- Partial index for analyzed tweets only (reduces index size)
CREATE INDEX IF NOT EXISTS idx_buddychip_tweets_analyzed_only 
ON buddychip_tweets(user_id, tweet_created_at DESC, source_account_id)
WHERE ai_analysis IS NOT NULL AND is_marked_irrelevant = false;

-- Partial index for unanalyzed tweets only
CREATE INDEX IF NOT EXISTS idx_buddychip_tweets_unanalyzed_only 
ON buddychip_tweets(user_id, tweet_created_at DESC, source_account_id)
WHERE ai_analysis IS NULL AND is_marked_irrelevant = false;

-- Index for engagement sorting (using raw_data metrics)
CREATE INDEX IF NOT EXISTS idx_buddychip_tweets_engagement 
ON buddychip_tweets USING GIN(raw_data)
WHERE is_marked_irrelevant = false;

-- ============================================================================
-- 3. COMPOUND INDEXES FOR COMPLEX QUERIES
-- ============================================================================

-- Index for user + worth_replying + relevance_score + created_at
CREATE INDEX IF NOT EXISTS idx_buddychip_tweets_complex_analysis 
ON buddychip_tweets(
  user_id, 
  ((ai_analysis->>'worth_replying')::boolean), 
  ((ai_analysis->>'relevance_score')::numeric) DESC,
  tweet_created_at DESC
)
WHERE ai_analysis IS NOT NULL AND is_marked_irrelevant = false;

-- Index for time-based filtering (recent tweets)
CREATE INDEX IF NOT EXISTS idx_buddychip_tweets_recent 
ON buddychip_tweets(user_id, tweet_created_at DESC)
WHERE tweet_created_at >= (NOW() - INTERVAL '7 days') AND is_marked_irrelevant = false;

-- ============================================================================
-- 4. FOREIGN KEY OPTIMIZATION
-- ============================================================================

-- Ensure foreign key indexes exist for joins
CREATE INDEX IF NOT EXISTS idx_buddychip_tweets_source_account 
ON buddychip_tweets(source_account_id);

CREATE INDEX IF NOT EXISTS idx_buddychip_followed_accounts_user 
ON buddychip_followed_accounts(user_id);

-- ============================================================================
-- 5. FULL-TEXT SEARCH INDEXES (for future content search)
-- ============================================================================

-- Full-text search index for tweet content
CREATE INDEX IF NOT EXISTS idx_buddychip_tweets_content_search 
ON buddychip_tweets USING GIN(to_tsvector('english', content))
WHERE is_marked_irrelevant = false;

-- Full-text search index for AI analysis reasons
CREATE INDEX IF NOT EXISTS idx_buddychip_tweets_analysis_search 
ON buddychip_tweets USING GIN(to_tsvector('english', ai_analysis->>'evaluation_reason'))
WHERE ai_analysis IS NOT NULL AND is_marked_irrelevant = false;

-- ============================================================================
-- 6. STATISTICS UPDATE FOR QUERY PLANNER
-- ============================================================================

-- Update table statistics for better query planning
ANALYZE buddychip_tweets;
ANALYZE buddychip_followed_accounts;

-- ============================================================================
-- 7. PERFORMANCE MONITORING VIEWS
-- ============================================================================

-- View to monitor index usage
CREATE OR REPLACE VIEW buddychip_tweets_index_usage AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE tablename = 'buddychip_tweets'
ORDER BY idx_scan DESC;

-- View to monitor query performance
CREATE OR REPLACE VIEW buddychip_tweets_query_stats AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE query LIKE '%buddychip_tweets%'
ORDER BY total_time DESC;

-- ============================================================================
-- 8. MAINTENANCE FUNCTIONS
-- ============================================================================

-- Function to rebuild indexes if needed
CREATE OR REPLACE FUNCTION rebuild_buddychip_tweets_indexes()
RETURNS TEXT AS $$
DECLARE
    index_name TEXT;
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    result TEXT := '';
BEGIN
    start_time := NOW();
    
    -- List of indexes to rebuild
    FOR index_name IN 
        SELECT indexname 
        FROM pg_indexes 
        WHERE tablename = 'buddychip_tweets' 
        AND indexname LIKE 'idx_buddychip_tweets_%'
    LOOP
        EXECUTE 'REINDEX INDEX CONCURRENTLY ' || index_name;
        result := result || 'Rebuilt: ' || index_name || E'\n';
    END LOOP;
    
    end_time := NOW();
    result := result || 'Total time: ' || (end_time - start_time);
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to get index size information
CREATE OR REPLACE FUNCTION get_buddychip_tweets_index_sizes()
RETURNS TABLE(
    index_name TEXT,
    size_mb NUMERIC,
    table_size_mb NUMERIC,
    index_ratio NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        i.indexname::TEXT,
        ROUND(pg_relation_size(i.indexname::regclass) / 1024.0 / 1024.0, 2) as size_mb,
        ROUND(pg_relation_size('buddychip_tweets') / 1024.0 / 1024.0, 2) as table_size_mb,
        ROUND(
            (pg_relation_size(i.indexname::regclass)::NUMERIC / 
             NULLIF(pg_relation_size('buddychip_tweets'), 0)) * 100, 2
        ) as index_ratio
    FROM pg_indexes i
    WHERE i.tablename = 'buddychip_tweets'
    AND i.indexname LIKE 'idx_buddychip_tweets_%'
    ORDER BY pg_relation_size(i.indexname::regclass) DESC;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- 9. COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON INDEX idx_buddychip_tweets_user_analysis_created IS 
'Primary index for analyzed tweets API - covers user filtering, analysis status, and time ordering';

COMMENT ON INDEX idx_buddychip_tweets_user_account_created IS 
'Index for account-specific tweet filtering with time ordering';

COMMENT ON INDEX idx_buddychip_tweets_relevance_score IS 
'JSONB index for relevance score filtering and sorting';

COMMENT ON INDEX idx_buddychip_tweets_worth_replying IS 
'JSONB index for worth_replying boolean filtering';

COMMENT ON INDEX idx_buddychip_tweets_analyzed_only IS 
'Partial index for analyzed tweets only - reduces index size and improves performance';

COMMENT ON INDEX idx_buddychip_tweets_unanalyzed_only IS 
'Partial index for unanalyzed tweets only - optimizes unanalyzed tweet queries';

COMMENT ON FUNCTION rebuild_buddychip_tweets_indexes() IS 
'Maintenance function to rebuild all buddychip_tweets indexes concurrently';

COMMENT ON FUNCTION get_buddychip_tweets_index_sizes() IS 
'Utility function to monitor index sizes and ratios for performance tuning';

-- ============================================================================
-- 10. VERIFICATION QUERIES
-- ============================================================================

-- Verify indexes were created
SELECT 
    indexname,
    indexdef
FROM pg_indexes 
WHERE tablename = 'buddychip_tweets' 
AND indexname LIKE 'idx_buddychip_tweets_%'
ORDER BY indexname;

-- Check index sizes
SELECT * FROM get_buddychip_tweets_index_sizes();

-- Show table statistics
SELECT 
    schemaname,
    tablename,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_rows,
    n_dead_tup as dead_rows,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables 
WHERE tablename = 'buddychip_tweets';
