# AI Integration Guide

BuddyChip uses AI to analyze tweets and generate personalized reply suggestions. This guide explains how the AI integration works and how to set it up.

## Overview

The AI integration consists of:

1. **Tweet Analysis**: Evaluates tweets to determine if they're worth replying to
2. **Reply Generation**: Creates personalized reply suggestions based on user preferences
3. **User Preferences**: Allows users to customize how the AI analyzes and responds to tweets

## Architecture

The AI integration uses:

- **OpenRouter API**: Provides access to Google's Gemini 2.5 Flash Preview model
- **Server-side API Routes**: Handle AI requests securely
- **Client Components**: Display AI analysis and suggestions

## Setup

### 1. OpenRouter API Key

1. Sign up for an account at [OpenRouter](https://openrouter.ai/)
2. Create an API key
3. Add the API key to your `.env` file:
   ```
   OPENROUTER_API_KEY=your-openrouter-api-key
   NEXT_PUBLIC_APP_URL=your-app-url
   ```

### 2. Database Setup

The AI integration requires additional tables in your Supabase database:

1. `buddychip_user_preferences`: Stores user preferences for AI analysis and replies
2. `buddychip_tweets.ai_analysis`: JSONB column for storing AI analysis results

These tables are included in the `database.sql` file.

## User Preferences

Users can customize their AI preferences through the `/preferences` page:

- **Reply Style**: The overall style and approach for replies
- **Interests & Topics**: What the user is knowledgeable about
- **Preferred Tone**: How formal, casual, or authoritative the user wants to sound
- **Custom System Prompt**: Advanced users can provide specific instructions to the AI

## Tweet Analysis

The AI analyzes tweets based on:

1. **Relevance**: How relevant the tweet is to the user's interests
2. **Engagement Potential**: Likelihood of generating meaningful engagement
3. **Sentiment**: Positive, negative, or neutral tone
4. **Value-Add Potential**: Whether a reply would add value

The analysis produces:
- A score from 1-10
- A determination of whether the tweet is worth replying to
- A reason for the recommendation
- Suggested replies (if worth replying to)

## API Endpoints

### `/api/ai/analyze-tweet`

Analyzes a tweet and determines if it's worth replying to.

**Request:**
```json
{
  "tweet_id": 123
}
```

**Response:**
```json
{
  "tweetId": 123,
  "isWorthReplying": true,
  "relevanceScore": 8,
  "evaluationReason": "This tweet directly asks a question about a topic in your interests.",
  "replySuggestions": [
    "Thanks for asking! Here's my perspective...",
    "Great question. I think...",
    "I've dealt with this before. My approach is..."
  ]
}
```

### `/api/ai/generate-reply`

Generates reply suggestions for a tweet.

**Request:**
```json
{
  "tweet_id": 123,
  "custom_style": "Professional but friendly" // Optional
}
```

**Response:**
```json
{
  "tweet_id": 123,
  "suggestions": [
    "Thanks for sharing this! I've found that...",
    "Interesting perspective. Have you considered...",
    "I appreciate you bringing this up. My thoughts are..."
  ]
}
```

### `/api/user/preferences`

Gets or updates user preferences for AI analysis and replies.

**GET Response:**
```json
{
  "user_id": "user-id",
  "reply_style": "Professional and friendly",
  "interests": "Technology, marketing, startups",
  "tone": "Helpful and informative",
  "system_prompt": "Custom instructions if provided"
}
```

**POST Request:**
```json
{
  "reply_style": "Professional and friendly",
  "interests": "Technology, marketing, startups",
  "tone": "Helpful and informative",
  "system_prompt": "Custom instructions if provided"
}
```

## UI Components

### `UserPreferencesForm`

A form component for users to set their AI preferences.

### `TweetCard` AI Features

The `TweetCard` component has been enhanced with:

1. **Analyze Button**: Triggers AI analysis of the tweet
2. **AI Analysis Display**: Shows whether the tweet is worth replying to
3. **Reply Suggestions**: Displays AI-generated reply suggestions
4. **Generate Response Button**: Generates new reply suggestions

## Customization

### Model Selection

By default, the integration uses Google's Gemini 2.5 Flash Preview model. You can change this in `src/lib/ai/openrouter-service.ts`:

```typescript
const DEFAULT_MODEL = 'google/gemini-2.5-flash-preview';
```

Other recommended models:
- `anthropic/claude-3-opus`
- `anthropic/claude-3-sonnet`
- `openai/gpt-4-turbo`

### System Prompts

The system prompts for tweet analysis and reply generation can be customized in `src/lib/ai/openrouter-service.ts`.

## Troubleshooting

### API Key Issues

If you encounter errors related to the OpenRouter API key:

1. Verify the key is correctly set in your `.env` file
2. Check that the key has not expired
3. Ensure you have sufficient credits in your OpenRouter account

### Rate Limiting

OpenRouter has rate limits based on your subscription. If you hit rate limits:

1. Implement a queue system for processing tweets
2. Add retry logic with exponential backoff
3. Consider upgrading your OpenRouter subscription

### Model Availability

If a specific model is unavailable:

1. Try a different model by changing `DEFAULT_MODEL`
2. Check the OpenRouter status page for outages
3. Implement fallback logic to use alternative models
