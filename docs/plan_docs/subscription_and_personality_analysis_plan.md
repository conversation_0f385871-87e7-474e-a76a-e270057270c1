# BuddyChip Subscription Model & Tweet Personality Analysis Plan

## Overview

This document outlines the implementation plan for two major features:
1. A tiered subscription model with Free, Pro, and Team plans
2. A tweet personality analysis system that analyzes user tweets to determine their personality traits

## Subscription Model

### Tier Structure

| Tier | Price | Max Tweets Analyzed | Max Accounts | Team Members |
|------|-------|---------------------|--------------|--------------|
| Free | $0    | 25                  | 5            | 0            |
| Pro  | $49.99| 250                 | 20           | 0            |
| Team | $39.99| 500                 | 50           | 2            |

### Database Schema Updates

```sql
-- Subscription tiers table
CREATE TABLE public.buddychip_subscription_tiers (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  max_tweets_analyzed INTEGER NOT NULL,
  max_accounts INTEGER NOT NULL,
  features <PERSON><PERSON><PERSON><PERSON>,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- User subscriptions table
CREATE TABLE public.buddychip_user_subscriptions (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.buddychip_profiles(id) ON DELETE CASCADE,
  tier_id INTEGER NOT NULL REFERENCES public.buddychip_subscription_tiers(id),
  status TEXT NOT NULL, -- 'active', 'canceled', 'past_due'
  current_period_start TIMESTAMPTZ NOT NULL,
  current_period_end TIMESTAMPTZ NOT NULL,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  payment_provider TEXT NOT NULL, -- 'stripe', 'paypal', etc.
  payment_provider_subscription_id TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT buddychip_unique_user_subscription UNIQUE (user_id)
);

-- For team subscriptions, we need a way to link team members
CREATE TABLE public.buddychip_team_members (
  id SERIAL PRIMARY KEY,
  team_owner_id UUID NOT NULL REFERENCES public.buddychip_profiles(id) ON DELETE CASCADE,
  member_id UUID NOT NULL REFERENCES public.buddychip_profiles(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member', -- 'owner', 'admin', 'member'
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT buddychip_unique_team_member UNIQUE (team_owner_id, member_id)
);
```

### Initial Data Seeding

```sql
INSERT INTO public.buddychip_subscription_tiers (name, price, max_tweets_analyzed, max_accounts, features)
VALUES 
  ('Free', 0.00, 25, 5, '{"ai_replies": true, "basic_analytics": true}'),
  ('Pro', 49.99, 250, 20, '{"ai_replies": true, "advanced_analytics": true, "priority_support": true, "personality_analysis": true}'),
  ('Team', 39.99, 500, 50, '{"ai_replies": true, "advanced_analytics": true, "priority_support": true, "personality_analysis": true, "team_collaboration": true, "max_team_members": 2}');
```

### API Routes

1. `/api/subscriptions/tiers` - GET: Fetch available subscription tiers
2. `/api/subscriptions/current` - GET: Get current user's subscription
3. `/api/subscriptions/upgrade` - POST: Upgrade subscription
4. `/api/subscriptions/cancel` - POST: Cancel subscription
5. `/api/team/members` - GET/POST/DELETE: Manage team members

### Frontend Components

1. Subscription page with tier comparison
2. Payment processing integration (Stripe/PayPal)
3. Account settings page with subscription management
4. Team management interface for Team tier

## Tweet Personality Analysis

### Database Schema Updates

```sql
-- Table to store user's own tweets
CREATE TABLE public.buddychip_user_tweets (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.buddychip_profiles(id) ON DELETE CASCADE,
  tweet_id TEXT NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL,
  fetched_at TIMESTAMPTZ DEFAULT NOW(),
  raw_data JSONB,
  CONSTRAINT buddychip_unique_user_tweet UNIQUE (user_id, tweet_id)
);

-- Table to store personality analysis results
CREATE TABLE public.buddychip_personality_analysis (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.buddychip_profiles(id) ON DELETE CASCADE,
  analysis_date TIMESTAMPTZ DEFAULT NOW(),
  traits JSONB NOT NULL, -- Store personality traits
  topics JSONB NOT NULL, -- Store topics of interest
  writing_style JSONB NOT NULL, -- Store writing style characteristics
  tweet_count INTEGER NOT NULL, -- Number of tweets analyzed
  CONSTRAINT buddychip_unique_user_personality UNIQUE (user_id)
);
```

### API Routes

1. `/api/twitter/user-tweets` - GET: Fetch user's own tweets
2. `/api/twitter/user-tweets/fetch` - POST: Trigger fetching of user's tweets
3. `/api/ai/personality-analysis` - GET: Get user's personality analysis
4. `/api/ai/personality-analysis/analyze` - POST: Trigger personality analysis

### Implementation Approach

#### Tweet Fetching Service

```typescript
// src/lib/twitter/user-tweets-service.ts
export async function fetchUserTweets(userId: string, maxTweets: number): Promise<Tweet[]> {
  // 1. Check user's subscription tier to determine max tweets
  // 2. Fetch tweets from Twitter API (or use mock data for development)
  // 3. Store tweets in buddychip_user_tweets table
  // 4. Return fetched tweets
}
```

#### Personality Analysis Service

```typescript
// src/lib/ai/personality-analysis-service.ts
export async function analyzeUserPersonality(userId: string): Promise<PersonalityAnalysis> {
  // 1. Fetch user's tweets from buddychip_user_tweets
  // 2. Preprocess tweets (remove URLs, mentions, etc.)
  // 3. Use AI model to analyze personality traits
  // 4. Store results in buddychip_personality_analysis
  // 5. Return analysis results
}
```

## Implementation Timeline

### Phase 1: Subscription Model (2-3 weeks)

#### Week 1: Database & Backend
- Create subscription tables
- Implement subscription tier management
- Integrate with payment provider (Stripe)
- Add subscription checks to existing features

#### Week 2: Frontend Implementation
- Create subscription page with tier comparison
- Implement payment flow
- Add subscription management to user settings
- Implement usage limits based on subscription tier

#### Week 3: Team Functionality
- Implement team member management
- Add team invitation system
- Create team dashboard
- Test and refine subscription features

### Phase 2: Tweet Personality Analysis (3-4 weeks)

#### Week 1: Data Collection
- Implement tweet fetching service
- Create storage for user tweets
- Add rate limiting based on subscription tier
- Implement progress tracking for tweet fetching

#### Week 2: Analysis Engine
- Develop text preprocessing pipeline
- Implement AI analysis integration
- Create storage for analysis results
- Add background job processing

#### Week 3: Frontend Visualization
- Design personality dashboard
- Implement data visualizations
- Create user-friendly insights presentation
- Add personality traits explanation

#### Week 4: Refinement
- Optimize analysis for performance
- Implement caching strategies
- Add periodic re-analysis option
- Polish UI/UX for the feature

## Technical Considerations

### Twitter API Limitations
- Standard API: 900 requests per 15-minute window
- Each request can fetch up to 100 tweets
- For 500 tweets, we need at least 5 requests

**Solution**: Implement a queuing system with exponential backoff for fetching tweets.

### AI Processing Options
1. **Real-time processing**: Analyze as tweets are fetched
2. **Batch processing**: Queue analysis jobs to run during off-peak hours

**Recommendation**: Use batch processing with a progress indicator for users.

### Storage Considerations
- Average tweet with metadata: ~2KB
- 500 tweets = ~1MB per user
- 1,000 users = ~1GB of tweet data

**Solution**: Implement data retention policies based on subscription tier.

## Next Steps

1. Set up the database schema changes
2. Implement the subscription model first
3. Add the tweet personality analysis feature
4. Test thoroughly with different subscription tiers
5. Deploy with feature flags to enable gradual rollout
