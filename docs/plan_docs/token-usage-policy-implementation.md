# Token Usage Policy System - Implementation Plan

## 🎯 **Overview**
Implement a comprehensive token usage policy system where users must spend $COPY tokens for each action in the BuddyChip platform. This creates a sustainable token economy with configurable pricing for different features.

## 📋 **Implementation Phases**

### **Phase 1: Database Schema & Configuration** ✅
**Timeline: 1-2 days** - **COMPLETED**

#### 1.1 Database Schema Updates
- [x] Create `buddychip_token_usage_policies` table
- [x] Update `buddychip_token_transactions` table with action tracking
- [x] Add indexes for performance optimization
- [x] Insert default token costs for all actions
- [x] Create database functions for cost lookup and affordability checks
- [x] Set up RLS policies for security

#### 1.2 Environment Configuration
- [x] Add token cost environment variables
- [x] Configure default pricing in `.env`
- [x] Set up admin override capabilities
- [x] Add TOKEN_USAGE_ENABLED flag for easy toggle

### **Phase 2: Core Token Usage Service** ✅
**Timeline: 2-3 days** - **COMPLETED**

#### 2.1 Token Usage Service (`src/lib/web3/token-usage-service.ts`)
- [x] `getActionCost(actionType)` - Get cost for specific action
- [x] `canAffordAction(userId, actionType)` - Check affordability
- [x] `deductTokensForAction(transaction)` - Deduct tokens safely
- [x] `getUserUsageHistory(userId)` - Get usage history
- [x] `getAllPolicies()` - Get all active policies
- [x] Comprehensive error handling and logging

#### 2.2 Action Types & Interfaces
- [x] Define `TokenAction` enum with all action types
- [x] Create `TokenUsagePolicy` interface
- [x] Create `UsageTransaction` interface
- [x] Add proper TypeScript types
- [x] Create `UsageAnalytics` interface for future use

### **Phase 3: Middleware & Guards** ✅
**Timeline: 1-2 days** - **COMPLETED**

#### 3.1 Token Usage Middleware
- [x] `withTokenUsage()` wrapper function
- [x] Transaction safety (rollback on failure)
- [x] Error handling and logging
- [x] `executeWithTokens()` simplified wrapper
- [x] `checkActionAffordability()` utility function
- [x] Environment-based enable/disable functionality

#### 3.2 API Routes for Policies
- [x] `/api/token-usage/policies` GET endpoint
- [x] `/api/token-usage/policies` POST endpoint for updates
- [x] `/api/token-usage/policies` PUT endpoint for bulk updates
- [x] Admin authentication and validation

### **Phase 4: API Integration** ✅
**Timeline: 2-3 days** - **COMPLETED**

#### 4.1 Update Existing API Routes
- [x] `/api/copy-ai/generate` - Tweet generation costs (0.5 $COPY)
- [x] `/api/copy-ai/context` - Context fetch costs (0.05 $COPY)
- [ ] `/api/copy-ai/analyze` - Analysis costs
- [ ] `/api/image-generation` - Image generation costs

#### 4.2 New API Routes
- [x] `/api/token-usage/policies` - Get/update policies (GET, POST, PUT)
- [ ] `/api/token-usage/history` - User usage history
- [ ] `/api/token-usage/analytics` - Usage analytics
- [ ] `/api/token-usage/check` - Check action affordability

### **Phase 5: UI Components** ✅
**Timeline: 3-4 days** - **COMPLETED**

#### 5.1 Token Cost Display
- [x] Show cost before each action (ActionCostDisplay component)
- [x] Disable buttons when insufficient tokens
- [x] Loading states during token checks
- [x] Success/error notifications via toast
- [x] ActionCostBadge component for compact display
- [x] ActionButton component with built-in cost checking

#### 5.2 React Hooks & Utilities
- [x] useTokenUsage hook for policy management
- [x] useActionAffordability hook for balance checking
- [x] useActionCostDisplay hook for UI display
- [x] Integrated cost display in Copium dashboard
- [x] Real-time balance checking and UI updates

### **Phase 6: Admin Dashboard** ⏳
**Timeline: 2-3 days**

#### 6.1 Policy Management
- [ ] Update token costs in real-time
- [ ] Enable/disable specific actions
- [ ] Bulk policy updates
- [ ] Policy history tracking

#### 6.2 Analytics Dashboard
- [ ] Platform-wide usage metrics
- [ ] Revenue per feature analysis
- [ ] User behavior patterns
- [ ] Token economy health monitoring

## 💰 **Suggested Token Costs**

| Action | Cost ($COPY) | Description |
|--------|--------------|-------------|
| Tweet Generation | 0.5 | Generate viral tweet with AI |
| Context Fetch | 0.05 | Fetch Twitter post/handle context |
| Tweet Analysis | 0.1 | Analyze tweet relevance |
| Image Generation | 1.0 | Generate AI social media image |
| Bulk Analysis (10 tweets) | 0.5 | Batch tweet analysis |
| Premium Themes | 2.0 | Access premium tweet themes |
| Viral Score Calculation | 0.2 | Calculate viral potential |
| Hashtag Suggestions | 0.1 | Generate relevant hashtags |
| Engagement Prediction | 0.3 | Predict engagement metrics |
| Content Optimization | 0.4 | Optimize content for virality |

## 🏗️ **Technical Architecture**

### Database Schema
```sql
-- Token usage policies table
CREATE TABLE buddychip_token_usage_policies (
  id SERIAL PRIMARY KEY,
  action_type VARCHAR(50) NOT NULL UNIQUE,
  cost_amount DECIMAL(10,4) NOT NULL DEFAULT 0,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Enhanced transactions table
ALTER TABLE buddychip_token_transactions
ADD COLUMN action_type VARCHAR(50),
ADD COLUMN action_metadata JSONB;
```

### Core Service Structure
```typescript
export enum TokenAction {
  TWEET_GENERATION = 'tweet_generation',
  CONTEXT_FETCH = 'context_fetch',
  TWEET_ANALYSIS = 'tweet_analysis',
  IMAGE_GENERATION = 'image_generation',
  // ... more actions
}

export interface TokenUsagePolicy {
  actionType: string;
  costAmount: number;
  description: string;
  isActive: boolean;
}

export class TokenUsageService {
  static async getActionCost(actionType: TokenAction): Promise<number>
  static async canAffordAction(userId: string, actionType: TokenAction): Promise<boolean>
  static async deductTokensForAction(transaction: UsageTransaction): Promise<void>
  // ... more methods
}
```

## 🚀 **Implementation Checklist**

### Phase 1: Foundation ⏳
- [ ] Database schema creation
- [ ] Environment configuration
- [ ] Basic service structure

### Phase 2: Core Logic ⏳
- [ ] Token usage service implementation
- [ ] Action cost calculation
- [ ] Transaction safety mechanisms

### Phase 3: Integration ⏳
- [ ] API route updates
- [ ] Middleware implementation
- [ ] Error handling

### Phase 4: User Interface ⏳
- [ ] Cost display components
- [ ] Balance checking
- [ ] Usage dashboard

### Phase 5: Administration ⏳
- [ ] Admin policy management
- [ ] Analytics dashboard
- [ ] Monitoring tools

## 📊 **Success Metrics**

### Technical Metrics
- [ ] 100% action coverage with token costs
- [ ] <100ms token cost lookup time
- [ ] 99.9% transaction success rate
- [ ] Zero token balance inconsistencies

### Business Metrics
- [ ] User engagement with paid features
- [ ] Token consumption patterns
- [ ] Revenue per user increase
- [ ] Feature adoption rates

## 🔄 **Future Enhancements**

### Dynamic Pricing
- [ ] Peak hour pricing adjustments
- [ ] User tier-based pricing
- [ ] Bulk action discounts
- [ ] Loyalty reward systems

### Advanced Features
- [ ] Token subscription plans
- [ ] Referral token rewards
- [ ] Seasonal pricing campaigns
- [ ] A/B testing for pricing

---

**Next Steps:** Begin with Phase 1 - Database Schema & Configuration
**Estimated Total Timeline:** 2-3 weeks for full implementation
**Priority:** High - Core platform monetization feature
