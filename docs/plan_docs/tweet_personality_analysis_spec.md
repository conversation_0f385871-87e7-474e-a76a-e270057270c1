# BuddyChip Tweet Personality Analysis - Technical Specification

## Overview

This document provides detailed technical specifications for implementing the tweet personality analysis feature in BuddyChip. The system will analyze a user's tweets (25 for Free tier, 250 for Pro tier, and 500 for Team tier) to determine their personality traits, writing style, and topics of interest.

## Database Schema

### Tables

#### 1. buddychip_user_tweets

```sql
CREATE TABLE public.buddychip_user_tweets (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.buddychip_profiles(id) ON DELETE CASCADE,
  tweet_id TEXT NOT NULL,
  content TEXT NOT NULL,
  created_at TIMESTAMPTZ NOT NULL,
  fetched_at TIMESTAMPTZ DEFAULT NOW(),
  raw_data JSONB,
  CONSTRAINT buddychip_unique_user_tweet UNIQUE (user_id, tweet_id)
);
```

**Indexes:**
- Primary key on `id`
- Foreign key on `user_id` referencing `buddychip_profiles(id)`
- Unique constraint on `(user_id, tweet_id)`
- Index on `created_at` for efficient sorting

#### 2. buddychip_personality_analysis

```sql
CREATE TABLE public.buddychip_personality_analysis (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.buddychip_profiles(id) ON DELETE CASCADE,
  analysis_date TIMESTAMPTZ DEFAULT NOW(),
  traits JSONB NOT NULL, -- Store personality traits
  topics JSONB NOT NULL, -- Store topics of interest
  writing_style JSONB NOT NULL, -- Store writing style characteristics
  tweet_count INTEGER NOT NULL, -- Number of tweets analyzed
  CONSTRAINT buddychip_unique_user_personality UNIQUE (user_id)
);
```

**Indexes:**
- Primary key on `id`
- Foreign key on `user_id` referencing `buddychip_profiles(id)`
- Unique constraint on `user_id`

#### 3. buddychip_analysis_jobs

```sql
CREATE TABLE public.buddychip_analysis_jobs (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.buddychip_profiles(id) ON DELETE CASCADE,
  status TEXT NOT NULL, -- 'pending', 'in_progress', 'completed', 'failed'
  progress INTEGER DEFAULT 0, -- Progress percentage
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  error TEXT,
  CONSTRAINT buddychip_unique_user_analysis_job UNIQUE (user_id, status)
  CHECK (status IN ('pending', 'in_progress', 'completed', 'failed'))
);
```

**Indexes:**
- Primary key on `id`
- Foreign key on `user_id` referencing `buddychip_profiles(id)`
- Index on `status` for efficient querying of job status

### Row-Level Security Policies

```sql
-- RLS for user tweets
ALTER TABLE public.buddychip_user_tweets ENABLE ROW LEVEL SECURITY;
CREATE POLICY "BuddyChip: Users can manage their own tweets"
  ON public.buddychip_user_tweets
  FOR ALL
  USING (auth.uid() = user_id);

-- RLS for personality analysis
ALTER TABLE public.buddychip_personality_analysis ENABLE ROW LEVEL SECURITY;
CREATE POLICY "BuddyChip: Users can view their own personality analysis"
  ON public.buddychip_personality_analysis
  FOR SELECT
  USING (auth.uid() = user_id);

-- RLS for analysis jobs
ALTER TABLE public.buddychip_analysis_jobs ENABLE ROW LEVEL SECURITY;
CREATE POLICY "BuddyChip: Users can manage their own analysis jobs"
  ON public.buddychip_analysis_jobs
  FOR ALL
  USING (auth.uid() = user_id);
```

## API Endpoints

### 1. User Tweets

#### GET /api/twitter/user-tweets

**Description:** Fetch the user's own tweets that have been stored for analysis.

**Query Parameters:**
- `limit`: Number of tweets to return (default: 50)
- `offset`: Pagination offset (default: 0)

**Response:**
```json
{
  "tweets": [
    {
      "id": 1,
      "tweet_id": "1234567890",
      "content": "This is a tweet about AI and technology.",
      "created_at": "2023-04-01T12:34:56Z",
      "fetched_at": "2023-05-01T00:00:00Z"
    },
    // More tweets...
  ],
  "total": 250,
  "limit": 50,
  "offset": 0
}
```

#### POST /api/twitter/user-tweets/fetch

**Description:** Trigger the fetching of the user's own tweets for analysis.

**Request:**
```json
{
  "twitter_handle": "username",
  "count": 250  // Optional, defaults to max allowed by subscription
}
```

**Response:**
```json
{
  "job_id": "job-123",
  "status": "pending",
  "max_tweets": 250,
  "estimated_time": "5 minutes"
}
```

### 2. Personality Analysis

#### GET /api/ai/personality-analysis

**Description:** Get the user's personality analysis results.

**Response:**
```json
{
  "analysis": {
    "id": 1,
    "analysis_date": "2023-05-01T00:00:00Z",
    "traits": {
      "openness": 0.85,
      "conscientiousness": 0.72,
      "extraversion": 0.65,
      "agreeableness": 0.78,
      "neuroticism": 0.45
    },
    "topics": [
      {"name": "Technology", "score": 0.92},
      {"name": "Business", "score": 0.78},
      {"name": "Politics", "score": 0.45},
      {"name": "Sports", "score": 0.23},
      {"name": "Entertainment", "score": 0.35}
    ],
    "writing_style": {
      "formality": 0.68,
      "emotionality": 0.55,
      "complexity": 0.72,
      "assertiveness": 0.81
    },
    "tweet_count": 250
  },
  "job_status": "completed"
}
```

#### POST /api/ai/personality-analysis/analyze

**Description:** Trigger a personality analysis based on the user's tweets.

**Response:**
```json
{
  "job_id": "job-456",
  "status": "pending",
  "estimated_time": "10 minutes"
}
```

### 3. Analysis Job Status

#### GET /api/ai/analysis-jobs/{job_id}

**Description:** Check the status of an analysis job.

**Response:**
```json
{
  "id": "job-456",
  "status": "in_progress",
  "progress": 45,
  "created_at": "2023-05-01T12:34:56Z",
  "updated_at": "2023-05-01T12:40:00Z",
  "estimated_completion": "2023-05-01T12:50:00Z"
}
```

## Implementation Details

### 1. Tweet Fetching Service

```typescript
// src/lib/twitter/user-tweets-service.ts

export async function fetchUserTweets(
  userId: string, 
  twitterHandle: string, 
  maxTweets: number
): Promise<{ jobId: string }> {
  // 1. Check user's subscription tier to determine max tweets allowed
  const { data: subscription } = await supabase
    .from('buddychip_user_subscriptions')
    .select('tier:buddychip_subscription_tiers(max_tweets_analyzed)')
    .eq('user_id', userId)
    .single();
  
  const maxAllowedTweets = subscription?.tier?.max_tweets_analyzed || 25; // Default to Free tier
  const tweetsToFetch = Math.min(maxTweets || maxAllowedTweets, maxAllowedTweets);
  
  // 2. Create an analysis job
  const { data: job, error } = await supabase
    .from('buddychip_analysis_jobs')
    .insert({
      user_id: userId,
      status: 'pending',
      progress: 0
    })
    .select()
    .single();
  
  if (error) throw error;
  
  // 3. Start a background process to fetch tweets
  // This would typically be handled by a serverless function or worker
  await startTweetFetchingJob(userId, twitterHandle, tweetsToFetch, job.id);
  
  return { jobId: job.id };
}

async function startTweetFetchingJob(
  userId: string, 
  twitterHandle: string, 
  maxTweets: number,
  jobId: string
): Promise<void> {
  // This would be implemented as a background job
  // For example, using AWS Lambda, Vercel Edge Functions, or a worker queue
  
  try {
    // Update job status
    await supabase
      .from('buddychip_analysis_jobs')
      .update({ status: 'in_progress' })
      .eq('id', jobId);
    
    // Fetch tweets in batches to respect Twitter API rate limits
    let fetchedCount = 0;
    let maxId = null;
    
    while (fetchedCount < maxTweets) {
      // Fetch a batch of tweets (up to 100 per request)
      const batchSize = Math.min(100, maxTweets - fetchedCount);
      const tweets = await fetchTwitterUserTimeline(twitterHandle, batchSize, maxId);
      
      if (tweets.length === 0) break; // No more tweets available
      
      // Store tweets in database
      const tweetsToInsert = tweets.map(tweet => ({
        user_id: userId,
        tweet_id: tweet.id,
        content: tweet.text,
        created_at: new Date(tweet.created_at).toISOString(),
        raw_data: tweet
      }));
      
      await supabase.from('buddychip_user_tweets').upsert(tweetsToInsert, {
        onConflict: 'user_id,tweet_id'
      });
      
      // Update progress
      fetchedCount += tweets.length;
      const progress = Math.round((fetchedCount / maxTweets) * 100);
      await supabase
        .from('buddychip_analysis_jobs')
        .update({ progress })
        .eq('id', jobId);
      
      // Set max_id for pagination
      maxId = tweets[tweets.length - 1].id;
      
      // Respect Twitter API rate limits
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // Mark job as completed
    await supabase
      .from('buddychip_analysis_jobs')
      .update({ 
        status: 'completed', 
        progress: 100,
        completed_at: new Date().toISOString()
      })
      .eq('id', jobId);
    
  } catch (error) {
    // Handle errors
    console.error('Error fetching tweets:', error);
    await supabase
      .from('buddychip_analysis_jobs')
      .update({ 
        status: 'failed',
        error: error.message,
        completed_at: new Date().toISOString()
      })
      .eq('id', jobId);
  }
}
```

### 2. Personality Analysis Service

```typescript
// src/lib/ai/personality-analysis-service.ts

export async function analyzeUserPersonality(userId: string): Promise<{ jobId: string }> {
  // 1. Create an analysis job
  const { data: job, error } = await supabase
    .from('buddychip_analysis_jobs')
    .insert({
      user_id: userId,
      status: 'pending',
      progress: 0
    })
    .select()
    .single();
  
  if (error) throw error;
  
  // 2. Start a background process to analyze tweets
  await startPersonalityAnalysisJob(userId, job.id);
  
  return { jobId: job.id };
}

async function startPersonalityAnalysisJob(userId: string, jobId: string): Promise<void> {
  try {
    // Update job status
    await supabase
      .from('buddychip_analysis_jobs')
      .update({ status: 'in_progress' })
      .eq('id', jobId);
    
    // Fetch user's tweets
    const { data: tweets, error } = await supabase
      .from('buddychip_user_tweets')
      .select('content, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });
    
    if (error) throw error;
    
    if (tweets.length === 0) {
      throw new Error('No tweets available for analysis');
    }
    
    // Preprocess tweets
    const processedTweets = tweets.map(tweet => preprocessTweet(tweet.content));
    
    // Analyze personality using AI
    const traits = await analyzePersonalityTraits(processedTweets);
    const topics = await analyzeTopics(processedTweets);
    const writingStyle = await analyzeWritingStyle(processedTweets);
    
    // Store analysis results
    await supabase
      .from('buddychip_personality_analysis')
      .upsert({
        user_id: userId,
        analysis_date: new Date().toISOString(),
        traits,
        topics,
        writing_style: writingStyle,
        tweet_count: tweets.length
      }, {
        onConflict: 'user_id'
      });
    
    // Mark job as completed
    await supabase
      .from('buddychip_analysis_jobs')
      .update({ 
        status: 'completed', 
        progress: 100,
        completed_at: new Date().toISOString()
      })
      .eq('id', jobId);
    
  } catch (error) {
    // Handle errors
    console.error('Error analyzing personality:', error);
    await supabase
      .from('buddychip_analysis_jobs')
      .update({ 
        status: 'failed',
        error: error.message,
        completed_at: new Date().toISOString()
      })
      .eq('id', jobId);
  }
}
```

## Frontend Components

### 1. Personality Dashboard

- Overview of personality traits with visualizations
- Topic interest breakdown with percentages
- Writing style analysis with examples
- Comparison to average Twitter users

### 2. Tweet Fetching UI

- Twitter handle input form
- Progress indicator for tweet fetching
- Status updates during the process
- Error handling and retry options

### 3. Analysis Results Visualization

- Radar charts for personality traits
- Bar charts for topic interests
- Timeline of tweet activity
- Word clouds for common terms and phrases

## Implementation Considerations

### Twitter API Integration

We'll use Twitter API v2 for fetching user tweets:

1. Implement proper rate limit handling (900 requests per 15-minute window)
2. Use pagination to fetch tweets in batches
3. Store raw tweet data for future reference
4. Implement error handling and retry logic

### AI Analysis Approach

For personality analysis, we'll use a combination of:

1. **Linguistic analysis**: Analyze writing style, vocabulary, and sentence structure
2. **Topic modeling**: Identify main topics and interests
3. **Sentiment analysis**: Determine emotional tone and patterns
4. **Personality trait extraction**: Map linguistic features to Big Five personality traits

### Performance Optimization

To handle large numbers of tweets efficiently:

1. Implement background job processing for tweet fetching and analysis
2. Use batch processing for AI analysis
3. Implement caching for analysis results
4. Optimize database queries with proper indexing

## Testing Plan

1. Unit tests for tweet preprocessing and analysis algorithms
2. Integration tests for Twitter API interaction
3. End-to-end tests for the complete analysis flow
4. Performance tests with varying tweet volumes
5. Edge case testing for different types of Twitter content
