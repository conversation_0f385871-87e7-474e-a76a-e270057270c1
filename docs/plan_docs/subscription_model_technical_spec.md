# BuddyChip Subscription Model - Technical Specification

## Overview

This document provides detailed technical specifications for implementing the subscription model in BuddyChip. The system will support three tiers: Free, Pro ($49.99/month), and Team ($39.99/month with 2 team members).

## Database Schema

### Tables

#### 1. buddychip_subscription_tiers

```sql
CREATE TABLE public.buddychip_subscription_tiers (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  max_tweets_analyzed INTEGER NOT NULL,
  max_accounts INTEGER NOT NULL,
  features J<PERSON>N<PERSON>,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Indexes:**
- Primary key on `id`

**Sample Data:**
```sql
INSERT INTO public.buddychip_subscription_tiers (name, price, max_tweets_analyzed, max_accounts, features)
VALUES 
  ('Free', 0.00, 25, 5, '{"ai_replies": true, "basic_analytics": true}'),
  ('Pro', 49.99, 250, 20, '{"ai_replies": true, "advanced_analytics": true, "priority_support": true, "personality_analysis": true}'),
  ('Team', 39.99, 500, 50, '{"ai_replies": true, "advanced_analytics": true, "priority_support": true, "personality_analysis": true, "team_collaboration": true, "max_team_members": 2}');
```

#### 2. buddychip_user_subscriptions

```sql
CREATE TABLE public.buddychip_user_subscriptions (
  id SERIAL PRIMARY KEY,
  user_id UUID NOT NULL REFERENCES public.buddychip_profiles(id) ON DELETE CASCADE,
  tier_id INTEGER NOT NULL REFERENCES public.buddychip_subscription_tiers(id),
  status TEXT NOT NULL, -- 'active', 'canceled', 'past_due'
  current_period_start TIMESTAMPTZ NOT NULL,
  current_period_end TIMESTAMPTZ NOT NULL,
  cancel_at_period_end BOOLEAN DEFAULT FALSE,
  payment_provider TEXT NOT NULL, -- 'stripe', 'paypal', etc.
  payment_provider_subscription_id TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT buddychip_unique_user_subscription UNIQUE (user_id)
);
```

**Indexes:**
- Primary key on `id`
- Foreign key on `user_id` referencing `buddychip_profiles(id)`
- Foreign key on `tier_id` referencing `buddychip_subscription_tiers(id)`
- Unique constraint on `user_id`

#### 3. buddychip_team_members

```sql
CREATE TABLE public.buddychip_team_members (
  id SERIAL PRIMARY KEY,
  team_owner_id UUID NOT NULL REFERENCES public.buddychip_profiles(id) ON DELETE CASCADE,
  member_id UUID NOT NULL REFERENCES public.buddychip_profiles(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member', -- 'owner', 'admin', 'member'
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT buddychip_unique_team_member UNIQUE (team_owner_id, member_id)
);
```

**Indexes:**
- Primary key on `id`
- Foreign key on `team_owner_id` referencing `buddychip_profiles(id)`
- Foreign key on `member_id` referencing `buddychip_profiles(id)`
- Unique constraint on `(team_owner_id, member_id)`

### Row-Level Security Policies

```sql
-- RLS for subscription tiers (viewable by all authenticated users)
ALTER TABLE public.buddychip_subscription_tiers ENABLE ROW LEVEL SECURITY;
CREATE POLICY "BuddyChip: All users can view subscription tiers"
  ON public.buddychip_subscription_tiers
  FOR SELECT
  USING (true);

-- RLS for user subscriptions
ALTER TABLE public.buddychip_user_subscriptions ENABLE ROW LEVEL SECURITY;
CREATE POLICY "BuddyChip: Users can view their own subscriptions"
  ON public.buddychip_user_subscriptions
  FOR SELECT
  USING (auth.uid() = user_id);

-- RLS for team members
ALTER TABLE public.buddychip_team_members ENABLE ROW LEVEL SECURITY;
CREATE POLICY "BuddyChip: Team owners can manage their team members"
  ON public.buddychip_team_members
  FOR ALL
  USING (auth.uid() = team_owner_id);
CREATE POLICY "BuddyChip: Members can view teams they belong to"
  ON public.buddychip_team_members
  FOR SELECT
  USING (auth.uid() = member_id);
```

## API Endpoints

### 1. Subscription Tiers

#### GET /api/subscriptions/tiers

**Description:** Fetch all available subscription tiers.

**Response:**
```json
{
  "tiers": [
    {
      "id": 1,
      "name": "Free",
      "price": 0.00,
      "max_tweets_analyzed": 25,
      "max_accounts": 5,
      "features": {
        "ai_replies": true,
        "basic_analytics": true
      }
    },
    {
      "id": 2,
      "name": "Pro",
      "price": 49.99,
      "max_tweets_analyzed": 250,
      "max_accounts": 20,
      "features": {
        "ai_replies": true,
        "advanced_analytics": true,
        "priority_support": true,
        "personality_analysis": true
      }
    },
    {
      "id": 3,
      "name": "Team",
      "price": 39.99,
      "max_tweets_analyzed": 500,
      "max_accounts": 50,
      "features": {
        "ai_replies": true,
        "advanced_analytics": true,
        "priority_support": true,
        "personality_analysis": true,
        "team_collaboration": true,
        "max_team_members": 2
      }
    }
  ]
}
```

### 2. User Subscription

#### GET /api/subscriptions/current

**Description:** Get the current user's subscription details.

**Response:**
```json
{
  "subscription": {
    "id": 123,
    "tier": {
      "id": 2,
      "name": "Pro",
      "price": 49.99,
      "max_tweets_analyzed": 250,
      "max_accounts": 20,
      "features": {
        "ai_replies": true,
        "advanced_analytics": true,
        "priority_support": true,
        "personality_analysis": true
      }
    },
    "status": "active",
    "current_period_start": "2023-05-01T00:00:00Z",
    "current_period_end": "2023-06-01T00:00:00Z",
    "cancel_at_period_end": false
  },
  "usage": {
    "tweets_analyzed": 120,
    "accounts_followed": 8
  }
}
```

#### POST /api/subscriptions/upgrade

**Description:** Upgrade the user's subscription to a new tier.

**Request:**
```json
{
  "tier_id": 2,
  "payment_method_id": "pm_card_visa"
}
```

**Response:**
```json
{
  "success": true,
  "subscription": {
    "id": 123,
    "tier": {
      "id": 2,
      "name": "Pro"
    },
    "status": "active",
    "current_period_start": "2023-05-01T00:00:00Z",
    "current_period_end": "2023-06-01T00:00:00Z"
  }
}
```

#### POST /api/subscriptions/cancel

**Description:** Cancel the user's subscription at the end of the current billing period.

**Response:**
```json
{
  "success": true,
  "subscription": {
    "id": 123,
    "tier": {
      "id": 2,
      "name": "Pro"
    },
    "status": "active",
    "current_period_start": "2023-05-01T00:00:00Z",
    "current_period_end": "2023-06-01T00:00:00Z",
    "cancel_at_period_end": true
  }
}
```

### 3. Team Management

#### GET /api/team/members

**Description:** Get all team members for the current user's team.

**Response:**
```json
{
  "members": [
    {
      "id": 1,
      "user_id": "user-uuid-1",
      "email": "<EMAIL>",
      "username": "teamowner",
      "role": "owner",
      "joined_at": "2023-04-01T00:00:00Z"
    },
    {
      "id": 2,
      "user_id": "user-uuid-2",
      "email": "<EMAIL>",
      "username": "teammember",
      "role": "member",
      "joined_at": "2023-04-15T00:00:00Z"
    }
  ],
  "max_members": 2,
  "available_slots": 0
}
```

#### POST /api/team/members

**Description:** Add a new team member.

**Request:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "invitation": {
    "id": "inv-123",
    "email": "<EMAIL>",
    "status": "pending",
    "expires_at": "2023-05-15T00:00:00Z"
  }
}
```

#### DELETE /api/team/members/{member_id}

**Description:** Remove a team member.

**Response:**
```json
{
  "success": true
}
```

## Frontend Components

### 1. Subscription Page

- Tier comparison table
- Feature highlights
- Pricing information
- Upgrade buttons
- Current subscription status

### 2. Payment Processing

- Integration with Stripe Elements
- Credit card input form
- Payment confirmation
- Receipt generation

### 3. Account Settings

- Current subscription details
- Usage statistics
- Cancellation option
- Billing history

### 4. Team Management

- Team members list
- Invite new members form
- Remove member functionality
- Role management

## Implementation Considerations

### Payment Processing

We'll use Stripe for payment processing:

1. Create Stripe Products and Prices for each tier
2. Use Stripe Checkout for the payment flow
3. Implement webhook handling for subscription events
4. Store Stripe subscription IDs in our database

### Usage Limits Enforcement

We need to enforce usage limits based on the user's subscription tier:

1. Check subscription tier before fetching tweets
2. Limit the number of accounts that can be followed
3. Show appropriate messaging when limits are reached
4. Provide upgrade prompts when users approach limits

### Team Functionality

For Team subscriptions:

1. Implement an invitation system via email
2. Create a team dashboard for the owner
3. Allow sharing of followed accounts among team members
4. Implement proper access controls for team resources

## Testing Plan

1. Unit tests for subscription logic
2. Integration tests for payment processing
3. End-to-end tests for subscription flows
4. Load testing for team functionality
5. Edge case testing for subscription changes and cancellations
