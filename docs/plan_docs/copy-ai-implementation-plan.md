# Copium On-Chain Integration Plan

## Overview
Integration of the Copium On-Chain feature into BuddyChip, allowing users to generate viral tweets with AI and turn them into tokens through $COPY token economics.

## Phase 1: Foundation & Database Schema ✅

### 1.1 Database Schema Extensions ✅
- [x] Created `docs/sql/copy-ai-schema.sql` with new tables:
  - [x] `buddychip_copy_tokens` - Token balance and transactions
  - [x] `buddychip_generated_tweets` - AI-generated tweets storage
  - [x] `buddychip_tweet_nfts` - NFT minting records
  - [x] `buddychip_leaderboard` - Daily viral tweet tracking
  - [x] `buddychip_staking` - Token staking for premium access
  - [x] `buddychip_shill_activities` - Shill-to-earn tracking
  - [x] `buddychip_token_transactions` - Transaction history

### 1.2 Web3 Infrastructure Setup ✅
- [x] Created `scripts/install-web3-dependencies.sh` for dependency installation
- [x] Created `src/lib/web3/config.ts` for Web3 configuration
- [x] Set up environment variables template

### 1.3 Token Service ✅
- [x] Created `src/lib/web3/token-service.ts` with functions:
  - [x] `getUserTokenBalance()` - Get user balance and staking info
  - [x] `canAffordGeneration()` - Check if user can afford generation
  - [x] `hasPremiumAccess()` - Check premium access through staking
  - [x] `deductGenerationCost()` - Deduct tokens for generation
  - [x] `awardShillReward()` - Award tokens for shill-to-earn

## Phase 2: Core Tweet Generation ✅

### 2.1 AI Tweet Generation Service ✅
- [x] Created `src/lib/ai/viral-tweet-generator.ts` with:
  - [x] `generateViralTweet()` - Main generation function
  - [x] Theme-specific system prompts (meme, degen, vc_bait, general)
  - [x] Viral score calculation algorithm
  - [x] Engagement estimation
  - [x] `getAvailableThemes()` - Theme configuration

### 2.2 Tweet Generation API Routes ✅
- [x] Created `src/app/api/copy-ai/generate/route.ts` with:
  - [x] POST endpoint for tweet generation
  - [x] GET endpoint for user data and history
  - [x] Request validation with Zod
  - [x] Token balance checking and deduction
  - [x] Premium access validation

### 2.3 Tweet Generation UI Components ✅
- [x] Created `src/components/copy-ai/copy-ai-dashboard.tsx` with:
  - [x] Prompt input interface
  - [x] Theme selection dropdown
  - [x] Cost display and confirmation
  - [x] Generated tweet preview
  - [x] Token balance display
  - [x] Tweet history with engagement metrics

### 2.4 Page Integration ✅
- [x] Created `src/app/copy-ai/page.tsx`
- [x] Updated navigation in `src/components/navbar.tsx`

## Phase 3: On-Chain Integration (Next Steps)

### 3.1 IPFS Storage
- [ ] Set up IPFS client for tweet hashing
- [ ] Create tweet metadata structure
- [ ] Implement tweet upload to IPFS
- [ ] Store IPFS hashes on-chain

### 3.2 NFT Minting
- [ ] Create NFT contract interface
- [ ] Implement tweet-to-NFT minting
- [ ] Add royalty distribution system
- [ ] Create NFT marketplace integration

### 3.3 Token Economics
- [ ] Implement blockchain token balance checking
- [ ] Create staking mechanism for unlimited access
- [ ] Set up reward distribution for viral tweets
- [ ] Implement shill-to-earn tracking

## Phase 4: Gamification & Social Features (Future)

### 4.1 Leaderboard System
- [ ] Daily viral tweet tracking
- [ ] Engagement metrics collection
- [ ] Reward distribution automation
- [ ] Public leaderboard display

### 4.2 Social Integration
- [ ] Twitter posting integration
- [ ] Engagement tracking
- [ ] Viral metrics calculation
- [ ] Community features

### 4.3 Advanced Features
- [ ] Telegram bot integration
- [ ] Chrome extension preparation
- [ ] Copy.AI Social Feed
- [ ] Analytics dashboard

## Implementation Status

### ✅ Completed
1. Database schema design and creation
2. Web3 configuration setup
3. Token service implementation
4. AI viral tweet generation service
5. API routes for tweet generation
6. UI components and dashboard
7. Navigation integration

### 🔄 In Progress
- Testing and refinement of existing features

### ⏳ Next Steps
1. **Install Dependencies**: Run `bash scripts/install-web3-dependencies.sh`
2. **Database Migration**: Execute `docs/sql/copy-ai-schema.sql` in Supabase
3. **Environment Setup**: Configure environment variables
4. **Testing**: Test the generation flow end-to-end
5. **IPFS Integration**: Implement on-chain storage
6. **Wallet Connection**: Add Web3 wallet integration
7. **Token Contract**: Deploy and integrate $COPY token

## Technical Notes

### Current Architecture
- **Frontend**: React components with TypeScript
- **Backend**: Next.js API routes
- **Database**: Supabase PostgreSQL with RLS
- **AI**: OpenRouter with Gemini 2.0 Flash
- **Styling**: Tailwind CSS with shadcn/ui

### Key Features Implemented
- Multi-theme tweet generation (meme, degen, vc_bait, general)
- Token-gated generation with premium access
- Viral score calculation and engagement estimation
- Transaction history and balance tracking
- Responsive UI with real-time updates

### Security Considerations
- Row Level Security (RLS) on all database tables
- Request validation with Zod schemas
- Authentication required for all operations
- Proper error handling and logging

## Testing Checklist

### Before Production
- [ ] Test tweet generation for all themes
- [ ] Verify token balance calculations
- [ ] Test premium access functionality
- [ ] Validate API error handling
- [ ] Test responsive UI on mobile
- [ ] Verify database constraints and policies
- [ ] Load test API endpoints
- [ ] Security audit of token operations

## Deployment Requirements

### Environment Variables
```env
# Copium On-Chain Configuration
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_project_id
NEXT_PUBLIC_COPY_TOKEN_CONTRACT_ADDRESS=0x...
NEXT_PUBLIC_NFT_CONTRACT_ADDRESS=0x...
NEXT_PUBLIC_STAKING_CONTRACT_ADDRESS=0x...

# IPFS Configuration
IPFS_API_URL=https://ipfs.infura.io:5001
IPFS_API_KEY=your_api_key
IPFS_API_SECRET=your_api_secret

# Blockchain Configuration
NEXT_PUBLIC_CHAIN_ID=8453
NEXT_PUBLIC_RPC_URL=https://mainnet.base.org

# Copium Pricing
COPY_GENERATION_COST=0.5
COPY_STAKING_MINIMUM=100
```

### Dependencies
- wagmi, viem, @rainbow-me/rainbowkit
- ipfs-http-client
- @tanstack/react-query

This plan provides a comprehensive roadmap for integrating the Copium On-Chain feature into BuddyChip, with clear phases and actionable steps.
