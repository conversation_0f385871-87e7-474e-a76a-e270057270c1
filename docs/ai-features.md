# AI Features Guide

BuddyChip uses AI to analyze tweets and generate personalized reply suggestions. This guide explains how to use these features effectively.

## Overview

The AI features in BuddyChip include:

1. **Tweet Analysis**: Evaluates tweets to determine if they're worth replying to
2. **Reply Generation**: Creates personalized reply suggestions based on your preferences
3. **User Preferences**: Allows you to customize how the AI analyzes and responds to tweets

## Using AI Features

### 1. Setting Up Your AI Preferences

Before using the AI features, you should set up your preferences:

1. Click on your profile in the top-right corner
2. Select "AI Preferences" from the dropdown
3. Configure your preferences:
   - **Reply Style**: Choose from preset styles or enter your own
   - **Interests & Topics**: List topics you're knowledgeable about
   - **Preferred Tone**: Select multiple tones or enter your own
   - **Custom System Prompt**: Advanced users can provide specific instructions

Your preferences will be used by the AI when analyzing tweets and generating replies.

### 2. Ingesting Tweets

To analyze tweets, you first need to ingest them:

1. Go to the "All Tweets" tab on your dashboard
2. Click the "Ingest Tweets" button
3. This will fetch the 10 most recent tweets from each account you follow
4. These tweets will be automatically analyzed

Note: Only the 10 most recent tweets per account are fetched to keep your collection focused on the most relevant content.

### 3. Viewing AI-Analyzed Tweets

To view tweets that have been analyzed by the AI:

1. Go to the "AI Analyzed" tab on your dashboard
2. You'll see three categories:
   - **Worth Replying**: Tweets the AI recommends you reply to
   - **Not Worth Replying**: Tweets the AI suggests you skip
   - **Unanalyzed**: Tweets that haven't been analyzed yet

### 4. Analyzing Tweets

Tweets are automatically analyzed when they are ingested. The analysis determines if a tweet is relevant and worth replying to with a simple YES or NO answer.

If some tweets weren't automatically analyzed during ingestion (due to rate limits or other issues), you can use the backup option:

- In the "AI Analyzed" tab, click "Analyze Unanalyzed Tweets"
- By default, this will analyze up to 10 unanalyzed tweets at once
- Check the "Continue analyzing until all tweets are processed" box to automatically process all unanalyzed tweets in batches of 10 until complete
- A progress bar will show you the status of the batch processing

### 5. Generating Reply Suggestions

Once a tweet has been analyzed, you can generate reply suggestions:

1. For tweets worth replying to, click the "Generate Response" button
2. The AI will create multiple reply suggestions based on your preferences
3. You can view all suggestions and choose the one you prefer

## How It Works

### Tweet Analysis

The AI analyzes tweets based on:

- **Relevance**: How relevant the tweet is to your interests
- **Engagement Potential**: Likelihood of generating meaningful engagement
- **Sentiment**: Positive, negative, or neutral tone
- **Value-Add Potential**: Whether a reply would add value
- **Question Detection**: Whether the tweet is asking a question

Each tweet receives a simple YES or NO determination of whether it's worth replying to. This simplified approach makes it easy to quickly identify which tweets deserve your attention.

### Reply Generation

When generating replies, the AI considers:

- Your preferred reply style
- Your interests and topics
- Your preferred tone
- Any custom system prompt you've provided

The AI generates multiple reply options, typically:
1. A concise and direct reply
2. A thoughtful and engaging reply
3. A professional but friendly reply

## Advanced Features

### Custom System Prompts

The system prompt controls how the AI evaluates tweets and generates replies. You can customize it to:

- Focus on specific types of tweets
- Generate replies in a particular style
- Include specific information in replies
- Follow certain rules or guidelines

Example system prompts are provided in the AI Preferences page.

### Batch Processing

BuddyChip supports batch processing to analyze multiple tweets at once:

- When ingesting tweets, the most recent tweets are automatically analyzed
- You can manually analyze up to 10 tweets at once using the "Analyze X Tweets" button
- This helps you quickly identify which tweets are worth your attention

## Tips for Effective Use

1. **Be Specific with Preferences**: The more specific your preferences, the better the AI can match your style.

2. **Use the AI Analyzed Tab**: This tab helps you focus on tweets that are worth your time.

3. **Customize System Prompts**: If you have specific requirements, use custom system prompts.

4. **Regularly Ingest Tweets**: Set a schedule to regularly ingest and analyze tweets.

5. **Review AI Suggestions**: Always review AI suggestions before using them - they're meant to assist, not replace your judgment.

## Troubleshooting

### Analysis Not Working

If tweet analysis isn't working:

1. Check your OpenRouter API key in the `.env` file
2. Ensure you have sufficient credits in your OpenRouter account
3. Try refreshing the page and analyzing again

### Poor Quality Suggestions

If reply suggestions aren't matching your style:

1. Update your AI preferences to be more specific
2. Try using a custom system prompt
3. Experiment with different reply styles and tones

### Rate Limiting

If you hit rate limits:

1. Reduce the number of tweets you analyze at once
2. Space out your analysis requests
3. Consider upgrading your OpenRouter subscription
