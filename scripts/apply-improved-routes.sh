#!/bin/bash

# <PERSON><PERSON>t to apply the improved route files by replacing the original ones

echo "Applying improved route files..."

# Create a backup directory
mkdir -p backups/api

# Function to backup and replace a file
replace_file() {
  local original_file=$1
  local improved_file=$2
  
  # Check if both files exist
  if [ -f "$original_file" ] && [ -f "$improved_file" ]; then
    # Create backup directory structure
    local backup_dir=$(dirname "backups/$original_file")
    mkdir -p "$backup_dir"
    
    # Backup the original file
    cp "$original_file" "backups/$original_file"
    echo "✅ Backed up $original_file"
    
    # Replace the original file with the improved one
    cp "$improved_file" "$original_file"
    echo "✅ Replaced $original_file with $improved_file"
  else
    echo "❌ Error: One or both files do not exist: $original_file, $improved_file"
  fi
}

# Replace API route files
replace_file "src/app/api/ai/analyze-tweet/route.ts" "src/app/api/ai/analyze-tweet/route-improved.ts"
replace_file "src/app/api/ai/generate-reply/route.ts" "src/app/api/ai/generate-reply/route-improved.ts"
replace_file "src/app/api/personality/analyze/route.ts" "src/app/api/personality/analyze/route-improved.ts"
replace_file "src/app/api/personality/results/route.ts" "src/app/api/personality/results/route-improved.ts"
replace_file "src/app/api/personality/job-status/[jobId]/route.ts" "src/app/api/personality/job-status/[jobId]/route-improved.ts"
replace_file "src/app/api/twitter/accounts/route.ts" "src/app/api/twitter/accounts/route-improved.ts"
replace_file "src/app/api/twitter/ingest/route.ts" "src/app/api/twitter/ingest/route-improved.ts"
replace_file "src/app/api/filtering/route.ts" "src/app/api/filtering/route-improved.ts"
replace_file "src/app/api/rag/route.ts" "src/app/api/rag/route-improved.ts"
replace_file "src/app/api/beta-signup/route.ts" "src/app/api/beta-signup/route-improved.ts"

# Replace the layout file
replace_file "src/app/layout.tsx" "src/app/layout-improved.tsx"

# Replace the OpenRouter service
replace_file "src/lib/ai/openrouter-service.ts" "src/lib/ai/openrouter-service-improved.ts"

echo "All improved files have been applied!"
echo "Original files are backed up in the 'backups' directory."

echo "Next steps:"
echo "1. Update UI components to use toast notifications"
echo "2. Add loading states to all async operations"
echo "3. Implement error handling in UI components"
echo "4. Set up testing infrastructure"
