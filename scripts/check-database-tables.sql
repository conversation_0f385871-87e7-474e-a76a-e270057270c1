-- Quick check to see which BuddyChip tables exist in your database
-- Run this in Supabase SQL editor to verify your setup

SELECT 
  table_name,
  CASE 
    WHEN table_name IN (
      'buddychip_profiles',
      'buddychip_followed_accounts', 
      'buddychip_categories',
      'buddychip_category_keywords',
      'buddychip_tweets',
      'buddychip_tweet_categories',
      'buddychip_user_preferences',
      'buddychip_beta_signups'
    ) THEN 'Core'
    WHEN table_name IN (
      'buddychip_copy_tokens',
      'buddychip_generated_tweets',
      'buddychip_tweet_nfts',
      'buddychip_leaderboard',
      'buddychip_staking',
      'buddychip_shill_activities',
      'buddychip_token_transactions',
      'buddychip_generated_videos'
    ) THEN 'Copy.AI'
    WHEN table_name IN (
      'buddychip_personality_jobs',
      'buddychip_personality_results'
    ) THEN 'Personality'
    WHEN table_name IN (
      'buddychip_memories',
      'buddychip_search_sessions'
    ) THEN 'AI/Memory'
    ELSE 'Other'
  END as feature_group,
  'EXISTS' as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name LIKE 'buddychip_%'
ORDER BY feature_group, table_name;

-- Check for missing core tables
WITH required_tables AS (
  SELECT unnest(ARRAY[
    'buddychip_profiles',
    'buddychip_followed_accounts',
    'buddychip_categories', 
    'buddychip_category_keywords',
    'buddychip_tweets',
    'buddychip_tweet_categories',
    'buddychip_user_preferences',
    'buddychip_beta_signups',
    'buddychip_copy_tokens',
    'buddychip_generated_tweets'
  ]) as table_name
),
existing_tables AS (
  SELECT table_name 
  FROM information_schema.tables 
  WHERE table_schema = 'public' 
    AND table_name LIKE 'buddychip_%'
)
SELECT 
  rt.table_name,
  CASE 
    WHEN et.table_name IS NULL THEN 'MISSING - NEEDS CREATION'
    ELSE 'EXISTS'
  END as status
FROM required_tables rt
LEFT JOIN existing_tables et ON rt.table_name = et.table_name
ORDER BY status DESC, rt.table_name;
