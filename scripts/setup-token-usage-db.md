# Database Setup for Token Usage Policy System

## Instructions

1. **Open your Supabase Dashboard**
   - Go to your project: https://supabase.com/dashboard/project/[your-project-id]
   - Navigate to the SQL Editor

2. **Execute the Schema**
   - Copy the entire content from `docs/database-updates/token-usage-policy-schema.sql`
   - Paste it into the SQL Editor
   - Click "Run" to execute

3. **Verify Setup**
   After running the SQL, you should see:
   - New table: `buddychip_token_usage_policies`
   - Updated table: `buddychip_token_transactions` (with new columns)
   - New indexes for performance
   - Default token policies inserted
   - Database functions created

## Expected Results

The following token policies should be created with these default costs:

| Action Type | Cost ($COPY) | Description |
|-------------|--------------|-------------|
| tweet_generation | 0.5 | Generate a viral tweet with AI |
| context_fetch | 0.05 | Fetch context from Twitter post or handle |
| tweet_analysis | 0.1 | Analyze tweet relevance and engagement |
| image_generation | 1.0 | Generate AI image for social media |
| bulk_tweet_analysis | 0.5 | Analyze multiple tweets in batch (10 tweets) |
| premium_theme_access | 2.0 | Access to premium tweet themes |
| viral_score_calculation | 0.2 | Calculate viral potential score |
| hashtag_suggestions | 0.1 | Generate relevant hashtags |
| engagement_prediction | 0.3 | Predict engagement metrics |
| content_optimization | 0.4 | Optimize content for virality |

## Testing

After setup, you can test the system by:

1. **Check Policies API**:
   ```bash
   curl -X GET "http://localhost:3000/api/token-usage/policies"
   ```

2. **Test Tweet Generation** (should now deduct 0.5 $COPY):
   - Go to the Copium page
   - Generate a tweet
   - Check your token balance

3. **Test Context Fetch** (should now deduct 0.05 $COPY):
   - Try fetching context from a Twitter URL
   - Check your token balance

## Troubleshooting

If you encounter issues:

1. **Check RLS Policies**: Make sure Row Level Security is properly configured
2. **Verify Functions**: Ensure the database functions were created successfully
3. **Check Logs**: Look at the browser console and server logs for errors
4. **Test Environment**: Make sure `TOKEN_USAGE_ENABLED=true` in your `.env`

## Next Steps

After successful setup:
1. Test the token deduction system
2. Verify balance updates in real-time
3. Check transaction history
4. Test with insufficient balance scenarios
