#!/bin/bash

# <PERSON>ript to install dependencies for improved error handling and logging

echo "Installing dependencies for improved error handling and logging..."

# Install Sonner for toast notifications
bun add sonner

# Install next-themes for theme support
bun add next-themes

# Install Sentry for error monitoring (optional)
# bun add @sentry/nextjs

echo "Dependencies installed successfully!"

# Update the improvement plan to mark completed items
echo "Updating improvement plan..."

# Create a backup of the improvement plan
cp docs/improvement-plan.md docs/improvement-plan.md.bak

# Update the improvement plan with completed items
sed -i '' 's/- \[ \] **Implement structured logging**/- \[x\] **Implement structured logging**/' docs/improvement-plan.md
sed -i '' 's/- \[ \] **Create custom error classes for different types of errors**/- \[x\] **Create custom error classes for different types of errors**/' docs/improvement-plan.md
sed -i '' 's/- \[ \] **Implement consistent error handling patterns across the codebase**/- \[x\] **Implement consistent error handling patterns across the codebase**/' docs/improvement-plan.md
sed -i '' 's/- \[ \] **Add more detailed error messages with actionable information**/- \[x\] **Add more detailed error messages with actionable information**/' docs/improvement-plan.md
sed -i '' 's/- \[ \] **Implement global error boundary for React components**/- \[x\] **Implement global error boundary for React components**/' docs/improvement-plan.md

echo "Improvement plan updated!"

echo "Next steps:"
echo "1. Update the API routes to use the new error handling middleware"
echo "2. Update the UI components to use the toast notifications"
echo "3. Add more tests to ensure the error handling works correctly"
echo "4. Consider adding Sentry for production error monitoring"

echo "Done!"
