#!/bin/bash

# Script to install Web3 dependencies for Copy.AI On-Chain feature

echo "Installing Web3 dependencies for Copy.AI On-Chain feature..."

# Core Web3 libraries
echo "Installing wagmi and viem..."
bun add wagmi viem

# Wallet connection
echo "Installing RainbowKit..."
bun add @rainbow-me/rainbowkit

# IPFS integration
echo "Installing IPFS client..."
bun add ipfs-http-client

# Blockchain utilities
echo "Installing additional blockchain utilities..."
bun add @tanstack/react-query

# Development dependencies
echo "Installing development dependencies..."
bun add -D @types/node

# Environment variables template
echo "Creating environment variables template..."
cat > .env.copium.example << 'EOF'
# Copium On-Chain Configuration
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_walletconnect_project_id
NEXT_PUBLIC_COPY_TOKEN_CONTRACT_ADDRESS=0x...
NEXT_PUBLIC_NFT_CONTRACT_ADDRESS=0x...
NEXT_PUBLIC_STAKING_CONTRACT_ADDRESS=0x...

# IPFS Configuration
IPFS_API_URL=https://ipfs.infura.io:5001
IPFS_API_KEY=your_ipfs_api_key
IPFS_API_SECRET=your_ipfs_api_secret

# Blockchain Configuration
NEXT_PUBLIC_CHAIN_ID=8453  # Base mainnet
NEXT_PUBLIC_RPC_URL=https://mainnet.base.org

# Copium Pricing
COPY_GENERATION_COST=0.5
COPY_STAKING_MINIMUM=100
EOF

echo "Web3 dependencies installed successfully!"
echo ""
echo "Next steps:"
echo "1. Copy .env.copium.example to your .env.local file"
echo "2. Fill in the required environment variables"
echo "3. Set up your WalletConnect project at https://cloud.walletconnect.com/"
echo "4. Deploy or configure your token contracts"
