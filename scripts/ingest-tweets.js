#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to manually trigger tweet ingestion
 * 
 * Usage:
 *   node scripts/ingest-tweets.js --url=http://localhost:3000 --token=your-auth-token
 *   
 * Options:
 *   --url     Base URL of the application (default: http://localhost:3000)
 *   --token   Authentication token (required)
 *   --account Account ID to fetch tweets for (optional, if not provided, fetches for all accounts)
 */

// Parse command line arguments
const args = process.argv.slice(2).reduce((acc, arg) => {
  const [key, value] = arg.split('=');
  acc[key.replace(/^--/, '')] = value;
  return acc;
}, {});

// Default values
const url = args.url || 'http://localhost:3000';
const token = args.token;
const accountId = args.account;

if (!token) {
  console.error('Error: Authentication token is required');
  console.error('Usage: node scripts/ingest-tweets.js --url=http://localhost:3000 --token=your-auth-token [--account=account-id]');
  process.exit(1);
}

async function ingestTweets() {
  try {
    console.log('Starting tweet ingestion...');
    
    const endpoint = accountId 
      ? `${url}/api/twitter/ingest?account_id=${accountId}`
      : `${url}/api/twitter/ingest`;
    
    const method = accountId ? 'GET' : 'POST';
    
    console.log(`Making ${method} request to ${endpoint}`);
    
    const response = await fetch(endpoint, {
      method,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || `Failed with status ${response.status}`);
    }
    
    const data = await response.json();
    console.log('Tweet ingestion completed successfully:');
    console.log(JSON.stringify(data, null, 2));
  } catch (error) {
    console.error('Error ingesting tweets:', error.message);
    process.exit(1);
  }
}

ingestTweets();
