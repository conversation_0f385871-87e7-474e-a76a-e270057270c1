-- BuddyChip Complete Database Setup Script
-- Run this script in your Supabase SQL editor to set up all required tables

-- First, run the main database schema
\i docs/sql/database.sql

-- Then add Copy.AI on-chain features
\i docs/sql/copy-ai-schema.sql

-- Add personality analysis features
\i docs/sql/personality_analysis_schema.sql

-- Add memory/search agent features
\i docs/sql/mem0-schema.sql

-- Add search agent features
\i docs/sql/search-agent-schema.sql

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated, service_role;

-- Grant table permissions (RLS policies will control access)
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO authenticated, service_role;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;

-- Ensure all functions have proper permissions
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated, service_role;

-- Refresh the publication for realtime if needed
-- Uncomment these lines if you want realtime subscriptions
-- BEGIN;
--   DROP PUBLICATION IF EXISTS supabase_realtime;
--   CREATE PUBLICATION supabase_realtime;
-- COMMIT;
-- ALTER PUBLICATION supabase_realtime ADD TABLE 
--   public.buddychip_profiles,
--   public.buddychip_followed_accounts,
--   public.buddychip_categories,
--   public.buddychip_category_keywords,
--   public.buddychip_tweets,
--   public.buddychip_tweet_categories,
--   public.buddychip_user_preferences,
--   public.buddychip_beta_signups,
--   public.buddychip_copy_tokens,
--   public.buddychip_generated_tweets,
--   public.buddychip_tweet_nfts,
--   public.buddychip_leaderboard,
--   public.buddychip_staking,
--   public.buddychip_shill_activities,
--   public.buddychip_token_transactions,
--   public.buddychip_generated_videos;
