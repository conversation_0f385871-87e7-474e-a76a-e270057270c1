#!/bin/bash

echo "🔧 Fixing remaining unused variables with underscore prefix..."

# Fix specific unused variables
sed -i '' 's/export async function PATCH(request:/export async function PATCH(_request:/' src/app/api/twitter/analyzed-tweets/cache/route.ts
sed -i '' 's/export async function PATCH(request:/export async function PATCH(_request:/' src/app/api/twitter/analyzed-tweets/monitor/route.ts
sed -i '' 's/} catch (dbError) {/} catch (_dbError) {/' src/app/api/twitter/analyzed-tweets/monitor/route.ts

# Fix floating chatbot
sed -i '' 's/SyntaxHighlighterProps,/_SyntaxHighlighterProps,/' src/components/ai/floating-chatbot.tsx
sed -i '' 's/const style = /const _style = /' src/components/ai/floating-chatbot.tsx

# Fix copy-ai dashboard
sed -i '' 's/BalanceData,/_BalanceData,/' src/components/copy-ai/copy-ai-dashboard.tsx

# Fix network switcher
sed -i '' 's/isDevnet,/_isDevnet,/' src/components/network-switcher.tsx

# Fix useTokenUsage hook
sed -i '' 's/userId,/_userId,/' src/hooks/useTokenUsage.ts
sed -i '' 's/setAnalytics,/_setAnalytics,/' src/hooks/useTokenUsage.ts
sed -i '' 's/setLoading,/_setLoading,/' src/hooks/useTokenUsage.ts

# Fix monitoring
sed -i '' 's/timeWindow,/_timeWindow,/' src/lib/monitoring/analyzed-tweets-monitor.ts

# Fix solana topup service
sed -i '' 's/CONTRACTS,/_CONTRACTS,/' src/lib/solana/topup-service.ts

# Fix lamp component display name
cat > temp_lamp.tsx << 'EOF'
'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface LampProps {
  className?: string;
}

const Lamp = React.memo(function Lamp({ className }: LampProps) {
  return (
    <div className={cn("relative", className)}>
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full blur-3xl opacity-20 animate-pulse" />
      <div className="relative bg-gradient-to-r from-blue-500 to-purple-500 rounded-full p-8 shadow-2xl">
        <div className="w-16 h-16 bg-white rounded-full flex items-center justify-center">
          <div className="w-8 h-8 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full animate-pulse" />
        </div>
      </div>
    </div>
  );
});

export default Lamp;
EOF

mv temp_lamp.tsx src/components/ui/lamp.tsx

echo "✅ Fixed remaining unused variables"
