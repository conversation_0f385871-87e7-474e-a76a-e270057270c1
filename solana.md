# Solana Migration Plan

## Migration: Base to Solana WalletConnect Integration

### Overview
Migrating the WalletConnect integration from Base blockchain to Solana using WalletConnect's native Solana support and adding $Copy token contract configuration.

### Current State
- ✅ RainbowKit + Wagmi for Base/Ethereum wallet connections
- ✅ Web3 provider configured for Base and Base Sepolia chains
- ✅ Token service implemented for Base blockchain
- ✅ WalletConnect integration in navbar and components

### Key Discovery
- ✅ WalletConnect now supports Solana natively
- ✅ Can use `@solana/wallet-adapter-walletconnect` for integration
- ✅ Keep existing WalletConnect infrastructure, just switch chains

---

## Updated Implementation Plan

### Phase 1: Install Solana Dependencies
- [x] Remove Base/Ethereum dependencies
  - [x] Remove `@rainbow-me/rainbowkit`
  - [x] Remove `wagmi` and `viem`
  - [x] Remove `ipfs-http-client` (if not needed)
- [x] Install Solana wallet adapter dependencies
  - [x] `@solana/wallet-adapter-base`
  - [x] `@solana/wallet-adapter-react`
  - [x] `@solana/wallet-adapter-react-ui`
  - [x] `@solana/wallet-adapter-wallets`
  - [x] `@solana/wallet-adapter-walletconnect`
  - [x] `@solana/web3.js`
  - [x] `@solana/spl-token`

### Phase 2: Update Configuration Files
- [x] Update `src/lib/web3/config.ts`
  - [x] Replace Base chain config with Solana network config
  - [x] Add Solana RPC endpoints (mainnet, devnet, testnet)
  - [x] Update contract addresses for Solana
  - [x] Add $Copy token mint address (mockup)
- [x] Update environment variables in `.env`
  - [x] Add Solana RPC URLs
  - [x] Add $Copy token mint address
  - [x] Keep WalletConnect project ID (reuse existing)

### Phase 3: Replace Web3 Provider
- [x] Update `src/components/web3-provider.tsx`
  - [x] Replace RainbowKit provider with Solana wallet adapter provider
  - [x] Configure supported wallets (Phantom, Solflare, WalletConnect, etc.)
  - [x] Set up connection provider and wallet provider

### Phase 4: Update Wallet Connect Component
- [x] Update `src/components/wallet-connect-button.tsx`
  - [x] Replace RainbowKit ConnectButton with Solana wallet adapter
  - [x] Update UI to show Solana wallet connection state
  - [x] Handle Solana-specific wallet interactions

### Phase 5: Update Token Service
- [x] Update `src/lib/web3/token-service.ts`
  - [x] Add Solana imports and utilities
  - [x] Add on-chain balance checking for Solana
  - [x] Add wallet address management
  - [x] Add balance sync functionality

### Phase 6: Update Layout and Integration
- [x] Update `src/components/layout-wrapper.tsx`
  - [x] Web3Provider already correctly imported and used
  - [x] Solana provider wraps the application correctly
- [x] Update navbar integration
  - [x] Wallet connect button works with new Solana integration

### Phase 7: Environment Configuration
- [x] Add $Copy token contract to `.env`
  - [x] Add mockup Solana mint address for $Copy token
  - [x] Add Solana network configurations
  - [x] Update documentation

### Phase 8: Create New Solana Utilities
- [x] Create `src/lib/solana/` directory
- [x] Create `src/lib/solana/connection.ts` - Solana connection management
- [x] Create `src/lib/solana/token-utils.ts` - SPL token utilities

---

## Files to Modify
- `package.json` - Update dependencies
- `src/lib/web3/config.ts` - Solana configuration
- `src/components/web3-provider.tsx` - Solana provider setup
- `src/components/wallet-connect-button.tsx` - Solana wallet UI
- `src/lib/web3/token-service.ts` - Solana token operations
- `src/components/layout-wrapper.tsx` - Provider integration
- `.env` - Add Solana and $Copy token configuration

## New Files to Create
- `src/lib/solana/connection.ts`
- `src/lib/solana/token-utils.ts`

---

## Notes
- Using mockup address for $Copy token: `CopyAiTokenMint1111111111111111111111111111`
- Will maintain backward compatibility during transition
- All console logging will be added for debugging
- Focus on clean, minimal code solutions
