# BuddyChip - Twitter Assistant

BuddyChip is a Next.js application that helps you collect, filter, and respond to tweets from accounts you follow. It uses Supabase for authentication and database storage, and provides AI-powered assistance for drafting responses to tweets.

## Features

- **User Authentication**: Secure login and signup using Supabase Auth
- **Twitter Account Management**: Add and remove Twitter accounts to follow
- **Tweet Collection**: Automatically collect tweets from followed accounts
- **Tweet Filtering**: Filter tweets by sentiment, importance, and relevance
- **AI-Powered Tweet Analysis**: Automatically evaluate which tweets are worth replying to
- **Personalized Reply Suggestions**: Get AI-generated reply suggestions based on your style
- **Customizable AI Preferences**: Set your preferred reply style, interests, and tone
- **Robust Error Handling**: Comprehensive error handling with custom error classes
- **Structured Logging**: Detailed logging with contextual information
- **Request Validation**: Input validation using Zod schemas
- **UI Error Boundaries**: Graceful error recovery in the UI

## Getting Started

### Prerequisites

- Node.js 18+ and Bun
- A Supabase account
- (Optional) Twitter API credentials

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/buddychip.git
   cd buddychip
   ```

2. Install dependencies:
   ```bash
   bun install
   ```

3. Set up environment variables:
   Create a `.env` file in the root directory with the following variables:
   ```
   NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key
   TWITTER_BEARER_TOKEN=your-twitter-bearer-token
   OPENROUTER_API_KEY=your-openrouter-api-key
   NEXT_PUBLIC_APP_URL=your-app-url
   ```

   > **Note**: The `TWITTER_BEARER_TOKEN` is required for fetching real tweets from Twitter. If not provided, the application will use mock data for development purposes. See `docs/twitter-api-setup.md` for detailed instructions on setting up Twitter API access.

   > **Note**: The `OPENROUTER_API_KEY` is required for AI-powered tweet analysis and reply generation. You can get an API key from [OpenRouter](https://openrouter.ai/).

4. Set up the Supabase database:
   Follow the instructions in `docs/supabase-setup.md` to create the necessary tables and policies.

5. Start the development server:
   ```bash
   bun dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

- `src/app`: Next.js app router pages and API routes
- `src/components`: React components
- `src/lib`: Utility functions and libraries
- `docs`: Documentation and database schema

## Database Setup

The application requires a Supabase database with specific tables and policies. See `docs/supabase-setup.md` for detailed setup instructions and `docs/database.sql` for the complete schema.

## Twitter API Integration

BuddyChip uses the Twitter API to fetch tweets from accounts that users follow. The integration includes:

- Fetching user details from Twitter handles
- Retrieving tweets with pagination support
- Rate limiting to comply with Twitter API policies
- Fallback to mock data for development

For detailed setup instructions, see `docs/twitter-api-setup.md`.

### Scheduling Tweet Ingestion

For regular tweet ingestion, you can set up a scheduled job using:

```javascript
import { scheduleTweetIngestion } from './lib/utils/schedule-tweet-ingestion';

// Example: Run this function from a cron job or serverless function
async function runTweetIngestion() {
  await scheduleTweetIngestion('https://yourdomain.com', 'your-auth-token');
}
```

## AI Integration

BuddyChip uses AI to analyze tweets and generate personalized reply suggestions:

- **Tweet Analysis**: Automatically evaluates which tweets are worth replying to
- **Reply Generation**: Creates personalized reply suggestions based on user preferences
- **User Preferences**: Allows users to customize how the AI analyzes and responds to tweets

The AI integration uses Google's Gemini 2.5 Flash Preview model through OpenRouter.

For detailed setup instructions, see `docs/ai-integration.md`.

## Code Quality & Reliability

BuddyChip includes several features to ensure code quality and reliability:

### Error Handling

- **Custom Error Classes**: Specific error types for different scenarios
- **Consistent Error Handling**: Middleware for API routes
- **Error Boundaries**: React components for graceful UI error recovery

```typescript
// Example: Using the error handling middleware
export const GET = withErrorHandling(async (request) => {
  // Your code here - errors will be handled consistently
});
```

### Logging

- **Structured Logging**: JSON-formatted logs with contextual information
- **Log Levels**: Debug, info, warning, and error levels
- **Component-specific Logging**: Create loggers for specific components

```typescript
// Example: Using the logger
import { logger, createLogger } from '@/lib/utils/logger';

// Component-specific logger
const componentLogger = createLogger({ component: 'my-component' });
componentLogger.info('Operation started', { userId: 'user-123' });
```

### Validation

- **Schema Validation**: Zod schemas for request validation
- **Consistent Error Messages**: Standardized validation error responses
- **Type Safety**: TypeScript integration with validation schemas

```typescript
// Example: Validating request data
import { validateData, schemas } from '@/lib/utils/validation';

const { tweet_id } = validateData(schemas.tweetId, requestBody);
```

For more details on these features, see `docs/improvement-plan.md` and `docs/progress-summary.md`.

## License

This project is licensed under the MIT License.
